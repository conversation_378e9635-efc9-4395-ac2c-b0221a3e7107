'use client';

import React, { useState, useEffect } from 'react';
import { AdminCard, AdminStatCard } from '@/components/admin/ui/AdminCard';
import AdminButton from '@/components/admin/ui/AdminButton';
import { AdminTableSkeleton } from '@/components/admin/ui/AdminLoading';
import { useAdminAuth, ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';
import { AdminPermissionWrapper } from '@/components/admin/auth/AdminProtectedRoute';
import { adminLeads } from '@/lib/supabase/admin';
import { LeadFollowUp } from '@/lib/types/admin';
import {
  Calendar,
  Clock,
  AlertTriangle,
  CheckCircle,
  User,
  Building,
  Mail,
  Phone,
  MessageSquare,
  Star,
  Filter,
  RefreshCw,
} from 'lucide-react';

export default function FollowUpDashboard() {
  const { hasPermission } = useAdminAuth();
  const [followUps, setFollowUps] = useState<LeadFollowUp[]>([]);
  const [loading, setLoading] = useState(true);
  const [filterType, setFilterType] = useState<string>('all');

  // Fetch follow-ups requiring attention
  const fetchFollowUps = async () => {
    try {
      setLoading(true);
      const data = await adminLeads.getLeadsRequiringFollowup();
      setFollowUps(data);
    } catch (error) {
      console.error('Error fetching follow-ups:', error);
      // Mock data for development
      const mockFollowUps: LeadFollowUp[] = [
        {
          id: '1',
          fullName: 'John Smith',
          email: '<EMAIL>',
          companyName: 'Tech Innovations Inc.',
          projectType: 'Web Development',
          status: 'contacted',
          priority: 'high',
          leadScore: 85,
          nextFollowUpDate: '2024-01-20',
          daysOverdue: 2,
          assignedToName: 'Admin User',
        },
        {
          id: '2',
          fullName: 'Sarah Johnson',
          email: '<EMAIL>',
          companyName: 'Creative Studio LLC',
          projectType: 'Mobile App',
          status: 'qualified',
          priority: 'medium',
          leadScore: 72,
          nextFollowUpDate: '2024-01-22',
          daysOverdue: 0,
          assignedToName: 'Admin User',
        },
        {
          id: '3',
          fullName: 'Mike Wilson',
          email: '<EMAIL>',
          companyName: 'Startup Inc.',
          projectType: 'E-commerce',
          status: 'proposal_sent',
          priority: 'urgent',
          leadScore: 91,
          nextFollowUpDate: '2024-01-18',
          daysOverdue: 4,
          assignedToName: 'Admin User',
        },
      ];
      setFollowUps(mockFollowUps);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFollowUps();
  }, []);

  const getStatusBadge = (status: string) => {
    const badges = {
      new: { bg: 'bg-blue-100', text: 'text-blue-800', icon: <AlertTriangle className="h-3 w-3" /> },
      contacted: { bg: 'bg-yellow-100', text: 'text-yellow-800', icon: <MessageSquare className="h-3 w-3" /> },
      qualified: { bg: 'bg-green-100', text: 'text-green-800', icon: <CheckCircle className="h-3 w-3" /> },
      proposal_sent: { bg: 'bg-purple-100', text: 'text-purple-800', icon: <Mail className="h-3 w-3" /> },
      negotiating: { bg: 'bg-orange-100', text: 'text-orange-800', icon: <MessageSquare className="h-3 w-3" /> },
      won: { bg: 'bg-green-100', text: 'text-green-800', icon: <CheckCircle className="h-3 w-3" /> },
      lost: { bg: 'bg-red-100', text: 'text-red-800', icon: <AlertTriangle className="h-3 w-3" /> },
      on_hold: { bg: 'bg-gray-100', text: 'text-gray-800', icon: <Clock className="h-3 w-3" /> },
    };
    return badges[status as keyof typeof badges] || badges.new;
  };

  const getPriorityBadge = (priority: string) => {
    const badges = {
      low: 'bg-gray-100 text-gray-800',
      medium: 'bg-blue-100 text-blue-800',
      high: 'bg-orange-100 text-orange-800',
      urgent: 'bg-red-100 text-red-800',
    };
    return badges[priority as keyof typeof badges] || badges.medium;
  };

  const getLeadScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    if (score >= 40) return 'text-orange-600';
    return 'text-red-600';
  };

  const getOverdueColor = (days: number) => {
    if (days === 0) return 'text-green-600';
    if (days <= 2) return 'text-yellow-600';
    if (days <= 5) return 'text-orange-600';
    return 'text-red-600';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  };

  // Calculate stats
  const totalFollowUps = followUps.length;
  const overdueFollowUps = followUps.filter(f => f.daysOverdue > 0).length;
  const todayFollowUps = followUps.filter(f => {
    const today = new Date().toISOString().split('T')[0];
    return f.nextFollowUpDate === today;
  }).length;
  const highPriorityFollowUps = followUps.filter(f => f.priority === 'high' || f.priority === 'urgent').length;

  // Filter follow-ups
  const filteredFollowUps = followUps.filter(followUp => {
    if (filterType === 'overdue') return followUp.daysOverdue > 0;
    if (filterType === 'today') {
      const today = new Date().toISOString().split('T')[0];
      return followUp.nextFollowUpDate === today;
    }
    if (filterType === 'high-priority') return followUp.priority === 'high' || followUp.priority === 'urgent';
    return true;
  });

  if (loading) {
    return <AdminTableSkeleton rows={4} columns={5} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 font-jost">Follow-up Dashboard</h1>
          <p className="text-gray-600 font-jost">Track and manage your lead follow-up schedule</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <AdminButton 
            variant="outline" 
            onClick={fetchFollowUps}
            icon={<RefreshCw className="h-4 w-4" />}
          >
            Refresh
          </AdminButton>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <AdminStatCard
          title="Total Follow-ups"
          value={totalFollowUps.toString()}
          icon={<Calendar className="h-6 w-6 text-gray-600" />}
        />
        <AdminStatCard
          title="Overdue"
          value={overdueFollowUps.toString()}
          change={{ value: overdueFollowUps > 0 ? 'Action needed' : 'All current', type: overdueFollowUps > 0 ? 'negative' : 'positive' }}
          icon={<AlertTriangle className="h-6 w-6 text-gray-600" />}
        />
        <AdminStatCard
          title="Due Today"
          value={todayFollowUps.toString()}
          icon={<Clock className="h-6 w-6 text-gray-600" />}
        />
        <AdminStatCard
          title="High Priority"
          value={highPriorityFollowUps.toString()}
          icon={<Star className="h-6 w-6 text-gray-600" />}
        />
      </div>

      {/* Filters */}
      <AdminCard>
        <div className="flex items-center space-x-4">
          <Filter className="h-5 w-5 text-gray-400" />
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setFilterType('all')}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                filterType === 'all' 
                  ? 'bg-blue-100 text-blue-800' 
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              All ({totalFollowUps})
            </button>
            <button
              onClick={() => setFilterType('overdue')}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                filterType === 'overdue' 
                  ? 'bg-red-100 text-red-800' 
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              Overdue ({overdueFollowUps})
            </button>
            <button
              onClick={() => setFilterType('today')}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                filterType === 'today' 
                  ? 'bg-yellow-100 text-yellow-800' 
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              Today ({todayFollowUps})
            </button>
            <button
              onClick={() => setFilterType('high-priority')}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                filterType === 'high-priority' 
                  ? 'bg-orange-100 text-orange-800' 
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              High Priority ({highPriorityFollowUps})
            </button>
          </div>
        </div>
      </AdminCard>

      {/* Follow-ups List */}
      {filteredFollowUps.length > 0 ? (
        <div className="space-y-4">
          {filteredFollowUps.map((followUp) => {
            const statusBadge = getStatusBadge(followUp.status);
            
            return (
              <AdminCard key={followUp.id} className="hover:shadow-md transition-shadow duration-200">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-start space-x-4">
                        <div className="h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center">
                          <User className="h-5 w-5 text-gray-600" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 font-jost">
                            {followUp.fullName}
                          </h3>
                          <div className="flex items-center space-x-4 text-sm text-gray-600 font-jost">
                            <span className="flex items-center">
                              <Mail className="h-4 w-4 mr-1" />
                              {followUp.email}
                            </span>
                            {followUp.companyName && (
                              <span className="flex items-center">
                                <Building className="h-4 w-4 mr-1" />
                                {followUp.companyName}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        {/* Lead Score */}
                        <div className="text-center">
                          <div className={`text-lg font-bold ${getLeadScoreColor(followUp.leadScore)}`}>
                            {followUp.leadScore}
                          </div>
                          <div className="text-xs text-gray-500 font-jost">Score</div>
                        </div>

                        {/* Status Badge */}
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusBadge.bg} ${statusBadge.text}`}>
                          {statusBadge.icon}
                          <span className="ml-1">{followUp.status.replace('_', ' ')}</span>
                        </span>

                        {/* Priority Badge */}
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityBadge(followUp.priority)}`}>
                          {followUp.priority}
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <p className="text-sm font-medium text-gray-900 font-jost">Project Type</p>
                        <p className="text-sm text-gray-600 font-jost">{followUp.projectType}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900 font-jost">Next Follow-up</p>
                        <p className="text-sm text-gray-600 font-jost">
                          {followUp.nextFollowUpDate ? formatDate(followUp.nextFollowUpDate) : 'Not scheduled'}
                          {followUp.daysOverdue > 0 && (
                            <span className={`ml-2 font-medium ${getOverdueColor(followUp.daysOverdue)}`}>
                              ({followUp.daysOverdue} days overdue)
                            </span>
                          )}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900 font-jost">Assigned To</p>
                        <p className="text-sm text-gray-600 font-jost">{followUp.assignedToName}</p>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2 ml-4">
                    <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.LEADS_EDIT}>
                      <AdminButton size="sm" variant="outline">
                        Contact
                      </AdminButton>
                      <AdminButton size="sm">
                        Update
                      </AdminButton>
                    </AdminPermissionWrapper>
                  </div>
                </div>
              </AdminCard>
            );
          })}
        </div>
      ) : (
        <AdminCard className="text-center py-12">
          <div className="mx-auto h-16 w-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <Calendar className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2 font-jost">
            {filterType === 'all' ? 'No follow-ups scheduled' : `No ${filterType.replace('-', ' ')} follow-ups`}
          </h3>
          <p className="text-gray-600 font-jost">
            {filterType === 'all' 
              ? 'All your leads are up to date!' 
              : 'Try selecting a different filter to see more results.'}
          </p>
        </AdminCard>
      )}
    </div>
  );
}

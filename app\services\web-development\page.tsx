'use client';

import React, { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import Link from 'next/link';
import {
  ArrowRight,
  Code,
  Zap,
  Shield,
  Smartphone,
  Search,
  BarChart3,
  CheckCircle,
  Clock,
  Users,
  Star,
  Quote,
  ChevronDown,
  ChevronUp,
  TrendingUp
} from 'lucide-react';

interface Feature {
  title: string;
  description: string;
  icon: React.ReactNode;
}

interface Package {
  name: string;
  price: string;
  description: string;
  features: string[];
  timeline: string;
  popular?: boolean;
}

interface CaseStudy {
  title: string;
  client: string;
  challenge: string;
  solution: string;
  results: string[];
  image: string;
}

export default function WebDevelopmentPage() {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  const features: Feature[] = [
    {
      title: 'Lightning Fast Performance',
      description: 'Built with Next.js 14+ and optimized for Core Web Vitals, ensuring your website loads in under 2 seconds.',
      icon: <Zap className="w-6 h-6" />
    },
    {
      title: 'Mobile-First Design',
      description: 'Responsive design that looks perfect on all devices, from smartphones to desktop computers.',
      icon: <Smartphone className="w-6 h-6" />
    },
    {
      title: 'SEO Optimized',
      description: 'Built-in SEO best practices to help your website rank higher in search engine results.',
      icon: <Search className="w-6 h-6" />
    },
    {
      title: 'Enterprise Security',
      description: 'Advanced security measures including SSL, data encryption, and regular security audits.',
      icon: <Shield className="w-6 h-6" />
    },
    {
      title: 'Analytics Ready',
      description: 'Integrated analytics and tracking to measure performance and user behavior.',
      icon: <BarChart3 className="w-6 h-6" />
    },
    {
      title: 'Scalable Architecture',
      description: 'Built to grow with your business, handling increased traffic and functionality.',
      icon: <Code className="w-6 h-6" />
    }
  ];

  const packages: Package[] = [
    {
      name: 'Starter Website',
      price: '$5,000',
      description: 'Perfect for small businesses and startups looking to establish their online presence.',
      features: [
        'Up to 5 pages',
        'Responsive design',
        'Contact form',
        'Basic SEO setup',
        'SSL certificate',
        '30 days support'
      ],
      timeline: '2-3 weeks'
    },
    {
      name: 'Business Website',
      price: '$10,000',
      description: 'Comprehensive solution for growing businesses with advanced features and functionality.',
      features: [
        'Up to 15 pages',
        'Custom design',
        'CMS integration',
        'Advanced SEO',
        'Analytics setup',
        'Blog functionality',
        'Social media integration',
        '60 days support'
      ],
      timeline: '4-6 weeks',
      popular: true
    },
    {
      name: 'Enterprise Solution',
      price: '$25,000+',
      description: 'Custom web applications with advanced functionality for large organizations.',
      features: [
        'Unlimited pages',
        'Custom functionality',
        'Database integration',
        'User authentication',
        'API development',
        'Performance optimization',
        'Security audit',
        '90 days support'
      ],
      timeline: '8-12 weeks'
    }
  ];

  const caseStudies: CaseStudy[] = [
    {
      title: 'E-commerce Platform Transformation',
      client: 'RetailMax Inc.',
      challenge: 'Outdated website with poor mobile experience and slow loading times affecting sales.',
      solution: 'Complete redesign with Next.js, optimized checkout process, and mobile-first approach.',
      results: [
        '180% increase in mobile conversions',
        '65% faster page load times',
        '40% reduction in cart abandonment',
        '$2.3M additional revenue in first year'
      ],
      image: '/placeholder-case-study.jpg'
    }
  ];

  const faqs = [
    {
      question: 'What technologies do you use for web development?',
      answer: 'We specialize in modern technologies including Next.js 14+, React, TypeScript, Tailwind CSS, and Node.js. We choose the best technology stack based on your specific requirements and goals.'
    },
    {
      question: 'How long does it take to build a website?',
      answer: 'Timeline depends on complexity. Simple websites take 2-4 weeks, business websites take 4-8 weeks, and complex web applications can take 8-16 weeks. We provide detailed timelines during consultation.'
    },
    {
      question: 'Do you provide ongoing maintenance and support?',
      answer: 'Yes! We offer comprehensive maintenance packages including security updates, performance monitoring, content updates, and technical support. All projects include initial support period.'
    },
    {
      question: 'Will my website be mobile-friendly?',
      answer: 'Absolutely! All our websites are built with a mobile-first approach, ensuring they look and function perfectly on all devices and screen sizes.'
    },
    {
      question: 'Can you help with SEO and digital marketing?',
      answer: 'Yes, we provide comprehensive SEO optimization as part of our web development service, and we also offer digital marketing services to help drive traffic and conversions.'
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden bg-gradient-to-br from-blue-50 to-white">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="inline-flex items-center bg-blue-600 text-white px-4 py-2 rounded-full text-sm mb-6 font-jost">
                <Code className="w-4 h-4 mr-2" />
                Web Development Services
              </div>
              
              <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-black mb-6 leading-tight font-jost">
                Custom Web Development That 
                <span className="text-blue-600"> Drives Results</span>
              </h1>
              
              <p className="text-xl text-gray-600 mb-8 leading-relaxed font-jost">
                We build fast, secure, and scalable web applications using cutting-edge technologies like Next.js, React, and TypeScript. 
                From simple websites to complex web applications, we deliver solutions that grow your business.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <Link
                  href="/get-quote"
                  className="bg-black text-white px-8 py-4 rounded-full hover:bg-gray-800 transition-all duration-300 flex items-center justify-center gap-2 font-medium font-jost group"
                >
                  Get Free Quote
                  <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
                </Link>
                <Link
                  href="/project"
                  className="border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-full hover:border-gray-400 hover:bg-gray-50 transition-all duration-300 font-medium font-jost"
                >
                  View Portfolio
                </Link>
              </div>
              
              <div className="flex items-center gap-8 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  <span className="font-jost">2-12 weeks delivery</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  <span className="font-jost">Dedicated team</span>
                </div>
              </div>
            </div>
            
            <div className="relative">
              <div className="bg-white p-8 rounded-2xl shadow-2xl border border-gray-100">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  </div>
                  <div className="bg-gray-100 h-4 rounded w-3/4"></div>
                  <div className="bg-gray-100 h-4 rounded w-1/2"></div>
                  <div className="bg-blue-100 h-20 rounded"></div>
                  <div className="flex gap-2">
                    <div className="bg-gray-100 h-4 rounded flex-1"></div>
                    <div className="bg-gray-100 h-4 rounded flex-1"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              Why Choose Our Web Development?
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto font-jost">
              We don't just build websites – we create digital experiences that convert visitors into customers
              and help your business grow online.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group"
              >
                <div className="flex items-center justify-center w-12 h-12 bg-blue-600 text-white rounded-xl mb-6 group-hover:scale-110 transition-transform duration-300">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold text-black mb-4 font-jost">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed font-jost">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Packages Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              Web Development Packages
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto font-jost">
              Choose the perfect package for your business needs. All packages include our quality guarantee
              and ongoing support.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {packages.map((pkg, index) => (
              <div
                key={index}
                className={`bg-white p-8 rounded-2xl shadow-lg border-2 transition-all duration-300 hover:shadow-xl hover:-translate-y-1 relative ${
                  pkg.popular ? 'border-blue-600' : 'border-gray-100'
                }`}
              >
                {pkg.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-medium font-jost">
                      Most Popular
                    </div>
                  </div>
                )}

                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-black mb-2 font-jost">
                    {pkg.name}
                  </h3>
                  <div className="text-4xl font-bold text-black mb-2 font-jost">
                    {pkg.price}
                  </div>
                  <p className="text-gray-600 font-jost">
                    {pkg.description}
                  </p>
                </div>

                <ul className="space-y-3 mb-8">
                  {pkg.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center text-gray-600 font-jost">
                      <CheckCircle className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>

                <div className="text-center mb-6">
                  <div className="text-sm text-gray-500 font-jost">Timeline: {pkg.timeline}</div>
                </div>

                <Link
                  href="/get-quote"
                  className={`w-full py-3 px-6 rounded-full transition-all duration-300 flex items-center justify-center gap-2 font-medium font-jost ${
                    pkg.popular
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-black text-white hover:bg-gray-800'
                  }`}
                >
                  Get Started
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-600 mb-4 font-jost">
              Need a custom solution? We create tailored packages for unique requirements.
            </p>
            <Link
              href="/contact-us"
              className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium font-jost group"
            >
              Discuss Custom Requirements
              <ArrowRight className="w-4 h-4 ml-1 transition-transform duration-300 group-hover:translate-x-1" />
            </Link>
          </div>
        </div>
      </section>

      {/* Case Study Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              Success Stories
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto font-jost">
              See how our web development solutions have transformed businesses and delivered measurable results.
            </p>
          </div>

          {caseStudies.map((study, index) => (
            <div key={index} className="bg-gray-50 rounded-2xl p-8 lg:p-12">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-3xl font-bold text-black mb-4 font-jost">
                    {study.title}
                  </h3>
                  <div className="text-blue-600 font-semibold mb-6 font-jost">
                    Client: {study.client}
                  </div>

                  <div className="space-y-6">
                    <div>
                      <h4 className="text-lg font-semibold text-black mb-2 font-jost">Challenge</h4>
                      <p className="text-gray-600 font-jost">{study.challenge}</p>
                    </div>

                    <div>
                      <h4 className="text-lg font-semibold text-black mb-2 font-jost">Solution</h4>
                      <p className="text-gray-600 font-jost">{study.solution}</p>
                    </div>

                    <div>
                      <h4 className="text-lg font-semibold text-black mb-3 font-jost">Results</h4>
                      <ul className="space-y-2">
                        {study.results.map((result, idx) => (
                          <li key={idx} className="flex items-center text-gray-600 font-jost">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                            {result}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-2xl shadow-lg">
                  <div className="space-y-4">
                    {/* Before/After Comparison */}
                    <div className="text-center mb-4">
                      <h4 className="text-lg font-semibold text-black mb-2 font-jost">Performance Transformation</h4>
                      <p className="text-sm text-gray-600 font-jost">Before vs After Optimization</p>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      {/* Before */}
                      <div className="space-y-3">
                        <div className="text-center">
                          <span className="text-xs font-medium text-red-600 bg-red-50 px-2 py-1 rounded-full font-jost">Before</span>
                        </div>
                        <div className="bg-red-50 p-4 rounded-lg space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-gray-600 font-jost">Load Time</span>
                            <span className="text-sm font-bold text-red-600 font-jost">8.2s</span>
                          </div>
                          <div className="w-full bg-red-200 h-2 rounded-full">
                            <div className="bg-red-600 h-2 rounded-full w-4/5"></div>
                          </div>

                          <div className="flex items-center justify-between">
                            <span className="text-xs text-gray-600 font-jost">Mobile Score</span>
                            <span className="text-sm font-bold text-red-600 font-jost">32/100</span>
                          </div>
                          <div className="w-full bg-red-200 h-2 rounded-full">
                            <div className="bg-red-600 h-2 rounded-full w-1/3"></div>
                          </div>

                          <div className="flex items-center justify-between">
                            <span className="text-xs text-gray-600 font-jost">Conversion</span>
                            <span className="text-sm font-bold text-red-600 font-jost">1.2%</span>
                          </div>
                          <div className="w-full bg-red-200 h-2 rounded-full">
                            <div className="bg-red-600 h-2 rounded-full w-1/4"></div>
                          </div>
                        </div>
                      </div>

                      {/* After */}
                      <div className="space-y-3">
                        <div className="text-center">
                          <span className="text-xs font-medium text-green-600 bg-green-50 px-2 py-1 rounded-full font-jost">After</span>
                        </div>
                        <div className="bg-green-50 p-4 rounded-lg space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-gray-600 font-jost">Load Time</span>
                            <span className="text-sm font-bold text-green-600 font-jost">1.8s</span>
                          </div>
                          <div className="w-full bg-green-200 h-2 rounded-full">
                            <div className="bg-green-600 h-2 rounded-full w-1/5"></div>
                          </div>

                          <div className="flex items-center justify-between">
                            <span className="text-xs text-gray-600 font-jost">Mobile Score</span>
                            <span className="text-sm font-bold text-green-600 font-jost">94/100</span>
                          </div>
                          <div className="w-full bg-green-200 h-2 rounded-full">
                            <div className="bg-green-600 h-2 rounded-full w-11/12"></div>
                          </div>

                          <div className="flex items-center justify-between">
                            <span className="text-xs text-gray-600 font-jost">Conversion</span>
                            <span className="text-sm font-bold text-green-600 font-jost">3.4%</span>
                          </div>
                          <div className="w-full bg-green-200 h-2 rounded-full">
                            <div className="bg-green-600 h-2 rounded-full w-3/4"></div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Key Improvements */}
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h5 className="text-sm font-semibold text-blue-800 mb-2 font-jost">Key Improvements</h5>
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div className="flex items-center gap-1">
                          <Zap className="w-3 h-3 text-blue-600" />
                          <span className="text-blue-700 font-jost">78% faster loading</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Smartphone className="w-3 h-3 text-blue-600" />
                          <span className="text-blue-700 font-jost">Mobile optimized</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <TrendingUp className="w-3 h-3 text-blue-600" />
                          <span className="text-blue-700 font-jost">183% more conversions</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Shield className="w-3 h-3 text-blue-600" />
                          <span className="text-blue-700 font-jost">Enhanced security</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-gray-600 font-jost">
              Get answers to common questions about our web development services.
            </p>
          </div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div
                key={index}
                className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"
              >
                <button
                  onClick={() => setOpenFAQ(openFAQ === index ? null : index)}
                  className="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
                >
                  <h3 className="text-lg font-semibold text-black font-jost pr-4">
                    {faq.question}
                  </h3>
                  {openFAQ === index ? (
                    <ChevronUp className="w-5 h-5 text-gray-500 flex-shrink-0" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-gray-500 flex-shrink-0" />
                  )}
                </button>

                {openFAQ === index && (
                  <div className="px-8 pb-6">
                    <p className="text-gray-600 leading-relaxed font-jost">
                      {faq.answer}
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl sm:text-5xl font-bold text-white mb-6 font-jost">
            Ready to Build Your Dream Website?
          </h2>
          <p className="text-xl text-blue-100 mb-8 leading-relaxed font-jost">
            Let's create a web solution that drives results for your business.
            Get started with a free consultation and detailed project quote.
          </p>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-8">
            <Link
              href="/get-quote"
              className="bg-white text-blue-600 px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 flex items-center gap-2 font-medium font-jost group"
            >
              Get Free Quote & Consultation
              <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
            </Link>
            <Link
              href="/contact-us"
              className="border-2 border-white text-white px-8 py-4 rounded-full hover:bg-white hover:text-blue-600 transition-all duration-300 font-medium font-jost"
            >
              Schedule a Call
            </Link>
          </div>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-blue-100">
            <div className="flex items-center gap-2">
              <Clock className="w-5 h-5" />
              <span className="font-jost">Free consultation included</span>
            </div>
            <div className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              <span className="font-jost">100% satisfaction guarantee</span>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

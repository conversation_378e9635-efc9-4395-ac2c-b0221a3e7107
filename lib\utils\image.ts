/**
 * Image optimization utilities for the template showcase
 */

// Generate a simple blur data URL for placeholder
export const generateBlurDataURL = (width: number = 10, height: number = 10): string => {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  
  const ctx = canvas.getContext('2d');
  if (!ctx) return '';
  
  // Create a simple gradient
  const gradient = ctx.createLinearGradient(0, 0, width, height);
  gradient.addColorStop(0, '#f3f4f6');
  gradient.addColorStop(1, '#e5e7eb');
  
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, width, height);
  
  return canvas.toDataURL('image/jpeg', 0.1);
};

// Static blur data URL for better performance
export const BLUR_DATA_URL = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==';

// Image size configurations for different breakpoints
export const IMAGE_SIZES = {
  card: {
    mobile: 400,
    tablet: 600,
    desktop: 800,
  },
  gallery: {
    thumbnail: 80,
    preview: 1200,
    fullscreen: 1920,
  },
  hero: {
    mobile: 800,
    tablet: 1200,
    desktop: 1600,
  },
};

// Generate responsive image sizes string
export const generateImageSizes = (type: keyof typeof IMAGE_SIZES): string => {
  const sizes = IMAGE_SIZES[type];
  
  if ('mobile' in sizes) {
    return `(max-width: 768px) ${sizes.mobile}px, (max-width: 1200px) ${sizes.tablet}px, ${sizes.desktop}px`;
  }
  
  return '100vw';
};

// Check if image URL is valid
export const isValidImageUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// Preload critical images
export const preloadImage = (src: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
  });
};

// Lazy load images with Intersection Observer
export const createImageObserver = (callback: (entry: IntersectionObserverEntry) => void) => {
  if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
    return null;
  }

  return new IntersectionObserver(
    (entries) => {
      entries.forEach(callback);
    },
    {
      rootMargin: '50px 0px',
      threshold: 0.1,
    }
  );
};

// Generate placeholder image URLs (for development/demo purposes)
export const generatePlaceholderUrl = (
  width: number,
  height: number,
  text?: string,
  bgColor: string = 'f3f4f6',
  textColor: string = '6b7280'
): string => {
  const displayText = text || `${width}×${height}`;
  return `https://via.placeholder.com/${width}x${height}/${bgColor}/${textColor}?text=${encodeURIComponent(displayText)}`;
};

// Template-specific placeholder URLs
export const TEMPLATE_PLACEHOLDERS = {
  ecommerce: generatePlaceholderUrl(800, 600, 'E-commerce Template'),
  portfolio: generatePlaceholderUrl(800, 600, 'Portfolio Template'),
  saas: generatePlaceholderUrl(800, 600, 'SaaS Template'),
  business: generatePlaceholderUrl(800, 600, 'Business Template'),
  blog: generatePlaceholderUrl(800, 600, 'Blog Template'),
  dashboard: generatePlaceholderUrl(800, 600, 'Dashboard Template'),
};

// Image optimization for Next.js
export const getOptimizedImageProps = (
  src: string,
  alt: string,
  type: keyof typeof IMAGE_SIZES = 'card',
  priority: boolean = false
) => {
  return {
    src,
    alt,
    sizes: generateImageSizes(type),
    placeholder: 'blur' as const,
    blurDataURL: BLUR_DATA_URL,
    priority,
    quality: 85,
  };
};

// Cache image in browser
export const cacheImage = (src: string): void => {
  if (typeof window !== 'undefined') {
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = src;
    document.head.appendChild(link);
  }
};

// Batch preload multiple images
export const preloadImages = async (urls: string[]): Promise<void[]> => {
  return Promise.all(urls.map(preloadImage));
};

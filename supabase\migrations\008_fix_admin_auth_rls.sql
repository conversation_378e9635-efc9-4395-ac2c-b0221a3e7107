-- Fix RLS policies for admin authentication
-- This migration resolves the circular dependency issue where admin authentication
-- requires reading from admin_users table, but RLS policies prevent this access

-- =====================================================
-- PROBLEM ANALYSIS
-- =====================================================
-- Current RLS policies on admin_users require JWT role claims that only exist
-- AFTER successful authentication, creating a circular dependency:
-- 1. "Admin users can view all admin users" - requires auth.jwt() ->> 'role'
-- 2. "Admins can view their own profile" - should work but may have evaluation issues
-- 
-- During authentication, loadAdminUser() function needs to query admin_users
-- but the JWT doesn't contain role information yet.

-- =====================================================
-- SOLUTION: ADD EXPLICIT AUTHENTICATION POLICY
-- =====================================================

-- Add a new policy specifically for authentication scenarios
-- This policy allows authenticated users to read their own admin record
-- during the login process, before JWT role claims are populated
CREATE POLICY "Allow admin authentication queries" ON admin_users
    FOR SELECT TO authenticated 
    USING (
        auth.uid() IS NOT NULL 
        AND auth.uid()::text = supabase_user_id::text
        AND is_active = true
    );

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Test query that should work after this migration
-- (This is for documentation - don't execute in migration)
/*
-- Test as an authenticated user:
SELECT id, email, role, is_active 
FROM admin_users 
WHERE supabase_user_id = auth.uid() 
AND is_active = true;
*/

-- =====================================================
-- POLICY PRIORITY AND EVALUATION
-- =====================================================

-- PostgreSQL RLS evaluates SELECT policies with OR logic
-- After this migration, a user can SELECT from admin_users if ANY of these conditions are true:
-- 1. auth.jwt() ->> 'role' IN ('super_admin', 'admin') [existing policy]
-- 2. auth.uid()::text = supabase_user_id::text [existing policy] 
-- 3. auth.uid() IS NOT NULL AND auth.uid()::text = supabase_user_id::text AND is_active = true [new policy]

-- The new policy is more explicit and includes the is_active check
-- which matches the application logic in loadAdminUser()

-- =====================================================
-- ROLLBACK INSTRUCTIONS (if needed)
-- =====================================================
-- To rollback this migration:
-- DROP POLICY IF EXISTS "Allow admin authentication queries" ON admin_users;

-- =====================================================
-- TESTING INSTRUCTIONS
-- =====================================================
-- After running this migration:
-- 1. Test admin login at http://localhost:3000/admin/login
-- 2. Use credentials: <EMAIL> / your_password
-- 3. Check browser network tab for any 500 errors
-- 4. Verify successful redirect to /admin/dashboard
-- 5. Check server logs for any RLS policy violations

-- Add comment for documentation
COMMENT ON POLICY "Allow admin authentication queries" ON admin_users IS 
'Allows authenticated users to read their own admin profile during login process, resolving circular dependency with JWT role claims';

-- Create tables for the Collaborate page functionality
-- This migration creates all necessary tables for project and job applications

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create projects table for displaying available projects
CREATE TABLE projects (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  title VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  technologies TEXT[] NOT NULL, -- Array of technologies
  duration VARCHAR(100), -- e.g., "3-4 months"
  difficulty_level VARCHAR(50) CHECK (difficulty_level IN ('Beginner', 'Intermediate', 'Advanced')),
  required_skills TEXT[] NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  application_deadline DATE,
  max_collaborators INTEGER DEFAULT 5,
  
  -- Metadata
  created_by <PERSON><PERSON><PERSON>,
  updated_by UUID
);

-- Create job_postings table for displaying available jobs
CREATE TABLE job_postings (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  title VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  employment_type VARCHAR(100) CHECK (employment_type IN ('Full-time', 'Part-time', 'Contract')),
  experience_level VARCHAR(50) CHECK (experience_level IN ('Junior', 'Mid', 'Senior')),
  location_type VARCHAR(100) CHECK (location_type IN ('Remote', 'On-site', 'Hybrid')),
  compensation_range VARCHAR(100),
  required_skills TEXT[] NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  application_deadline DATE,
  
  -- Metadata
  created_by UUID,
  updated_by UUID
);

-- Create project_applications table
CREATE TABLE project_applications (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Personal Information
  full_name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  phone VARCHAR(50),
  location VARCHAR(255) NOT NULL,
  professional_title VARCHAR(255),
  
  -- Technical Information
  github_link VARCHAR(500) NOT NULL,
  linkedin_link VARCHAR(500),
  portfolio_link VARCHAR(500),
  years_experience VARCHAR(50),
  primary_skills TEXT[], -- Array of skills
  
  -- Application Details
  project_interest VARCHAR(255) NOT NULL, -- References project ID or title
  motivation_statement TEXT NOT NULL,
  availability_hours VARCHAR(50), -- e.g., "10-20 hours/week"
  expected_start_date DATE,
  additional_comments TEXT,
  
  -- Application Status
  status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'contacted')),
  admin_notes TEXT,
  reviewed_by UUID,
  reviewed_at TIMESTAMP WITH TIME ZONE,
  
  -- Foreign key to projects table (optional)
  project_id UUID REFERENCES projects(id) ON DELETE SET NULL
);

-- Create job_applications table
CREATE TABLE job_applications (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Personal Information
  full_name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  phone VARCHAR(50) NOT NULL,
  location VARCHAR(255) NOT NULL,
  current_position VARCHAR(255),
  
  -- Professional Information
  resume_file_url VARCHAR(500) NOT NULL,
  github_link VARCHAR(500) NOT NULL,
  linkedin_link VARCHAR(500),
  portfolio_link VARCHAR(500),
  years_experience VARCHAR(50),
  salary_expectation VARCHAR(100),
  
  -- Application Details
  position_applied_for VARCHAR(255) NOT NULL, -- References job posting ID or title
  cover_letter TEXT NOT NULL,
  availability_start_date DATE,
  preferred_work_arrangement VARCHAR(100) CHECK (preferred_work_arrangement IN ('Remote', 'On-site', 'Hybrid', 'Flexible')),
  additional_information TEXT,
  
  -- Application Status
  status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'contacted')),
  admin_notes TEXT,
  reviewed_by UUID,
  reviewed_at TIMESTAMP WITH TIME ZONE,
  
  -- Foreign key to job_postings table (optional)
  job_posting_id UUID REFERENCES job_postings(id) ON DELETE SET NULL
);

-- Create indexes for better performance
CREATE INDEX idx_projects_active ON projects(is_active) WHERE is_active = true;
CREATE INDEX idx_projects_difficulty ON projects(difficulty_level);
CREATE INDEX idx_projects_deadline ON projects(application_deadline);

CREATE INDEX idx_job_postings_active ON job_postings(is_active) WHERE is_active = true;
CREATE INDEX idx_job_postings_type ON job_postings(employment_type);
CREATE INDEX idx_job_postings_level ON job_postings(experience_level);
CREATE INDEX idx_job_postings_location ON job_postings(location_type);

CREATE INDEX idx_project_applications_status ON project_applications(status);
CREATE INDEX idx_project_applications_email ON project_applications(email);
CREATE INDEX idx_project_applications_created ON project_applications(created_at);

CREATE INDEX idx_job_applications_status ON job_applications(status);
CREATE INDEX idx_job_applications_email ON job_applications(email);
CREATE INDEX idx_job_applications_created ON job_applications(created_at);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at column
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_job_postings_updated_at BEFORE UPDATE ON job_postings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_project_applications_updated_at BEFORE UPDATE ON project_applications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_job_applications_updated_at BEFORE UPDATE ON job_applications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) Policies

-- Enable RLS on all tables
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_postings ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_applications ENABLE ROW LEVEL SECURITY;

-- Public read access for active projects and job postings
CREATE POLICY "Public can view active projects" ON projects
    FOR SELECT USING (is_active = true);

CREATE POLICY "Public can view active job postings" ON job_postings
    FOR SELECT USING (is_active = true);

-- Anyone can submit applications (insert only)
CREATE POLICY "Anyone can submit project applications" ON project_applications
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Anyone can submit job applications" ON job_applications
    FOR INSERT WITH CHECK (true);

-- Only authenticated users can view their own applications
CREATE POLICY "Users can view their own project applications" ON project_applications
    FOR SELECT USING (auth.jwt() ->> 'email' = email);

CREATE POLICY "Users can view their own job applications" ON job_applications
    FOR SELECT USING (auth.jwt() ->> 'email' = email);

-- Admin policies (for authenticated admin users)
-- Note: You'll need to implement proper admin role checking
CREATE POLICY "Admins can manage projects" ON projects
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Admins can manage job postings" ON job_postings
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Admins can manage project applications" ON project_applications
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Admins can manage job applications" ON job_applications
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON projects, job_postings TO anon, authenticated;
GRANT INSERT ON project_applications, job_applications TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;

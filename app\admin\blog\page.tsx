'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import AdminProtectedRoute from '@/components/admin/auth/AdminProtectedRoute';
import AdminLayout from '@/components/admin/layout/AdminLayout';
import BlogList from '@/components/admin/blog/BlogList';
import { ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';
import { AdminBlogPost } from '@/lib/types/admin';

export default function AdminBlogPage() {
  const router = useRouter();

  const handleEdit = (post: AdminBlogPost) => {
    router.push(`/admin/blog/${post.id}/edit`);
  };

  const handleDelete = (post: AdminBlogPost) => {
    // Blog post deletion is handled in the BlogList component
    console.log('Blog post deleted:', post.title);
  };

  return (
    <AdminProtectedRoute requiredPermission={ADMIN_PERMISSIONS.BLOG_VIEW}>
      <AdminLayout 
        title="Blog Posts" 
        subtitle="Manage your blog content and articles"
      >
        <BlogList onEdit={handleEdit} onDelete={handleDelete} />
      </AdminLayout>
    </AdminProtectedRoute>
  );
}

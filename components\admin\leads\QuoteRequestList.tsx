'use client';

import React, { useState, useEffect } from 'react';
import { AdminCard } from '@/components/admin/ui/AdminCard';
import AdminButton from '@/components/admin/ui/AdminButton';
import { AdminTableSkeleton } from '@/components/admin/ui/AdminLoading';
import { useAdminAuth, ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';
import { AdminPermissionWrapper } from '@/components/admin/auth/AdminProtectedRoute';
import { adminLeads } from '@/lib/supabase/admin';
import { AdminQuoteRequest, LeadStatus, LeadPriority } from '@/lib/types/admin';
import {
  Search,
  Filter,
  Eye,
  Edit,
  MessageSquare,
  Calendar,
  User,
  Building,
  DollarSign,
  Clock,
  Star,
  AlertCircle,
  CheckCircle,
  XCircle,
  Phone,
  Mail,
  Globe,
} from 'lucide-react';

interface QuoteRequestListProps {
  onView?: (request: AdminQuoteRequest) => void;
  onEdit?: (request: AdminQuoteRequest) => void;
}

export default function QuoteRequestList({ onView, onEdit }: QuoteRequestListProps) {
  const { hasPermission } = useAdminAuth();
  const [requests, setRequests] = useState<AdminQuoteRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [assignedFilter, setAssignedFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const itemsPerPage = 10;

  // Fetch quote requests
  const fetchRequests = async () => {
    try {
      setLoading(true);
      const filters: any = {};
      
      if (statusFilter !== 'all') filters.status = statusFilter;
      if (priorityFilter !== 'all') filters.priority = priorityFilter;
      if (assignedFilter !== 'all') filters.assignedTo = assignedFilter;
      if (searchQuery) filters.search = searchQuery;

      const response = await adminLeads.getQuoteRequests(currentPage, itemsPerPage, filters);
      setRequests(response.data);
      setTotalPages(response.pagination.totalPages);
    } catch (error) {
      console.error('Error fetching quote requests:', error);
      // Mock data for development
      const mockRequests: AdminQuoteRequest[] = [
        {
          id: '1',
          created_at: '2024-01-20T10:00:00Z',
          updated_at: '2024-01-20T10:00:00Z',
          full_name: 'John Smith',
          email: '<EMAIL>',
          phone: '+****************',
          company_name: 'Tech Innovations Inc.',
          website: 'https://techinnovations.com',
          project_type: 'Web Development',
          project_description: 'We need a modern e-commerce website with payment integration and inventory management.',
          budget_range: '$10,000 - $25,000',
          budget_min: 10000,
          budget_max: 25000,
          timeline: '3-4 months',
          timeline_urgency: 'normal',
          required_features: ['E-commerce', 'Payment Gateway', 'Inventory Management', 'Admin Dashboard'],
          technical_requirements: 'React/Next.js preferred, mobile responsive',
          design_preferences: 'Modern, clean design with blue color scheme',
          target_audience: 'B2B customers in technology sector',
          additional_services: ['SEO Optimization', 'Content Writing'],
          special_requirements: 'GDPR compliance required',
          how_did_you_hear: 'Google Search',
          status: 'new',
          priority: 'high',
          lead_score: 85,
          estimated_value: 17500,
          assigned_to: 'admin-1',
          next_follow_up_date: '2024-01-22',
          last_contact_date: '2024-01-20',
          contact_attempts: 1,
          source: 'website',
          utm_source: 'google',
          utm_medium: 'organic',
          utm_campaign: null,
          referrer_url: 'https://google.com',
          ip_address: '***********',
          user_agent: 'Mozilla/5.0...',
          created_by: null,
          updated_by: null,
        },
        // Add more mock data as needed
      ];
      setRequests(mockRequests);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRequests();
  }, [currentPage, statusFilter, priorityFilter, assignedFilter, searchQuery]);

  const getStatusBadge = (status: LeadStatus) => {
    const badges = {
      new: { bg: 'bg-blue-100', text: 'text-blue-800', icon: <AlertCircle className="h-3 w-3" /> },
      contacted: { bg: 'bg-yellow-100', text: 'text-yellow-800', icon: <MessageSquare className="h-3 w-3" /> },
      qualified: { bg: 'bg-green-100', text: 'text-green-800', icon: <CheckCircle className="h-3 w-3" /> },
      proposal_sent: { bg: 'bg-purple-100', text: 'text-purple-800', icon: <Mail className="h-3 w-3" /> },
      negotiating: { bg: 'bg-orange-100', text: 'text-orange-800', icon: <MessageSquare className="h-3 w-3" /> },
      won: { bg: 'bg-green-100', text: 'text-green-800', icon: <CheckCircle className="h-3 w-3" /> },
      lost: { bg: 'bg-red-100', text: 'text-red-800', icon: <XCircle className="h-3 w-3" /> },
      on_hold: { bg: 'bg-gray-100', text: 'text-gray-800', icon: <Clock className="h-3 w-3" /> },
    };
    return badges[status] || badges.new;
  };

  const getPriorityBadge = (priority: LeadPriority) => {
    const badges = {
      low: 'bg-gray-100 text-gray-800',
      medium: 'bg-blue-100 text-blue-800',
      high: 'bg-orange-100 text-orange-800',
      urgent: 'bg-red-100 text-red-800',
    };
    return badges[priority] || badges.medium;
  };

  const getLeadScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    if (score >= 40) return 'text-orange-600';
    return 'text-red-600';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  if (loading) {
    return <AdminTableSkeleton rows={5} columns={6} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 font-jost">Quote Requests</h1>
          <p className="text-gray-600 font-jost">Manage and track your project quote requests</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <AdminButton variant="outline" icon={<Filter className="h-4 w-4" />}>
            Export
          </AdminButton>
        </div>
      </div>

      {/* Filters */}
      <AdminCard>
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search by name, email, or company..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
              />
            </div>
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-3">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
            >
              <option value="all">All Status</option>
              <option value="new">New</option>
              <option value="contacted">Contacted</option>
              <option value="qualified">Qualified</option>
              <option value="proposal_sent">Proposal Sent</option>
              <option value="negotiating">Negotiating</option>
              <option value="won">Won</option>
              <option value="lost">Lost</option>
              <option value="on_hold">On Hold</option>
            </select>

            <select
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
            >
              <option value="all">All Priority</option>
              <option value="urgent">Urgent</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>

            <select
              value={assignedFilter}
              onChange={(e) => setAssignedFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
            >
              <option value="all">All Assigned</option>
              <option value="unassigned">Unassigned</option>
              <option value="me">Assigned to Me</option>
            </select>
          </div>
        </div>
      </AdminCard>

      {/* Quote Requests List */}
      {requests.length > 0 ? (
        <div className="space-y-4">
          {requests.map((request) => {
            const statusBadge = getStatusBadge(request.status);
            
            return (
              <AdminCard key={request.id} className="hover:shadow-md transition-shadow duration-200">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    {/* Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-start space-x-4">
                        <div className="h-12 w-12 bg-gray-100 rounded-full flex items-center justify-center">
                          <User className="h-6 w-6 text-gray-600" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 font-jost">
                            {request.full_name}
                          </h3>
                          <div className="flex items-center space-x-4 text-sm text-gray-600 font-jost">
                            <span className="flex items-center">
                              <Mail className="h-4 w-4 mr-1" />
                              {request.email}
                            </span>
                            {request.phone && (
                              <span className="flex items-center">
                                <Phone className="h-4 w-4 mr-1" />
                                {request.phone}
                              </span>
                            )}
                            {request.company_name && (
                              <span className="flex items-center">
                                <Building className="h-4 w-4 mr-1" />
                                {request.company_name}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        {/* Lead Score */}
                        <div className="text-center">
                          <div className={`text-lg font-bold ${getLeadScoreColor(request.lead_score)}`}>
                            {request.lead_score}
                          </div>
                          <div className="text-xs text-gray-500 font-jost">Score</div>
                        </div>

                        {/* Status Badge */}
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusBadge.bg} ${statusBadge.text}`}>
                          {statusBadge.icon}
                          <span className="ml-1">{request.status.replace('_', ' ')}</span>
                        </span>

                        {/* Priority Badge */}
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityBadge(request.priority)}`}>
                          {request.priority}
                        </span>
                      </div>
                    </div>

                    {/* Project Details */}
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-900 font-jost mb-2">
                        {request.project_type}
                      </h4>
                      <p className="text-sm text-gray-600 font-jost line-clamp-2">
                        {request.project_description}
                      </p>
                    </div>

                    {/* Key Metrics */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                      <div className="flex items-center text-sm text-gray-600 font-jost">
                        <DollarSign className="h-4 w-4 mr-1" />
                        <span>{request.budget_range || 'Custom'}</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600 font-jost">
                        <Clock className="h-4 w-4 mr-1" />
                        <span>{request.timeline || 'TBD'}</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600 font-jost">
                        <Calendar className="h-4 w-4 mr-1" />
                        <span>Created {formatDate(request.created_at)}</span>
                      </div>
                      {request.estimated_value && (
                        <div className="flex items-center text-sm text-gray-600 font-jost">
                          <Star className="h-4 w-4 mr-1" />
                          <span>Est. {formatCurrency(request.estimated_value)}</span>
                        </div>
                      )}
                    </div>

                    {/* Features */}
                    {request.required_features.length > 0 && (
                      <div className="mb-4">
                        <div className="flex flex-wrap gap-2">
                          {request.required_features.slice(0, 4).map((feature, index) => (
                            <span
                              key={index}
                              className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800"
                            >
                              {feature}
                            </span>
                          ))}
                          {request.required_features.length > 4 && (
                            <span className="text-xs text-gray-500 font-jost">
                              +{request.required_features.length - 4} more
                            </span>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Follow-up Info */}
                    {request.next_follow_up_date && (
                      <div className="flex items-center justify-between pt-3 border-t border-gray-200">
                        <div className="flex items-center text-sm text-gray-600 font-jost">
                          <Calendar className="h-4 w-4 mr-1" />
                          <span>Next follow-up: {formatDate(request.next_follow_up_date)}</span>
                        </div>
                        <div className="text-sm text-gray-500 font-jost">
                          {request.contact_attempts} contact{request.contact_attempts !== 1 ? 's' : ''}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2 ml-4">
                    <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.LEADS_VIEW}>
                      <button
                        onClick={() => onView && onView(request)}
                        className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                        title="View Details"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                    </AdminPermissionWrapper>

                    <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.LEADS_EDIT}>
                      <button
                        onClick={() => onEdit && onEdit(request)}
                        className="p-2 text-blue-600 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors"
                        title="Edit Request"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                    </AdminPermissionWrapper>
                  </div>
                </div>
              </AdminCard>
            );
          })}
        </div>
      ) : (
        <AdminCard className="text-center py-12">
          <div className="mx-auto h-16 w-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <MessageSquare className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2 font-jost">No quote requests found</h3>
          <p className="text-gray-600 font-jost">
            {searchQuery || statusFilter !== 'all' || priorityFilter !== 'all'
              ? 'Try adjusting your filters to see more results.'
              : 'Quote requests will appear here when customers submit them.'}
          </p>
        </AdminCard>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-700 font-jost">
            Showing page {currentPage} of {totalPages}
          </p>
          <div className="flex items-center space-x-2">
            <AdminButton
              variant="outline"
              size="sm"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(currentPage - 1)}
            >
              Previous
            </AdminButton>
            <AdminButton
              variant="outline"
              size="sm"
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage(currentPage + 1)}
            >
              Next
            </AdminButton>
          </div>
        </div>
      )}
    </div>
  );
}

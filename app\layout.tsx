import type { <PERSON>ada<PERSON>, Viewport } from "next";
// import { Inter } from "next/font/google"; // Disabled due to Turbopack issue
import localFont from "next/font/local";

import { Analytics } from "@vercel/analytics/react";
import { SpeedInsights } from "@vercel/speed-insights/next";

import "./globals.css";

// Using system fonts instead of Google Fonts to avoid Turbopack issues
// const inter = Inter({
//   subsets: ["latin"],
//   variable: "--font-sans",
//   display: "swap",
// });



const jost = localFont({
  src: [
    {
      path: "../public/font/Jost-VariableFont_wght.woff2",
      weight: "100 900",
      style: "normal",
    },
    {
      path: "../public/font/Jost-VariableFont_wght.woff",
      weight: "100 900",
      style: "normal",
    },
  ],
  variable: "--font-jost",
  display: "swap",
  fallback: ["system-ui", "arial"],
});

export const metadata: Metadata = {
  title: {
    default: "iREME Soft Hub - Elite App Builders",
    template: "%s | iREME Soft Hub",
  },
  description: "Dominate the Market with Bold Tech Elite App Builders. We Turn Your Ideas Into Scalable Solutions with advanced tools and personalized insights.",
  keywords: ["iREME Soft Hub", "App Development", "Web Development", "Mobile Apps", "Software Development", "Tech Solutions"],
  authors: [{ name: "iREME Soft Hub Team" }],
  creator: "iREME Soft Hub",
  metadataBase: new URL("https://iremesofthub.com"),
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://iremesofthub.com",
    title: "iREME Soft Hub - Elite App Builders",
    description: "Dominate the Market with Bold Tech Elite App Builders. We Turn Your Ideas Into Scalable Solutions with advanced tools and personalized insights.",
    siteName: "iREME Soft Hub",
  },
  twitter: {
    card: "summary_large_image",
    title: "iREME Soft Hub - Elite App Builders",
    description: "Dominate the Market with Bold Tech Elite App Builders. We Turn Your Ideas Into Scalable Solutions with advanced tools and personalized insights.",
    creator: "@iremesofthub",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  manifest: "/manifest.json",
  icons: {
    icon: "/favicon.ico",
    shortcut: "/favicon-16x16.png",
    apple: "/apple-touch-icon.png",
  },
};

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" },
  ],
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${jost.variable} font-sans antialiased`}
      >
        <div className="relative flex min-h-screen flex-col">
          <div className="flex-1">{children}</div>
        </div>
        <Analytics />
        <SpeedInsights />
      </body>
    </html>
  );
}

'use client';

import React, { useState } from 'react';
import { MapPin, Clock, DollarSign, ArrowRight, Briefcase, Users, Star } from 'lucide-react';

interface Job {
  id: string;
  title: string;
  description: string;
  previewDescription?: string;
  employmentType: 'Full-time' | 'Part-time' | 'Contract';
  experienceLevel: 'Junior' | 'Mid' | 'Senior';
  locationType: 'Remote' | 'On-site' | 'Hybrid';
  compensationRange: string;
  requiredSkills: string[];
  isActive: boolean;
  applicationDeadline?: string;
}

interface JobCardProps {
  job: Job;
  className?: string;
}

const JobCard: React.FC<JobCardProps> = ({ job, className = '' }) => {
  const [isHovered, setIsHovered] = useState(false);

  const getExperienceLevelColor = (level: string) => {
    switch (level) {
      case 'Junior':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Mid':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Senior':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getEmploymentTypeColor = (type: string) => {
    switch (type) {
      case 'Full-time':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Part-time':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Contract':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getLocationIcon = (locationType: string) => {
    return <MapPin size={16} />;
  };

  const handleApplyClick = () => {
    // Navigate to job application form with job ID
    window.location.href = `/collaborate/apply/job?id=${job.id}`;
  };

  return (
    <div 
      className={`group relative bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-500 overflow-hidden hover:-translate-y-2 ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Header Section */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="text-lg font-bold text-black mb-2 font-jost group-hover:text-gray-800 transition-colors line-clamp-1">
              {job.title}
            </h3>
            <div className="flex items-center gap-2 text-sm text-gray-600 mb-2 flex-wrap">
              <div className={`px-2 py-1 rounded-full text-xs font-medium border ${getEmploymentTypeColor(job.employmentType)} font-jost`}>
                {job.employmentType}
              </div>
              <div className="flex items-center gap-1">
                {getLocationIcon(job.locationType)}
                <span className="font-jost text-xs">{job.locationType}</span>
              </div>
              <div className={`px-2 py-1 rounded-full text-xs font-medium border ${getExperienceLevelColor(job.experienceLevel)} font-jost`}>
                {job.experienceLevel}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-1 text-yellow-500">
            <Star size={14} fill="currentColor" />
          </div>
        </div>

        {/* Compensation */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-green-600">
            <DollarSign size={18} />
            <span className="text-lg font-bold font-jost">{job.compensationRange}</span>
          </div>
          {job.isActive && (
            <div className="flex items-center gap-1 text-green-600">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-xs font-jost">Active</span>
            </div>
          )}
        </div>
      </div>

      {/* Content Section */}
      <div className="p-4">
        {/* Description */}
        <p className="text-gray-600 mb-4 leading-relaxed font-jost text-sm line-clamp-2">
          {job.previewDescription || job.description}
        </p>

        {/* Required Skills - Show only first 3 */}
        <div className="mb-4">
          <div className="flex flex-wrap gap-1">
            {job.requiredSkills.slice(0, 3).map((skill, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-blue-50 text-blue-700 rounded text-xs font-medium border border-blue-200 font-jost"
              >
                {skill}
              </span>
            ))}
            {job.requiredSkills.length > 3 && (
              <span className="px-2 py-1 text-gray-500 text-xs font-jost">
                +{job.requiredSkills.length - 3} more
              </span>
            )}
          </div>
        </div>

        {/* Job Info - Compact */}
        <div className="flex items-center justify-between mb-4 text-xs text-gray-600">
          <div className="font-jost">
            {job.applicationDeadline ?
              `Deadline: ${new Date(job.applicationDeadline).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}` :
              'Open Application'
            }
          </div>
          <div className="flex items-center gap-1">
            <Briefcase size={12} />
            <span className="font-jost">{job.locationType}</span>
          </div>
        </div>

        {/* Apply Button */}
        <button
          onClick={handleApplyClick}
          className="w-full bg-black text-white py-2.5 px-4 rounded-lg hover:bg-gray-800 transition-all duration-300 flex items-center justify-center gap-2 font-medium group/btn hover:shadow-lg transform hover:-translate-y-0.5 font-jost text-sm"
        >
          <span className="transition-transform duration-300 group-hover/btn:translate-x-[-4px]">
            Apply Now
          </span>
          <ArrowRight
            size={16}
            className={`transition-all duration-300 ${
              isHovered ? 'translate-x-1 scale-110' : ''
            }`}
          />
        </button>
      </div>

      {/* Hover Overlay Effect */}
      <div className={`absolute inset-0 bg-gradient-to-r from-green-500/5 to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none rounded-2xl`} />
      
      {/* Status Indicator */}
      {job.isActive && (
        <div className="absolute top-4 right-4">
          <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
        </div>
      )}

      {/* Urgency Badge */}
      {job.applicationDeadline && new Date(job.applicationDeadline) < new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) && (
        <div className="absolute top-4 left-4">
          <div className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium font-jost">
            Closing Soon
          </div>
        </div>
      )}
    </div>
  );
};

export default JobCard;

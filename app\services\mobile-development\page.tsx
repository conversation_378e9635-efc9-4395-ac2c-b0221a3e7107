'use client';

import React, { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import Link from 'next/link';
import {
  ArrowRight,
  Smartphone,
  Zap,
  Shield,
  Users,
  Bell,
  BarChart3,
  CheckCircle,
  Clock,
  Star,
  Quote,
  ChevronDown,
  ChevronUp,
  Apple,
  Play,
  Layers,
  TrendingUp
} from 'lucide-react';

interface Feature {
  title: string;
  description: string;
  icon: React.ReactNode;
}

interface Package {
  name: string;
  price: string;
  description: string;
  features: string[];
  timeline: string;
  popular?: boolean;
}

interface CaseStudy {
  title: string;
  client: string;
  challenge: string;
  solution: string;
  results: string[];
  image: string;
}

export default function MobileDevelopmentPage() {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  const features: Feature[] = [
    {
      title: 'Native iOS & Android',
      description: 'Platform-specific development for optimal performance and user experience on both iOS and Android devices.',
      icon: <Smartphone className="w-6 h-6" />
    },
    {
      title: 'Cross-Platform Solutions',
      description: 'React Native and Flutter development for faster deployment across multiple platforms with shared codebase.',
      icon: <Layers className="w-6 h-6" />
    },
    {
      title: 'Push Notifications',
      description: 'Engage users with targeted push notifications to increase retention and drive user actions.',
      icon: <Bell className="w-6 h-6" />
    },
    {
      title: 'Offline Functionality',
      description: 'Apps that work seamlessly offline with data synchronization when connection is restored.',
      icon: <Shield className="w-6 h-6" />
    },
    {
      title: 'App Store Optimization',
      description: 'Complete ASO strategy to improve visibility and downloads in App Store and Google Play.',
      icon: <BarChart3 className="w-6 h-6" />
    },
    {
      title: 'Performance Optimized',
      description: 'Lightning-fast apps with smooth animations and minimal battery consumption.',
      icon: <Zap className="w-6 h-6" />
    }
  ];

  const packages: Package[] = [
    {
      name: 'MVP Mobile App',
      price: '$10,000',
      description: 'Perfect for startups and businesses launching their first mobile app with core features.',
      features: [
        'Single platform (iOS or Android)',
        'Up to 5 core features',
        'User authentication',
        'Basic UI/UX design',
        'App store submission',
        '30 days support'
      ],
      timeline: '6-8 weeks'
    },
    {
      name: 'Business Mobile App',
      price: '$20,000',
      description: 'Comprehensive mobile solution for growing businesses with advanced features and integrations.',
      features: [
        'Both iOS & Android platforms',
        'Up to 15 features',
        'Custom UI/UX design',
        'Push notifications',
        'Analytics integration',
        'API integrations',
        'Admin dashboard',
        '60 days support'
      ],
      timeline: '10-14 weeks',
      popular: true
    },
    {
      name: 'Enterprise Mobile Solution',
      price: '$40,000+',
      description: 'Advanced mobile applications with complex functionality for large organizations.',
      features: [
        'Native iOS & Android',
        'Unlimited features',
        'Advanced security',
        'Offline functionality',
        'Real-time synchronization',
        'Custom integrations',
        'Performance optimization',
        '90 days support'
      ],
      timeline: '16-24 weeks'
    }
  ];

  const caseStudies: CaseStudy[] = [
    {
      title: 'HealthTech Mobile App Success',
      client: 'MediCare Solutions',
      challenge: 'Need for a patient management app with appointment scheduling, medical records, and telemedicine features.',
      solution: 'Built native iOS and Android apps with secure patient data handling, video consultations, and real-time notifications.',
      results: [
        '50,000+ active users within 6 months',
        '85% user retention rate',
        '40% reduction in missed appointments',
        '4.8-star rating on both app stores'
      ],
      image: '/placeholder-mobile-case-study.jpg'
    }
  ];

  const faqs = [
    {
      question: 'Should I build native apps or use cross-platform development?',
      answer: 'It depends on your requirements. Native apps offer the best performance and platform-specific features, while cross-platform solutions like React Native are more cost-effective and faster to develop. We help you choose the best approach based on your goals and budget.'
    },
    {
      question: 'How long does mobile app development take?',
      answer: 'Timeline varies by complexity. Simple apps take 6-10 weeks, business apps take 10-16 weeks, and complex enterprise apps can take 16-24 weeks. We provide detailed project timelines during our initial consultation.'
    },
    {
      question: 'Do you handle app store submissions?',
      answer: 'Yes! We handle the complete app store submission process for both Apple App Store and Google Play Store, including app store optimization, compliance checks, and approval follow-up.'
    },
    {
      question: 'Can you add features to my existing mobile app?',
      answer: 'Absolutely! We can enhance existing apps with new features, improve performance, update designs, and ensure compatibility with the latest OS versions.'
    },
    {
      question: 'What about app maintenance and updates?',
      answer: 'We offer comprehensive maintenance packages including bug fixes, OS compatibility updates, security patches, and feature enhancements. All projects include an initial support period.'
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden bg-gradient-to-br from-purple-50 to-white">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="inline-flex items-center bg-purple-600 text-white px-4 py-2 rounded-full text-sm mb-6 font-jost">
                <Smartphone className="w-4 h-4 mr-2" />
                Mobile App Development
              </div>
              
              <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-black mb-6 leading-tight font-jost">
                Mobile Apps That Users 
                <span className="text-purple-600"> Love & Use Daily</span>
              </h1>
              
              <p className="text-xl text-gray-600 mb-8 leading-relaxed font-jost">
                We create native iOS and Android applications plus cross-platform solutions using React Native and Flutter. 
                From concept to app store success, we build mobile experiences that engage users and drive business growth.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <Link
                  href="/get-quote"
                  className="bg-black text-white px-8 py-4 rounded-full hover:bg-gray-800 transition-all duration-300 flex items-center justify-center gap-2 font-medium font-jost group"
                >
                  Get Free Quote
                  <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
                </Link>
                <Link
                  href="/project"
                  className="border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-full hover:border-gray-400 hover:bg-gray-50 transition-all duration-300 font-medium font-jost"
                >
                  View Portfolio
                </Link>
              </div>
              
              <div className="flex items-center gap-8 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  <span className="font-jost">6-24 weeks delivery</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  <span className="font-jost">Expert mobile team</span>
                </div>
              </div>
            </div>
            
            <div className="relative">
              <div className="flex items-center justify-center space-x-4">
                <div className="bg-white p-4 rounded-3xl shadow-2xl border border-gray-100 transform rotate-12">
                  <div className="w-48 h-96 bg-gradient-to-b from-purple-100 to-purple-200 rounded-2xl p-4">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="w-6 h-6 bg-purple-600 rounded-full"></div>
                        <div className="flex space-x-1">
                          <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                          <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                          <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                        </div>
                      </div>
                      <div className="bg-white h-8 rounded-lg"></div>
                      <div className="bg-white h-16 rounded-lg"></div>
                      <div className="grid grid-cols-2 gap-2">
                        <div className="bg-white h-12 rounded-lg"></div>
                        <div className="bg-white h-12 rounded-lg"></div>
                      </div>
                      <div className="bg-purple-600 h-10 rounded-lg"></div>
                    </div>
                  </div>
                </div>
                
                <div className="bg-white p-4 rounded-3xl shadow-2xl border border-gray-100 transform -rotate-6">
                  <div className="w-48 h-96 bg-gradient-to-b from-blue-100 to-blue-200 rounded-2xl p-4">
                    <div className="space-y-4">
                      <div className="flex items-center justify-center">
                        <Apple className="w-8 h-8 text-gray-600" />
                      </div>
                      <div className="bg-white h-6 rounded-lg"></div>
                      <div className="bg-white h-20 rounded-lg"></div>
                      <div className="space-y-2">
                        <div className="bg-white h-4 rounded w-3/4"></div>
                        <div className="bg-white h-4 rounded w-1/2"></div>
                      </div>
                      <div className="bg-blue-600 h-10 rounded-lg"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              Why Choose Our Mobile Development?
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto font-jost">
              We don't just build apps – we create mobile experiences that users love, engage with daily,
              and help your business reach new heights.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group"
              >
                <div className="flex items-center justify-center w-12 h-12 bg-purple-600 text-white rounded-xl mb-6 group-hover:scale-110 transition-transform duration-300">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold text-black mb-4 font-jost">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed font-jost">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Packages Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              Mobile App Development Packages
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto font-jost">
              From MVP to enterprise-grade applications, we have the perfect package for your mobile app needs.
              All packages include app store submission and ongoing support.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {packages.map((pkg, index) => (
              <div
                key={index}
                className={`bg-white p-8 rounded-2xl shadow-lg border-2 transition-all duration-300 hover:shadow-xl hover:-translate-y-1 relative ${
                  pkg.popular ? 'border-purple-600' : 'border-gray-100'
                }`}
              >
                {pkg.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="bg-purple-600 text-white px-4 py-2 rounded-full text-sm font-medium font-jost">
                      Most Popular
                    </div>
                  </div>
                )}

                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-black mb-2 font-jost">
                    {pkg.name}
                  </h3>
                  <div className="text-4xl font-bold text-black mb-2 font-jost">
                    {pkg.price}
                  </div>
                  <p className="text-gray-600 font-jost">
                    {pkg.description}
                  </p>
                </div>

                <ul className="space-y-3 mb-8">
                  {pkg.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center text-gray-600 font-jost">
                      <CheckCircle className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>

                <div className="text-center mb-6">
                  <div className="text-sm text-gray-500 font-jost">Timeline: {pkg.timeline}</div>
                </div>

                <Link
                  href="/get-quote"
                  className={`w-full py-3 px-6 rounded-full transition-all duration-300 flex items-center justify-center gap-2 font-medium font-jost ${
                    pkg.popular
                      ? 'bg-purple-600 text-white hover:bg-purple-700'
                      : 'bg-black text-white hover:bg-gray-800'
                  }`}
                >
                  Get Started
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-600 mb-4 font-jost">
              Need a custom mobile solution? We create tailored apps for unique business requirements.
            </p>
            <Link
              href="/contact-us"
              className="inline-flex items-center text-purple-600 hover:text-purple-800 font-medium font-jost group"
            >
              Discuss Custom App Development
              <ArrowRight className="w-4 h-4 ml-1 transition-transform duration-300 group-hover:translate-x-1" />
            </Link>
          </div>
        </div>
      </section>

      {/* Case Study Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              Mobile App Success Stories
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto font-jost">
              See how our mobile app development has helped businesses reach millions of users
              and achieve remarkable growth.
            </p>
          </div>

          {caseStudies.map((study, index) => (
            <div key={index} className="bg-gray-50 rounded-2xl p-8 lg:p-12">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-3xl font-bold text-black mb-4 font-jost">
                    {study.title}
                  </h3>
                  <div className="text-purple-600 font-semibold mb-6 font-jost">
                    Client: {study.client}
                  </div>

                  <div className="space-y-6">
                    <div>
                      <h4 className="text-lg font-semibold text-black mb-2 font-jost">Challenge</h4>
                      <p className="text-gray-600 font-jost">{study.challenge}</p>
                    </div>

                    <div>
                      <h4 className="text-lg font-semibold text-black mb-2 font-jost">Solution</h4>
                      <p className="text-gray-600 font-jost">{study.solution}</p>
                    </div>

                    <div>
                      <h4 className="text-lg font-semibold text-black mb-3 font-jost">Results</h4>
                      <ul className="space-y-2">
                        {study.results.map((result, idx) => (
                          <li key={idx} className="flex items-center text-gray-600 font-jost">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                            {result}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-2xl shadow-lg">
                  <div className="space-y-4">
                    {/* App Success Metrics */}
                    <div className="text-center mb-4">
                      <h4 className="text-lg font-semibold text-black mb-2 font-jost">App Performance Metrics</h4>
                      <p className="text-sm text-gray-600 font-jost">6 Months Post-Launch Results</p>
                    </div>

                    {/* Key Metrics Grid */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-purple-50 p-4 rounded-lg text-center">
                        <div className="text-2xl font-bold text-purple-600 mb-1 font-jost">50K+</div>
                        <div className="text-xs text-gray-600 font-jost">Active Users</div>
                        <div className="flex items-center justify-center mt-1">
                          <TrendingUp className="w-3 h-3 text-purple-500 mr-1" />
                          <span className="text-xs text-purple-600 font-jost">+125% growth</span>
                        </div>
                      </div>

                      <div className="bg-blue-50 p-4 rounded-lg text-center">
                        <div className="text-2xl font-bold text-blue-600 mb-1 font-jost">4.8★</div>
                        <div className="text-xs text-gray-600 font-jost">App Store Rating</div>
                        <div className="flex items-center justify-center mt-1">
                          <Star className="w-3 h-3 text-yellow-500 mr-1" />
                          <span className="text-xs text-blue-600 font-jost">2,400+ reviews</span>
                        </div>
                      </div>

                      <div className="bg-green-50 p-4 rounded-lg text-center">
                        <div className="text-2xl font-bold text-green-600 mb-1 font-jost">85%</div>
                        <div className="text-xs text-gray-600 font-jost">Retention Rate</div>
                        <div className="flex items-center justify-center mt-1">
                          <Users className="w-3 h-3 text-green-500 mr-1" />
                          <span className="text-xs text-green-600 font-jost">30-day retention</span>
                        </div>
                      </div>

                      <div className="bg-orange-50 p-4 rounded-lg text-center">
                        <div className="text-2xl font-bold text-orange-600 mb-1 font-jost">40%</div>
                        <div className="text-xs text-gray-600 font-jost">Reduced No-Shows</div>
                        <div className="flex items-center justify-center mt-1">
                          <CheckCircle className="w-3 h-3 text-orange-500 mr-1" />
                          <span className="text-xs text-orange-600 font-jost">vs previous system</span>
                        </div>
                      </div>
                    </div>

                    {/* App Screenshots Mockup */}
                    <div className="flex items-center justify-center space-x-3 mt-6">
                      <div className="w-20 h-40 bg-gradient-to-b from-purple-100 to-purple-200 rounded-xl p-2 shadow-md">
                        <div className="space-y-2">
                          <div className="bg-white h-2 rounded w-full"></div>
                          <div className="bg-white h-8 rounded"></div>
                          <div className="bg-purple-600 h-3 rounded"></div>
                          <div className="space-y-1">
                            <div className="bg-white h-1 rounded w-3/4"></div>
                            <div className="bg-white h-1 rounded w-1/2"></div>
                          </div>
                          <div className="bg-purple-600 h-4 rounded"></div>
                        </div>
                      </div>

                      <div className="w-20 h-40 bg-gradient-to-b from-blue-100 to-blue-200 rounded-xl p-2 shadow-md">
                        <div className="space-y-2">
                          <div className="bg-white h-2 rounded w-full"></div>
                          <div className="bg-white h-6 rounded"></div>
                          <div className="grid grid-cols-2 gap-1">
                            <div className="bg-white h-3 rounded"></div>
                            <div className="bg-white h-3 rounded"></div>
                          </div>
                          <div className="bg-blue-600 h-3 rounded"></div>
                          <div className="bg-white h-4 rounded"></div>
                        </div>
                      </div>

                      <div className="w-20 h-40 bg-gradient-to-b from-green-100 to-green-200 rounded-xl p-2 shadow-md">
                        <div className="space-y-2">
                          <div className="bg-white h-2 rounded w-full"></div>
                          <div className="bg-white h-6 rounded"></div>
                          <div className="bg-green-600 h-3 rounded"></div>
                          <div className="space-y-1">
                            <div className="bg-white h-1 rounded w-full"></div>
                            <div className="bg-white h-1 rounded w-2/3"></div>
                            <div className="bg-white h-1 rounded w-1/2"></div>
                          </div>
                          <div className="bg-green-600 h-4 rounded"></div>
                        </div>
                      </div>
                    </div>

                    {/* Platform Badges */}
                    <div className="flex items-center justify-center space-x-4 mt-4">
                      <div className="bg-black text-white px-3 py-1 rounded-full text-xs font-jost">
                        App Store
                      </div>
                      <div className="bg-green-600 text-white px-3 py-1 rounded-full text-xs font-jost">
                        Google Play
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-gray-600 font-jost">
              Get answers to common questions about our mobile app development services.
            </p>
          </div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div
                key={index}
                className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"
              >
                <button
                  onClick={() => setOpenFAQ(openFAQ === index ? null : index)}
                  className="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
                >
                  <h3 className="text-lg font-semibold text-black font-jost pr-4">
                    {faq.question}
                  </h3>
                  {openFAQ === index ? (
                    <ChevronUp className="w-5 h-5 text-gray-500 flex-shrink-0" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-gray-500 flex-shrink-0" />
                  )}
                </button>

                {openFAQ === index && (
                  <div className="px-8 pb-6">
                    <p className="text-gray-600 leading-relaxed font-jost">
                      {faq.answer}
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-purple-600 to-pink-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl sm:text-5xl font-bold text-white mb-6 font-jost">
            Ready to Launch Your Mobile App?
          </h2>
          <p className="text-xl text-purple-100 mb-8 leading-relaxed font-jost">
            Join thousands of successful businesses that have transformed their customer experience with our mobile apps.
            Let's bring your app idea to life and reach millions of users.
          </p>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-8">
            <Link
              href="/get-quote"
              className="bg-white text-purple-600 px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 flex items-center gap-2 font-medium font-jost group"
            >
              Get Free App Consultation
              <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
            </Link>
            <Link
              href="/contact-us"
              className="border-2 border-white text-white px-8 py-4 rounded-full hover:bg-white hover:text-purple-600 transition-all duration-300 font-medium font-jost"
            >
              Schedule a Call
            </Link>
          </div>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-purple-100">
            <div className="flex items-center gap-2">
              <Clock className="w-5 h-5" />
              <span className="font-jost">Free consultation & quote</span>
            </div>
            <div className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              <span className="font-jost">App store submission included</span>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

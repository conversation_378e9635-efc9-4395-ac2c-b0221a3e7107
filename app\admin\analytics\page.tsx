'use client';

import React from 'react';
import AdminProtectedRoute from '@/components/admin/auth/AdminProtectedRoute';
import AdminLayout from '@/components/admin/layout/AdminLayout';
import AnalyticsDashboard from '@/components/admin/analytics/AnalyticsDashboard';
import { ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';

export default function AdminAnalyticsPage() {
  return (
    <AdminProtectedRoute requiredPermission={ADMIN_PERMISSIONS.ANALYTICS_VIEW}>
      <AdminLayout 
        title="Analytics" 
        subtitle="Track your business performance and insights"
      >
        <AnalyticsDashboard />
      </AdminLayout>
    </AdminProtectedRoute>
  );
}

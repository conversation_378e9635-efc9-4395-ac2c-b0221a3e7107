'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ChevronRight, Home } from 'lucide-react';

interface BreadcrumbItem {
  label: string;
  href?: string;
  isActive?: boolean;
}

export default function AdminBreadcrumb() {
  const pathname = usePathname();

  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const segments = pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [];

    // Always start with Dashboard
    breadcrumbs.push({
      label: 'Dashboard',
      href: '/admin/dashboard',
    });

    // Skip 'admin' segment and process the rest
    const adminIndex = segments.indexOf('admin');
    const relevantSegments = segments.slice(adminIndex + 1);

    let currentPath = '/admin';

    relevantSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const isLast = index === relevantSegments.length - 1;

      // Convert segment to readable label
      const label = getSegmentLabel(segment, relevantSegments, index);

      breadcrumbs.push({
        label,
        href: isLast ? undefined : currentPath,
        isActive: isLast,
      });
    });

    return breadcrumbs;
  };

  const getSegmentLabel = (segment: string, allSegments: string[], index: number): string => {
    // Handle special cases
    const labelMap: Record<string, string> = {
      'dashboard': 'Dashboard',
      'templates': 'Templates',
      'services': 'Services',
      'blog': 'Blog Posts',
      'media': 'Media Library',
      'leads': 'Lead Management',
      'quotes': 'Quote Requests',
      'contacts': 'Contact Forms',
      'customers': 'Customers',
      'analytics': 'Analytics',
      'users': 'User Management',
      'settings': 'System Settings',
      'admin': 'Administration',
      'content': 'Content Management',
      'new': 'New',
      'edit': 'Edit',
      'view': 'View',
    };

    // Check if it's a known segment
    if (labelMap[segment]) {
      return labelMap[segment];
    }

    // Check if it's an ID (UUID pattern)
    if (segment.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      // Look at the previous segment to determine context
      const previousSegment = allSegments[index - 1];
      if (previousSegment === 'templates') return 'Template Details';
      if (previousSegment === 'services') return 'Service Details';
      if (previousSegment === 'blog') return 'Blog Post Details';
      if (previousSegment === 'leads' || previousSegment === 'quotes') return 'Quote Details';
      if (previousSegment === 'customers') return 'Customer Details';
      if (previousSegment === 'users') return 'User Details';
      return 'Details';
    }

    // Default: capitalize and replace hyphens/underscores with spaces
    return segment
      .split(/[-_]/)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const breadcrumbs = generateBreadcrumbs();

  // Don't show breadcrumb if we're just on the dashboard
  if (breadcrumbs.length <= 1) {
    return null;
  }

  return (
    <nav className="bg-white border-b border-gray-200 px-6 py-3">
      <div className="flex items-center space-x-2 text-sm">
        <Home className="h-4 w-4 text-gray-400" />
        
        {breadcrumbs.map((item, index) => (
          <React.Fragment key={index}>
            {index > 0 && (
              <ChevronRight className="h-4 w-4 text-gray-400 flex-shrink-0" />
            )}
            
            {item.href && !item.isActive ? (
              <Link
                href={item.href}
                className="text-gray-600 hover:text-gray-900 transition-colors font-jost"
              >
                {item.label}
              </Link>
            ) : (
              <span
                className={`font-jost ${
                  item.isActive 
                    ? 'text-gray-900 font-medium' 
                    : 'text-gray-600'
                }`}
              >
                {item.label}
              </span>
            )}
          </React.Fragment>
        ))}
      </div>
    </nav>
  );
}

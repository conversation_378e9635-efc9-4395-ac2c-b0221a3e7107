'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAdminAuth, ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';
import { AdminUserRole } from '@/lib/types/admin';
import { Loader2, Shield, AlertTriangle, Lock } from 'lucide-react';

interface AdminProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission?: string;
  requiredRole?: AdminUserRole | AdminUserRole[];
  fallbackPath?: string;
  showUnauthorized?: boolean;
}

export default function AdminProtectedRoute({
  children,
  requiredPermission,
  requiredRole,
  fallbackPath = '/admin/login',
  showUnauthorized = true,
}: AdminProtectedRouteProps) {
  const router = useRouter();
  const { user, adminUser, loading, hasPermission, hasRole } = useAdminAuth();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    if (!loading) {
      setIsChecking(false);
    }
  }, [loading]);

  // Show loading state
  if (loading || isChecking) {
    return <AdminLoadingScreen />;
  }

  // Not authenticated
  if (!user || !adminUser) {
    router.push(fallbackPath);
    return <AdminLoadingScreen />;
  }

  // Check if user account is active
  if (!adminUser.is_active) {
    if (showUnauthorized) {
      return <AdminUnauthorizedScreen reason="account_inactive" />;
    }
    router.push(fallbackPath);
    return <AdminLoadingScreen />;
  }

  // Check role requirements
  if (requiredRole && !hasRole(requiredRole)) {
    if (showUnauthorized) {
      return <AdminUnauthorizedScreen reason="insufficient_role" requiredRole={requiredRole} />;
    }
    router.push('/admin/dashboard');
    return <AdminLoadingScreen />;
  }

  // Check permission requirements
  if (requiredPermission && !hasPermission(requiredPermission)) {
    if (showUnauthorized) {
      return <AdminUnauthorizedScreen reason="insufficient_permission" requiredPermission={requiredPermission} />;
    }
    router.push('/admin/dashboard');
    return <AdminLoadingScreen />;
  }

  // All checks passed - render children
  return <>{children}</>;
}

// =====================================================
// LOADING SCREEN COMPONENT
// =====================================================

function AdminLoadingScreen() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center">
      <div className="text-center">
        <div className="mx-auto h-16 w-16 bg-black rounded-2xl flex items-center justify-center mb-6">
          <Loader2 className="h-8 w-8 text-white animate-spin" />
        </div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2 font-jost">
          Loading Admin Dashboard
        </h2>
        <p className="text-gray-600 font-jost">
          Verifying your credentials...
        </p>
      </div>
    </div>
  );
}

// =====================================================
// UNAUTHORIZED SCREEN COMPONENT
// =====================================================

interface AdminUnauthorizedScreenProps {
  reason: 'account_inactive' | 'insufficient_role' | 'insufficient_permission';
  requiredRole?: AdminUserRole | AdminUserRole[];
  requiredPermission?: string;
}

function AdminUnauthorizedScreen({ 
  reason, 
  requiredRole, 
  requiredPermission 
}: AdminUnauthorizedScreenProps) {
  const router = useRouter();
  const { signOut, adminUser } = useAdminAuth();

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push('/admin/login');
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const getErrorContent = () => {
    switch (reason) {
      case 'account_inactive':
        return {
          icon: <AlertTriangle className="h-12 w-12 text-red-500" />,
          title: 'Account Inactive',
          message: 'Your admin account has been deactivated. Please contact your system administrator for assistance.',
          showContactInfo: true,
        };
      
      case 'insufficient_role':
        return {
          icon: <Shield className="h-12 w-12 text-orange-500" />,
          title: 'Insufficient Role',
          message: `This page requires ${Array.isArray(requiredRole) ? requiredRole.join(' or ') : requiredRole} privileges. Your current role is ${adminUser?.role}.`,
          showContactInfo: true,
        };
      
      case 'insufficient_permission':
        return {
          icon: <Lock className="h-12 w-12 text-red-500" />,
          title: 'Access Denied',
          message: `You don't have permission to access this resource. Required permission: ${requiredPermission}`,
          showContactInfo: true,
        };
      
      default:
        return {
          icon: <AlertTriangle className="h-12 w-12 text-red-500" />,
          title: 'Access Denied',
          message: 'You are not authorized to access this resource.',
          showContactInfo: true,
        };
    }
  };

  const errorContent = getErrorContent();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <div className="bg-white rounded-2xl shadow-xl p-8 border border-gray-100 text-center">
          {/* Icon */}
          <div className="mx-auto mb-6">
            {errorContent.icon}
          </div>

          {/* Title */}
          <h2 className="text-2xl font-bold text-gray-900 mb-4 font-jost">
            {errorContent.title}
          </h2>

          {/* Message */}
          <p className="text-gray-600 mb-6 font-jost leading-relaxed">
            {errorContent.message}
          </p>

          {/* User Info */}
          {adminUser && (
            <div className="bg-gray-50 rounded-lg p-4 mb-6 text-left">
              <h3 className="text-sm font-medium text-gray-900 mb-2 font-jost">
                Current User:
              </h3>
              <div className="space-y-1 text-sm text-gray-600 font-jost">
                <p><span className="font-medium">Email:</span> {adminUser.email}</p>
                <p><span className="font-medium">Role:</span> {adminUser.role}</p>
                <p><span className="font-medium">Name:</span> {adminUser.first_name} {adminUser.last_name}</p>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="space-y-3">
            <button
              onClick={() => router.push('/admin/dashboard')}
              className="w-full bg-black hover:bg-gray-800 text-white font-medium py-3 px-4 rounded-lg transition-all duration-300 font-jost"
            >
              Go to Dashboard
            </button>
            
            <button
              onClick={handleSignOut}
              className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-4 rounded-lg transition-all duration-300 font-jost"
            >
              Sign Out
            </button>
          </div>

          {/* Contact Info */}
          {errorContent.showContactInfo && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <p className="text-sm text-gray-500 font-jost">
                Need help? Contact your system administrator or email{' '}
                <a 
                  href="mailto:<EMAIL>" 
                  className="text-blue-600 hover:text-blue-500 font-medium"
                >
                  <EMAIL>
                </a>
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// =====================================================
// PERMISSION-BASED COMPONENT WRAPPER
// =====================================================

interface AdminPermissionWrapperProps {
  children: React.ReactNode;
  permission: string;
  fallback?: React.ReactNode;
  role?: AdminUserRole | AdminUserRole[];
}

export function AdminPermissionWrapper({
  children,
  permission,
  fallback = null,
  role,
}: AdminPermissionWrapperProps) {
  const { hasPermission, hasRole } = useAdminAuth();

  // Check role if specified
  if (role && !hasRole(role)) {
    return <>{fallback}</>;
  }

  // Check permission
  if (!hasPermission(permission)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// =====================================================
// ROLE-BASED COMPONENT WRAPPER
// =====================================================

interface AdminRoleWrapperProps {
  children: React.ReactNode;
  role: AdminUserRole | AdminUserRole[];
  fallback?: React.ReactNode;
}

export function AdminRoleWrapper({
  children,
  role,
  fallback = null,
}: AdminRoleWrapperProps) {
  const { hasRole } = useAdminAuth();

  if (!hasRole(role)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// =====================================================
// UTILITY HOOKS FOR CONDITIONAL RENDERING
// =====================================================

export function useAdminAccess(
  permission?: string,
  role?: AdminUserRole | AdminUserRole[]
) {
  const { hasPermission, hasRole, adminUser } = useAdminAuth();

  const hasRequiredPermission = permission ? hasPermission(permission) : true;
  const hasRequiredRole = role ? hasRole(role) : true;
  const isActive = adminUser?.is_active ?? false;

  return {
    hasAccess: hasRequiredPermission && hasRequiredRole && isActive,
    hasPermission: hasRequiredPermission,
    hasRole: hasRequiredRole,
    isActive,
    adminUser,
  };
}

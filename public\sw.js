if(!self.define){let e,s={};const a=(a,i)=>(a=new URL(a+".js",i).href,s[a]||new Promise(s=>{if("document"in self){const e=document.createElement("script");e.src=a,e.onload=s,document.head.appendChild(e)}else e=a,importScripts(a),s()}).then(()=>{let e=s[a];if(!e)throw new Error(`Module ${a} didn’t register its module`);return e}));self.define=(i,c)=>{const t=e||("document"in self?document.currentScript.src:"")||location.href;if(s[t])return;let n={};const f=e=>a(e,t),d={module:{uri:t},exports:n,require:f};s[t]=Promise.all(i.map(e=>d[e]||f(e))).then(e=>(c(...e),n))}}define(["./workbox-f52fd911"],function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"3e727c12226e06d9c97cc4e92e6338c5"},{url:"/_next/static/chunks/app/_not-found/page-4376c843ca965907.js",revision:"yuWN1JUBUwpk9bUKkazkM"},{url:"/_next/static/chunks/app/layout-93dde518cdc6ebab.js",revision:"yuWN1JUBUwpk9bUKkazkM"},{url:"/_next/static/chunks/app/loading-342526c3ccbc6add.js",revision:"yuWN1JUBUwpk9bUKkazkM"},{url:"/_next/static/chunks/app/not-found-b1e79fad2ebcacce.js",revision:"yuWN1JUBUwpk9bUKkazkM"},{url:"/_next/static/chunks/app/page-140f927b1bcd055c.js",revision:"yuWN1JUBUwpk9bUKkazkM"},{url:"/_next/static/chunks/main-a7e348ee465aeaf0.js",revision:"yuWN1JUBUwpk9bUKkazkM"},{url:"/_next/static/chunks/main-app-5b03ec75a1cdba16.js",revision:"yuWN1JUBUwpk9bUKkazkM"},{url:"/_next/static/chunks/pages/_app-2458a306623f743d.js",revision:"yuWN1JUBUwpk9bUKkazkM"},{url:"/_next/static/chunks/pages/_error-22e7ec8e7456d9c1.js",revision:"yuWN1JUBUwpk9bUKkazkM"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/vendors-d0777e1f045b0788.js",revision:"yuWN1JUBUwpk9bUKkazkM"},{url:"/_next/static/chunks/webpack-2c5a9216946d1c9d.js",revision:"yuWN1JUBUwpk9bUKkazkM"},{url:"/_next/static/css/bfc5a44d9987939e.css",revision:"bfc5a44d9987939e"},{url:"/_next/static/media/0aa834ed78bf6d07-s.woff2",revision:"324703f03c390d2e2a4f387de85fe63d"},{url:"/_next/static/media/26a46d62cd723877-s.woff2",revision:"befd9c0fdfa3d8a645d5f95717ed6420"},{url:"/_next/static/media/55c55f0601d81cf3-s.woff2",revision:"43828e14271c77b87e3ed582dbff9f74"},{url:"/_next/static/media/581909926a08bbc8-s.woff2",revision:"f0b86e7c24f455280b8df606b89af891"},{url:"/_next/static/media/67957d42bae0796d-s.woff2",revision:"54f02056e07c55023315568c637e3a96"},{url:"/_next/static/media/886030b0b59bc5a7-s.woff2",revision:"c94e6e6c23e789fcb0fc60d790c9d2c1"},{url:"/_next/static/media/8e9860b6e62d6359-s.woff2",revision:"01ba6c2a184b8cba08b0d57167664d75"},{url:"/_next/static/media/939c4f875ee75fbb-s.woff2",revision:"4a4e74bed5809194e4bc6538eb1a1e30"},{url:"/_next/static/media/97e0cb1ae144a2a9-s.woff2",revision:"e360c61c5bd8d90639fd4503c829c2dc"},{url:"/_next/static/media/bb3ef058b751a6ad-s.p.woff2",revision:"782150e6836b9b074d1a798807adcb18"},{url:"/_next/static/media/df0a9ae256c0569c-s.woff2",revision:"d54db44de5ccb18886ece2fda72bdfe0"},{url:"/_next/static/media/e4af272ccee01ff0-s.p.woff2",revision:"65850a373e258f1c897a2b3d75eb74de"},{url:"/_next/static/media/f911b923c6adde36-s.woff2",revision:"0f8d347d49960d05c9430d83e49edeb7"},{url:"/_next/static/yuWN1JUBUwpk9bUKkazkM/_buildManifest.js",revision:"49a09740cdcfe420ba5ec5fe64be42c1"},{url:"/_next/static/yuWN1JUBUwpk9bUKkazkM/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/file.svg",revision:"d09f95206c3fa0bb9bd9fefabfd0ea71"},{url:"/globe.svg",revision:"2aaafa6a49b6563925fe440891e32717"},{url:"/manifest.json",revision:"b5221c38552240280075fc23f393c487"},{url:"/next.svg",revision:"8e061864f388b47f33a1c3780831193e"},{url:"/vercel.svg",revision:"c0af2f507b369b085b35ef4bbe3bcf1e"},{url:"/window.svg",revision:"a2760511c65806022ad20adf74370ff3"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:s,event:a,state:i})=>s&&"opaqueredirect"===s.type?new Response(s.body,{status:200,statusText:"OK",headers:s.headers}):s}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.googleapis\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/fonts\.gstatic\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET")});

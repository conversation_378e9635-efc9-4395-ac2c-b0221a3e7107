import React from 'react';
import { cn } from '@/lib/utils';

interface AdminCardProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  shadow?: 'none' | 'sm' | 'md' | 'lg';
  border?: boolean;
  hover?: boolean;
  onClick?: (() => void) | undefined;
}

interface AdminCardHeaderProps {
  children: React.ReactNode;
  className?: string;
  border?: boolean;
}

interface AdminCardContentProps {
  children: React.ReactNode;
  className?: string;
}

interface AdminCardFooterProps {
  children: React.ReactNode;
  className?: string;
  border?: boolean;
}

export function AdminCard({
  children,
  className,
  padding = 'md',
  shadow = 'sm',
  border = true,
  hover = false,
  onClick,
}: AdminCardProps) {
  const paddingClasses = {
    none: '',
    sm: 'p-3',
    md: 'p-6',
    lg: 'p-8',
  };

  const shadowClasses = {
    none: '',
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg',
  };

  return (
    <div
      className={cn(
        'bg-white rounded-lg',
        paddingClasses[padding],
        shadowClasses[shadow],
        border && 'border border-gray-200',
        hover && 'hover:shadow-md transition-shadow duration-200',
        onClick && 'cursor-pointer',
        className
      )}
      onClick={onClick}
    >
      {children}
    </div>
  );
}

export function AdminCardHeader({
  children,
  className,
  border = true,
}: AdminCardHeaderProps) {
  return (
    <div
      className={cn(
        'pb-4',
        border && 'border-b border-gray-200 mb-4',
        className
      )}
    >
      {children}
    </div>
  );
}

export function AdminCardContent({
  children,
  className,
}: AdminCardContentProps) {
  return (
    <div className={cn('', className)}>
      {children}
    </div>
  );
}

export function AdminCardFooter({
  children,
  className,
  border = true,
}: AdminCardFooterProps) {
  return (
    <div
      className={cn(
        'pt-4',
        border && 'border-t border-gray-200 mt-4',
        className
      )}
    >
      {children}
    </div>
  );
}

// Stat Card Component
interface AdminStatCardProps {
  title: string;
  value: string | number;
  change?: {
    value: string;
    type: 'positive' | 'negative' | 'neutral';
  };
  icon?: React.ReactNode;
  className?: string;
}

export function AdminStatCard({
  title,
  value,
  change,
  icon,
  className,
}: AdminStatCardProps) {
  const getChangeColor = (type: 'positive' | 'negative' | 'neutral') => {
    switch (type) {
      case 'positive':
        return 'text-green-600';
      case 'negative':
        return 'text-red-600';
      case 'neutral':
        return 'text-gray-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <AdminCard className={cn('', className)} hover>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 font-jost">
            {title}
          </p>
          <p className="text-2xl font-semibold text-gray-900 mt-1 font-jost">
            {value}
          </p>
          {change && (
            <p className={cn(
              'text-sm font-medium mt-1 font-jost',
              getChangeColor(change.type)
            )}>
              {change.value}
            </p>
          )}
        </div>
        {icon && (
          <div className="flex-shrink-0 ml-4">
            <div className="h-12 w-12 bg-gray-100 rounded-lg flex items-center justify-center">
              {icon}
            </div>
          </div>
        )}
      </div>
    </AdminCard>
  );
}

// Quick Action Card Component
interface AdminQuickActionCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  onClick: () => void;
  className?: string;
  disabled?: boolean;
}

export function AdminQuickActionCard({
  title,
  description,
  icon,
  onClick,
  className,
  disabled = false,
}: AdminQuickActionCardProps) {
  return (
    <AdminCard
      className={cn(
        'cursor-pointer transition-all duration-200',
        disabled 
          ? 'opacity-50 cursor-not-allowed' 
          : 'hover:shadow-md hover:-translate-y-1',
        className
      )}
      onClick={disabled ? undefined : onClick}
    >
      <div className="flex items-start space-x-4">
        <div className="flex-shrink-0">
          <div className="h-10 w-10 bg-black rounded-lg flex items-center justify-center">
            <div className="text-white">
              {icon}
            </div>
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-medium text-gray-900 font-jost">
            {title}
          </h3>
          <p className="text-sm text-gray-600 mt-1 font-jost">
            {description}
          </p>
        </div>
      </div>
    </AdminCard>
  );
}

// Empty State Card Component
interface AdminEmptyStateCardProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  action?: {
    label: string;
    onClick: () => void;
  };
  className?: string;
}

export function AdminEmptyStateCard({
  title,
  description,
  icon,
  action,
  className,
}: AdminEmptyStateCardProps) {
  return (
    <AdminCard className={cn('text-center py-12', className)}>
      {icon && (
        <div className="mx-auto h-16 w-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <div className="text-gray-400">
            {icon}
          </div>
        </div>
      )}
      <h3 className="text-lg font-medium text-gray-900 mb-2 font-jost">
        {title}
      </h3>
      <p className="text-gray-600 mb-6 font-jost">
        {description}
      </p>
      {action && (
        <button
          onClick={action.onClick}
          className="inline-flex items-center px-4 py-2 bg-black text-white text-sm font-medium rounded-lg hover:bg-gray-800 transition-colors font-jost"
        >
          {action.label}
        </button>
      )}
    </AdminCard>
  );
}

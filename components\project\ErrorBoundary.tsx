'use client';

import React from 'react';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error | null; resetError: () => void }>;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Template showcase error:', error, errorInfo);
  }

  resetError = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={this.state.error} resetError={this.resetError} />;
      }

      return <DefaultErrorFallback error={this.state.error} resetError={this.resetError} />;
    }

    return this.props.children;
  }
}

interface ErrorFallbackProps {
  error: Error | null;
  resetError: () => void;
}

const DefaultErrorFallback: React.FC<ErrorFallbackProps> = ({ error, resetError }) => {
  const handleRefresh = () => {
    resetError();
    window.location.reload();
  };

  const goHome = () => {
    window.location.href = '/';
  };

  return (
    <div className="min-h-screen flex items-center justify-center py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div className="max-w-md w-full text-center">
        {/* Error Icon */}
        <div className="mb-8">
          <div className="mx-auto w-24 h-24 bg-red-100 rounded-full flex items-center justify-center">
            <AlertTriangle size={32} className="text-red-500" />
          </div>
        </div>

        {/* Error Message */}
        <h1 className="text-3xl font-bold text-gray-900 mb-4 font-jost">
          Something went wrong
        </h1>
        
        <p className="text-lg text-gray-600 mb-2 font-jost">
          We encountered an error while loading the template showcase.
        </p>

        {/* Error Details (only in development) */}
        {process.env.NODE_ENV === 'development' && error && (
          <details className="mb-8 text-left">
            <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700 mb-2">
              Error Details (Development)
            </summary>
            <div className="bg-gray-100 p-4 rounded-lg text-xs text-gray-700 overflow-auto max-h-32">
              <pre>{error.message}</pre>
              {error.stack && (
                <pre className="mt-2 text-xs">{error.stack}</pre>
              )}
            </div>
          </details>
        )}

        <p className="text-sm text-gray-500 mb-8 font-jost">
          Please try refreshing the page or go back to the homepage.
        </p>

        {/* Action Buttons */}
        <div className="space-y-4">
          <button
            onClick={handleRefresh}
            className="inline-flex items-center justify-center gap-2 w-full px-6 py-3 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors duration-200 font-medium font-jost"
          >
            <RefreshCw size={16} />
            Try Again
          </button>
          
          <button
            onClick={goHome}
            className="inline-flex items-center justify-center gap-2 w-full px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-lg hover:border-gray-400 hover:bg-gray-50 transition-colors duration-200 font-medium font-jost"
          >
            <Home size={16} />
            Go to Homepage
          </button>
        </div>
      </div>
    </div>
  );
};

// Template Card Error Fallback
export const TemplateCardErrorFallback: React.FC<ErrorFallbackProps> = ({ resetError }) => {
  return (
    <div className="bg-white rounded-2xl shadow-lg border border-red-200 p-6 text-center">
      <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <AlertTriangle size={20} className="text-red-500" />
      </div>
      
      <h3 className="text-lg font-semibold text-gray-900 mb-2 font-jost">
        Failed to Load Template
      </h3>
      
      <p className="text-gray-600 mb-4 text-sm font-jost">
        There was an error loading this template card.
      </p>
      
      <button
        onClick={resetError}
        className="inline-flex items-center gap-2 px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors duration-200 text-sm font-medium font-jost"
      >
        <RefreshCw size={14} />
        Retry
      </button>
    </div>
  );
};

// Gallery Error Fallback
export const GalleryErrorFallback: React.FC<ErrorFallbackProps> = ({ resetError }) => {
  return (
    <div className="bg-gray-100 rounded-2xl p-12 text-center">
      <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
        <AlertTriangle size={24} className="text-red-500" />
      </div>
      
      <h3 className="text-xl font-semibold text-gray-900 mb-3 font-jost">
        Gallery Failed to Load
      </h3>
      
      <p className="text-gray-600 mb-6 font-jost">
        We couldn't load the template images. Please try again.
      </p>
      
      <button
        onClick={resetError}
        className="inline-flex items-center gap-2 px-6 py-3 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors duration-200 font-medium font-jost"
      >
        <RefreshCw size={16} />
        Reload Gallery
      </button>
    </div>
  );
};

export default ErrorBoundary;

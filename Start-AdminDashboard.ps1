# iREME Soft Hub Admin Dashboard Startup Script
# PowerShell version for better error handling

Write-Host "===============================================" -ForegroundColor Green
Write-Host "   iREME Soft Hub Admin Dashboard Startup" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green
Write-Host ""

# Change to project directory
$ProjectPath = "C:\Users\<USER>\Desktop\iremesofthub"
Set-Location $ProjectPath
Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow
Write-Host ""

# Function to check if command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check Node.js installation
Write-Host "[1/5] Checking Node.js installation..." -ForegroundColor Cyan
if (Test-Command "node") {
    Write-Host "✅ Node.js is available" -ForegroundColor Green
    $nodeVersion = node --version
    Write-Host "Version: $nodeVersion" -ForegroundColor Gray
} else {
    Write-Host "❌ Node.js is not installed or not in PATH" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please install Node.js from: https://nodejs.org/" -ForegroundColor Yellow
    Write-Host "After installation, restart this script." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host ""

# Check npm availability
Write-Host "[2/5] Checking npm availability..." -ForegroundColor Cyan
if (Test-Command "npm") {
    Write-Host "✅ npm is available" -ForegroundColor Green
    $npmVersion = npm --version
    Write-Host "Version: $npmVersion" -ForegroundColor Gray
} else {
    Write-Host "❌ npm is not available" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host ""

# Check project files
Write-Host "[3/5] Checking project files..." -ForegroundColor Cyan
if (Test-Path "package.json") {
    Write-Host "✅ package.json found" -ForegroundColor Green
} else {
    Write-Host "❌ package.json not found in current directory" -ForegroundColor Red
    Write-Host "Make sure you're in the correct project directory" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host ""

# Install dependencies
Write-Host "[4/5] Checking dependencies..." -ForegroundColor Cyan
if (-not (Test-Path "node_modules")) {
    Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
    Write-Host "This may take a few minutes..." -ForegroundColor Gray
    
    try {
        npm install
        Write-Host "✅ Dependencies installed successfully" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
        Write-Host "Trying alternative installation..." -ForegroundColor Yellow
        
        try {
            npm install --legacy-peer-deps
            Write-Host "✅ Dependencies installed with legacy peer deps" -ForegroundColor Green
        } catch {
            Write-Host "❌ Alternative installation also failed" -ForegroundColor Red
            Write-Host "Please check your internet connection and try again" -ForegroundColor Yellow
            Read-Host "Press Enter to exit"
            exit 1
        }
    }
} else {
    Write-Host "✅ Dependencies already installed" -ForegroundColor Green
}
Write-Host ""

# Start development server
Write-Host "[5/5] Starting development server..." -ForegroundColor Cyan
Write-Host ""
Write-Host "🚀 Starting iREME Soft Hub Admin Dashboard..." -ForegroundColor Green
Write-Host ""
Write-Host "===============================================" -ForegroundColor Green
Write-Host "    SERVER INFORMATION" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green
Write-Host "Local URL:    http://localhost:3000" -ForegroundColor White
Write-Host "Admin Panel:  http://localhost:3000/admin" -ForegroundColor Yellow
Write-Host ""
Write-Host "Default Admin Credentials:" -ForegroundColor Cyan
Write-Host "Email:        <EMAIL>" -ForegroundColor White
Write-Host "Password:     admin123" -ForegroundColor White
Write-Host ""
Write-Host "===============================================" -ForegroundColor Green
Write-Host ""
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Gray
Write-Host ""

# Start the server
try {
    npm run dev
} catch {
    Write-Host ""
    Write-Host "❌ Failed to start development server" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Server stopped." -ForegroundColor Yellow
Read-Host "Press Enter to exit"

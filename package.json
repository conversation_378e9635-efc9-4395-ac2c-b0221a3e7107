{"name": "iremesofthub", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "analyze": "ANALYZE=true npm run build", "clean": "rm -rf .next out dist", "postinstall": "npm run type-check"}, "dependencies": {"@headlessui/react": "^2.2.4", "@next/bundle-analyzer": "^15.3.5", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.5", "@tailwindcss/line-clamp": "^0.4.4", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "framer-motion": "^12.23.3", "i18next": "^25.3.2", "lucide-react": "^0.525.0", "next": "15.3.5", "next-i18next": "^15.4.2", "next-pwa": "^5.6.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-i18next": "^15.6.0", "react-select": "^5.10.1", "sharp": "^0.34.3", "zod": "^4.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/next-pwa": "^5.6.9", "@types/node": "^20.19.7", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^3.4.17", "typescript": "^5"}}
-- Add slug fields to projects and job_postings tables
-- This migration adds URL-friendly slug columns for better SEO and routing

-- Add slug column to projects table
ALTER TABLE projects 
ADD COLUMN slug VARCHAR(255) UNIQUE;

-- Add slug column to job_postings table
ALTER TABLE job_postings 
ADD COLUMN slug VARCHAR(255) UNIQUE;

-- Create function to generate URL-friendly slugs
CREATE OR REPLACE FUNCTION generate_slug(input_text TEXT)
RETURNS TEXT AS $$
BEGIN
  RETURN lower(
    regexp_replace(
      regexp_replace(
        regexp_replace(input_text, '[^a-zA-Z0-9\s-]', '', 'g'),
        '\s+', '-', 'g'
      ),
      '-+', '-', 'g'
    )
  );
END;
$$ LANGUAGE plpgsql;

-- Create function to ensure unique slugs
CREATE OR REPLACE FUNCTION ensure_unique_slug(base_slug TEXT, table_name TEXT, exclude_id UUID DEFAULT NULL)
RETURNS TEXT AS $$
DECLARE
  final_slug TEXT;
  counter INTEGER := 1;
  slug_exists BOOLEAN;
BEGIN
  final_slug := base_slug;
  
  LOOP
    -- Check if slug exists in the specified table
    IF table_name = 'projects' THEN
      SELECT EXISTS(
        SELECT 1 FROM projects 
        WHERE slug = final_slug 
        AND (exclude_id IS NULL OR id != exclude_id)
      ) INTO slug_exists;
    ELSIF table_name = 'job_postings' THEN
      SELECT EXISTS(
        SELECT 1 FROM job_postings 
        WHERE slug = final_slug 
        AND (exclude_id IS NULL OR id != exclude_id)
      ) INTO slug_exists;
    END IF;
    
    -- If slug doesn't exist, we can use it
    IF NOT slug_exists THEN
      EXIT;
    END IF;
    
    -- If slug exists, append counter and try again
    final_slug := base_slug || '-' || counter;
    counter := counter + 1;
  END LOOP;
  
  RETURN final_slug;
END;
$$ LANGUAGE plpgsql;

-- Generate slugs for existing projects
UPDATE projects 
SET slug = ensure_unique_slug(generate_slug(title), 'projects', id)
WHERE slug IS NULL;

-- Generate slugs for existing job postings
UPDATE job_postings 
SET slug = ensure_unique_slug(generate_slug(title), 'job_postings', id)
WHERE slug IS NULL;

-- Create function to automatically generate slug on insert/update
CREATE OR REPLACE FUNCTION auto_generate_slug()
RETURNS TRIGGER AS $$
BEGIN
  -- Only generate slug if it's not provided or if title changed
  IF NEW.slug IS NULL OR (TG_OP = 'UPDATE' AND OLD.title != NEW.title AND NEW.slug = OLD.slug) THEN
    NEW.slug := ensure_unique_slug(generate_slug(NEW.title), TG_TABLE_NAME, NEW.id);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to auto-generate slugs
CREATE TRIGGER projects_auto_slug
  BEFORE INSERT OR UPDATE ON projects
  FOR EACH ROW
  EXECUTE FUNCTION auto_generate_slug();

CREATE TRIGGER job_postings_auto_slug
  BEFORE INSERT OR UPDATE ON job_postings
  FOR EACH ROW
  EXECUTE FUNCTION auto_generate_slug();

-- Make slug columns NOT NULL after populating existing records
ALTER TABLE projects 
ALTER COLUMN slug SET NOT NULL;

ALTER TABLE job_postings 
ALTER COLUMN slug SET NOT NULL;

-- Create indexes for better performance
CREATE INDEX idx_projects_slug ON projects(slug);
CREATE INDEX idx_job_postings_slug ON job_postings(slug);

-- Add comments for documentation
COMMENT ON COLUMN projects.slug IS 'URL-friendly slug generated from title, used for SEO-friendly routing';
COMMENT ON COLUMN job_postings.slug IS 'URL-friendly slug generated from title, used for SEO-friendly routing';

COMMENT ON FUNCTION generate_slug(TEXT) IS 'Generates a URL-friendly slug from input text';
COMMENT ON FUNCTION ensure_unique_slug(TEXT, TEXT, UUID) IS 'Ensures slug uniqueness by appending numbers if needed';
COMMENT ON FUNCTION auto_generate_slug() IS 'Trigger function to automatically generate slugs on insert/update';

-- Example of generated slugs for reference:
-- "AI Chatbot for Local Language Support" -> "ai-chatbot-for-local-language-support"
-- "Junior Frontend Developer" -> "junior-frontend-developer"
-- "E-commerce Platform for Local Businesses" -> "e-commerce-platform-for-local-businesses"

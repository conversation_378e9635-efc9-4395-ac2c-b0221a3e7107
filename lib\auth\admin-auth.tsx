/**
 * Admin Authentication and Authorization System
 * Handles admin user authentication, role-based permissions, and session management
 */

'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase/client';

// =====================================================
// TYPES AND INTERFACES
// =====================================================

export type AdminUserRole = 'super_admin' | 'admin' | 'editor' | 'viewer';

export interface AdminUser {
  id: string;
  created_at: string;
  updated_at: string;
  email: string;
  supabase_user_id: string;
  first_name: string;
  last_name: string;
  role: AdminUserRole;
  is_active: boolean;
  is_verified: boolean;
  last_login_at?: string;
  login_count: number;
  failed_login_attempts: number;
  locked_until?: string;
  password_changed_at?: string;
  two_factor_enabled: boolean;
  profile_image_url?: string;
  phone?: string;
  timezone: string;
  language: string;
  created_by?: string;
  updated_by?: string;
}

export interface AdminAuthState {
  user: User | null;
  adminUser: AdminUser | null;
  loading: boolean;
  error: string | null;
}

export interface AdminAuthContextType extends AdminAuthState {
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: AdminUserRole | AdminUserRole[]) => boolean;
  refreshUser: () => Promise<void>;
}

// =====================================================
// PERMISSIONS SYSTEM
// =====================================================

export const ADMIN_PERMISSIONS = {
  // Dashboard permissions
  DASHBOARD_VIEW: 'dashboard:view',
  
  // Template permissions
  TEMPLATES_VIEW: 'templates:view',
  TEMPLATES_CREATE: 'templates:create',
  TEMPLATES_EDIT: 'templates:edit',
  TEMPLATES_DELETE: 'templates:delete',
  TEMPLATES_PUBLISH: 'templates:publish',
  
  // Service permissions
  SERVICES_VIEW: 'services:view',
  SERVICES_CREATE: 'services:create',
  SERVICES_EDIT: 'services:edit',
  SERVICES_DELETE: 'services:delete',
  SERVICES_PUBLISH: 'services:publish',
  
  // Blog permissions
  BLOG_VIEW: 'blog:view',
  BLOG_CREATE: 'blog:create',
  BLOG_EDIT: 'blog:edit',
  BLOG_DELETE: 'blog:delete',
  BLOG_PUBLISH: 'blog:publish',
  
  // Media permissions
  MEDIA_VIEW: 'media:view',
  MEDIA_UPLOAD: 'media:upload',
  MEDIA_EDIT: 'media:edit',
  MEDIA_DELETE: 'media:delete',
  
  // Lead permissions
  LEADS_VIEW: 'leads:view',
  LEADS_CREATE: 'leads:create',
  LEADS_EDIT: 'leads:edit',
  LEADS_DELETE: 'leads:delete',
  LEADS_ASSIGN: 'leads:assign',
  
  // Customer permissions
  CUSTOMERS_VIEW: 'customers:view',
  CUSTOMERS_CREATE: 'customers:create',
  CUSTOMERS_EDIT: 'customers:edit',
  CUSTOMERS_DELETE: 'customers:delete',
  
  // Analytics permissions
  ANALYTICS_VIEW: 'analytics:view',
  ANALYTICS_EXPORT: 'analytics:export',
  
  // User management permissions
  USERS_VIEW: 'users:view',
  USERS_CREATE: 'users:create',
  USERS_EDIT: 'users:edit',
  USERS_DELETE: 'users:delete',
  USERS_MANAGE_ROLES: 'users:manage_roles',
  
  // System permissions
  SYSTEM_SETTINGS: 'system:settings',
  SYSTEM_BACKUP: 'system:backup',
  SYSTEM_MAINTENANCE: 'system:maintenance',
} as const;

// Role-based permission mapping
const ROLE_PERMISSIONS: Record<AdminUserRole, string[]> = {
  super_admin: Object.values(ADMIN_PERMISSIONS),
  admin: [
    ADMIN_PERMISSIONS.DASHBOARD_VIEW,
    ADMIN_PERMISSIONS.TEMPLATES_VIEW,
    ADMIN_PERMISSIONS.TEMPLATES_CREATE,
    ADMIN_PERMISSIONS.TEMPLATES_EDIT,
    ADMIN_PERMISSIONS.TEMPLATES_DELETE,
    ADMIN_PERMISSIONS.TEMPLATES_PUBLISH,
    ADMIN_PERMISSIONS.SERVICES_VIEW,
    ADMIN_PERMISSIONS.SERVICES_CREATE,
    ADMIN_PERMISSIONS.SERVICES_EDIT,
    ADMIN_PERMISSIONS.SERVICES_DELETE,
    ADMIN_PERMISSIONS.SERVICES_PUBLISH,
    ADMIN_PERMISSIONS.BLOG_VIEW,
    ADMIN_PERMISSIONS.BLOG_CREATE,
    ADMIN_PERMISSIONS.BLOG_EDIT,
    ADMIN_PERMISSIONS.BLOG_DELETE,
    ADMIN_PERMISSIONS.BLOG_PUBLISH,
    ADMIN_PERMISSIONS.MEDIA_VIEW,
    ADMIN_PERMISSIONS.MEDIA_UPLOAD,
    ADMIN_PERMISSIONS.MEDIA_EDIT,
    ADMIN_PERMISSIONS.MEDIA_DELETE,
    ADMIN_PERMISSIONS.LEADS_VIEW,
    ADMIN_PERMISSIONS.LEADS_CREATE,
    ADMIN_PERMISSIONS.LEADS_EDIT,
    ADMIN_PERMISSIONS.LEADS_DELETE,
    ADMIN_PERMISSIONS.LEADS_ASSIGN,
    ADMIN_PERMISSIONS.CUSTOMERS_VIEW,
    ADMIN_PERMISSIONS.CUSTOMERS_CREATE,
    ADMIN_PERMISSIONS.CUSTOMERS_EDIT,
    ADMIN_PERMISSIONS.CUSTOMERS_DELETE,
    ADMIN_PERMISSIONS.ANALYTICS_VIEW,
    ADMIN_PERMISSIONS.ANALYTICS_EXPORT,
    ADMIN_PERMISSIONS.USERS_VIEW,
    ADMIN_PERMISSIONS.USERS_CREATE,
    ADMIN_PERMISSIONS.USERS_EDIT,
    ADMIN_PERMISSIONS.USERS_DELETE,
  ],
  editor: [
    ADMIN_PERMISSIONS.DASHBOARD_VIEW,
    ADMIN_PERMISSIONS.TEMPLATES_VIEW,
    ADMIN_PERMISSIONS.TEMPLATES_CREATE,
    ADMIN_PERMISSIONS.TEMPLATES_EDIT,
    ADMIN_PERMISSIONS.SERVICES_VIEW,
    ADMIN_PERMISSIONS.SERVICES_CREATE,
    ADMIN_PERMISSIONS.SERVICES_EDIT,
    ADMIN_PERMISSIONS.BLOG_VIEW,
    ADMIN_PERMISSIONS.BLOG_CREATE,
    ADMIN_PERMISSIONS.BLOG_EDIT,
    ADMIN_PERMISSIONS.MEDIA_VIEW,
    ADMIN_PERMISSIONS.MEDIA_UPLOAD,
    ADMIN_PERMISSIONS.MEDIA_EDIT,
    ADMIN_PERMISSIONS.LEADS_VIEW,
    ADMIN_PERMISSIONS.LEADS_EDIT,
    ADMIN_PERMISSIONS.CUSTOMERS_VIEW,
    ADMIN_PERMISSIONS.CUSTOMERS_EDIT,
    ADMIN_PERMISSIONS.ANALYTICS_VIEW,
  ],
  viewer: [
    ADMIN_PERMISSIONS.DASHBOARD_VIEW,
    ADMIN_PERMISSIONS.TEMPLATES_VIEW,
    ADMIN_PERMISSIONS.SERVICES_VIEW,
    ADMIN_PERMISSIONS.BLOG_VIEW,
    ADMIN_PERMISSIONS.MEDIA_VIEW,
    ADMIN_PERMISSIONS.LEADS_VIEW,
    ADMIN_PERMISSIONS.CUSTOMERS_VIEW,
    ADMIN_PERMISSIONS.ANALYTICS_VIEW,
  ],
};

// =====================================================
// CONTEXT AND PROVIDER
// =====================================================

const AdminAuthContext = createContext<AdminAuthContextType | undefined>(undefined);

interface AdminAuthProviderProps {
  children: ReactNode;
}

export function AdminAuthProvider({ children }: AdminAuthProviderProps) {
  const [state, setState] = useState<AdminAuthState>({
    user: null,
    adminUser: null,
    loading: true,
    error: null,
  });

  // Initialize auth state
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      // Get current session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        throw sessionError;
      }

      if (session?.user) {
        await loadAdminUser(session.user);
      } else {
        setState(prev => ({ ...prev, loading: false }));
      }

      // Listen for auth changes
      const { data: { subscription } } = supabase.auth.onAuthStateChange(
        async (event, session) => {
          if (event === 'SIGNED_IN' && session?.user) {
            await loadAdminUser(session.user);
          } else if (event === 'SIGNED_OUT') {
            setState({
              user: null,
              adminUser: null,
              loading: false,
              error: null,
            });
          }
        }
      );

      return () => subscription.unsubscribe();
    } catch (error) {
      console.error('Auth initialization error:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Authentication error',
      }));
    }
  };

  const loadAdminUser = async (user: User) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      // Fetch admin user data
      const { data: adminUser, error } = await supabase
        .from('admin_users')
        .select('*')
        .eq('supabase_user_id', user.id)
        .eq('is_active', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No admin user found
          throw new Error('Access denied. Admin account not found.');
        }
        throw error;
      }

      if (!adminUser) {
        throw new Error('Access denied. Admin account not found.');
      }

      // Update last login
      await supabase
        .from('admin_users')
        .update({
          last_login_at: new Date().toISOString(),
        })
        .eq('id', adminUser.id);

      setState({
        user,
        adminUser: {
          ...adminUser,
          last_login_at: new Date().toISOString(),
        },
        loading: false,
        error: null,
      });
    } catch (error) {
      console.error('Load admin user error:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to load admin user',
      }));
      
      // Sign out if admin user not found
      await supabase.auth.signOut();
    }
  };

  const signIn = async (email: string, password: string): Promise<{ success: boolean; error?: string }> => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        // Record failed login attempt
        await recordFailedLogin(email);
        throw error;
      }

      if (data.user) {
        await loadAdminUser(data.user);
        return { success: true };
      }

      throw new Error('Sign in failed');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Sign in failed';
      setState(prev => ({ ...prev, loading: false, error: errorMessage }));
      return { success: false, error: errorMessage };
    }
  };

  const signOut = async () => {
    try {
      setState(prev => ({ ...prev, loading: true }));
      
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        throw error;
      }

      setState({
        user: null,
        adminUser: null,
        loading: false,
        error: null,
      });
    } catch (error) {
      console.error('Sign out error:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Sign out failed',
      }));
    }
  };

  const hasPermission = (permission: string): boolean => {
    if (!state.adminUser) return false;
    
    const userPermissions = ROLE_PERMISSIONS[state.adminUser.role] || [];
    return userPermissions.includes(permission);
  };

  const hasRole = (role: AdminUserRole | AdminUserRole[]): boolean => {
    if (!state.adminUser) return false;
    
    if (Array.isArray(role)) {
      return role.includes(state.adminUser.role);
    }
    
    return state.adminUser.role === role;
  };

  const refreshUser = async () => {
    if (state.user) {
      await loadAdminUser(state.user);
    }
  };

  const recordFailedLogin = async (email: string) => {
    try {
      // This would typically record failed login attempts
      // For now, we'll just log it
      console.warn('Failed login attempt for:', email);
    } catch (error) {
      console.error('Failed to record login attempt:', error);
    }
  };

  const value: AdminAuthContextType = {
    ...state,
    signIn,
    signOut,
    hasPermission,
    hasRole,
    refreshUser,
  };

  return (
    <AdminAuthContext.Provider value={value}>
      {children}
    </AdminAuthContext.Provider>
  );
}

// =====================================================
// CUSTOM HOOKS
// =====================================================

export function useAdminAuth(): AdminAuthContextType {
  const context = useContext(AdminAuthContext);
  
  if (context === undefined) {
    throw new Error('useAdminAuth must be used within an AdminAuthProvider');
  }
  
  return context;
}

// =====================================================
// UTILITY FUNCTIONS
// =====================================================

export function getPermissionsForRole(role: AdminUserRole): string[] {
  return ROLE_PERMISSIONS[role] || [];
}

export function canUserAccessResource(
  userRole: AdminUserRole,
  requiredPermission: string
): boolean {
  const userPermissions = ROLE_PERMISSIONS[userRole] || [];
  return userPermissions.includes(requiredPermission);
}

export function isHigherRole(userRole: AdminUserRole, targetRole: AdminUserRole): boolean {
  const roleHierarchy: Record<AdminUserRole, number> = {
    viewer: 1,
    editor: 2,
    admin: 3,
    super_admin: 4,
  };
  
  return roleHierarchy[userRole] > roleHierarchy[targetRole];
}

export function getRoleDisplayName(role: AdminUserRole): string {
  const displayNames: Record<AdminUserRole, string> = {
    super_admin: 'Super Admin',
    admin: 'Admin',
    editor: 'Editor',
    viewer: 'Viewer',
  };
  
  return displayNames[role] || role;
}

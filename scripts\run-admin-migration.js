#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to run the admin dashboard migration
 * This creates all necessary tables for the admin dashboard
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  console.error('\nPlease check your .env.local file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runAdminMigration() {
  try {
    console.log('🚀 Starting admin dashboard migration...');
    
    // Read the migration files
    const schemaPath = path.join(__dirname, '../supabase/migrations/006_create_admin_dashboard_schema.sql');
    const functionsPath = path.join(__dirname, '../supabase/migrations/007_create_admin_dashboard_functions.sql');
    
    if (!fs.existsSync(schemaPath)) {
      throw new Error(`Migration file not found: ${schemaPath}`);
    }
    
    if (!fs.existsSync(functionsPath)) {
      throw new Error(`Migration file not found: ${functionsPath}`);
    }
    
    const schemaSQL = fs.readFileSync(schemaPath, 'utf8');
    const functionsSQL = fs.readFileSync(functionsPath, 'utf8');
    
    console.log('🎯 ADMIN DASHBOARD MIGRATION INSTRUCTIONS');
    console.log('==========================================\n');

    console.log('📋 STEP 1: Go to your Supabase SQL Editor');
    console.log('   🔗 https://supabase.com/dashboard/project/[your-project]/sql\n');

    console.log('📋 STEP 2: Copy and run the SCHEMA migration first:');
    console.log('   📁 File: supabase/migrations/006_create_admin_dashboard_schema.sql');
    console.log('   ⚠️  This creates all tables, indexes, and policies\n');

    console.log('📋 STEP 3: Copy and run the FUNCTIONS migration second:');
    console.log('   📁 File: supabase/migrations/007_create_admin_dashboard_functions.sql');
    console.log('   ⚠️  This creates database functions and procedures\n');

    console.log('📋 STEP 4: Create your first admin user:');
    console.log(`
-- Create Supabase Auth User
INSERT INTO auth.users (
  id,
  email,
  encrypted_password,
  email_confirmed_at,
  created_at,
  updated_at,
  raw_app_meta_data,
  raw_user_meta_data,
  is_super_admin,
  role
) VALUES (
  gen_random_uuid(),
  '<EMAIL>',
  crypt('your_secure_password', gen_salt('bf')),
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{}',
  false,
  'authenticated'
);

-- Link to Admin Users Table
INSERT INTO admin_users (
  email,
  supabase_user_id,
  first_name,
  last_name,
  role,
  is_active,
  is_verified
) VALUES (
  '<EMAIL>',
  (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
  'System',
  'Administrator',
  'super_admin',
  true,
  true
);
`);
    
    console.log('\n' + '='.repeat(80));
    console.log('🔗 Go to: https://supabase.com/dashboard/project/[your-project]/sql');
    console.log('📋 Copy and paste the SQL above, then click "Run"');
    console.log('\n📋 Summary of what will be created:');
    console.log('   ✓ Admin users and authentication tables');
    console.log('   ✓ Content management tables (templates, services, blog posts)');
    console.log('   ✓ Lead and customer management tables');
    console.log('   ✓ Analytics and reporting tables');
    console.log('   ✓ System administration tables');
    console.log('   ✓ Audit logging and email templates');
    console.log('   ✓ Performance indexes and RLS policies');
    console.log('   ✓ Database functions for enhanced functionality');
    console.log('   ✓ Initial system settings and admin user');
    
  } catch (error) {
    console.error('❌ Error reading migration files:', error);
    process.exit(1);
  }
}

// Alternative method to check if we can connect to Supabase
async function testConnection() {
  try {
    console.log('🔍 Testing Supabase connection...');
    
    const { data, error } = await supabase
      .from('projects')
      .select('count(*)')
      .limit(1);
    
    if (error) {
      console.error('❌ Connection test failed:', error);
      return false;
    }
    
    console.log('✅ Supabase connection successful!');
    return true;
  } catch (error) {
    console.error('❌ Connection test error:', error);
    return false;
  }
}

async function main() {
  console.log('🎯 iREME Soft Hub Admin Dashboard Migration');
  console.log('==========================================\n');
  
  // Test connection first
  const connected = await testConnection();
  
  if (!connected) {
    console.log('\n⚠️  Could not connect to Supabase automatically.');
    console.log('   This is normal if you haven\'t set up environment variables yet.');
    console.log('   Proceeding with manual migration instructions...\n');
  }
  
  // Run migration
  await runAdminMigration();
}

// Run the migration
main();

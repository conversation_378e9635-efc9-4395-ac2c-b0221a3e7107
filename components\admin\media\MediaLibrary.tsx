'use client';

import React, { useState, useEffect } from 'react';
import { AdminCard } from '@/components/admin/ui/AdminCard';
import AdminButton from '@/components/admin/ui/AdminButton';
import { AdminTableSkeleton } from '@/components/admin/ui/AdminLoading';
import { useAdminAuth, ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';
import { AdminPermissionWrapper } from '@/components/admin/auth/AdminProtectedRoute';
import { AdminMediaFile } from '@/lib/types/admin';
import {
  Upload,
  Search,
  Filter,
  Grid3X3,
  List,
  Image,
  File,
  Video,
  Music,
  Download,
  Trash2,
  Edit,
  Eye,
  Copy,
  FolderOpen,
} from 'lucide-react';

interface MediaLibraryProps {
  onSelect?: (file: AdminMediaFile) => void;
  selectionMode?: boolean;
  allowedTypes?: string[];
}

export default function MediaLibrary({ 
  onSelect, 
  selectionMode = false, 
  allowedTypes 
}: MediaLibraryProps) {
  const { hasPermission } = useAdminAuth();
  const [files, setFiles] = useState<AdminMediaFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [folderFilter, setFolderFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const itemsPerPage = 24;

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockFiles: AdminMediaFile[] = [
      {
        id: '1',
        created_at: '2024-01-15T10:00:00Z',
        updated_at: '2024-01-15T10:00:00Z',
        filename: 'hero-image.jpg',
        original_filename: 'hero-image.jpg',
        file_path: '/uploads/images/hero-image.jpg',
        file_url: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400',
        file_size: 245760,
        mime_type: 'image/jpeg',
        file_extension: 'jpg',
        width: 1200,
        height: 800,
        alt_text: 'Hero image for homepage',
        caption: 'Modern workspace setup',
        folder: 'images',
        tags: ['hero', 'workspace', 'modern'],
        usage_count: 5,
        last_used_at: '2024-01-20T15:30:00Z',
        seo_filename: 'hero-workspace-image',
        uploaded_by: 'admin-1',
      },
      {
        id: '2',
        created_at: '2024-01-14T14:30:00Z',
        updated_at: '2024-01-14T14:30:00Z',
        filename: 'template-preview.png',
        original_filename: 'template-preview.png',
        file_path: '/uploads/templates/template-preview.png',
        file_url: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=400',
        file_size: 156432,
        mime_type: 'image/png',
        file_extension: 'png',
        width: 800,
        height: 600,
        alt_text: 'Template preview screenshot',
        caption: 'Dashboard template preview',
        folder: 'templates',
        tags: ['template', 'dashboard', 'preview'],
        usage_count: 12,
        last_used_at: '2024-01-22T09:15:00Z',
        seo_filename: 'dashboard-template-preview',
        uploaded_by: 'admin-1',
      },
      // Add more mock files as needed
    ];

    setTimeout(() => {
      setFiles(mockFiles);
      setLoading(false);
      setTotalPages(1);
    }, 1000);
  }, [searchQuery, folderFilter, typeFilter, currentPage]);

  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) return <Image className="h-5 w-5" />;
    if (mimeType.startsWith('video/')) return <Video className="h-5 w-5" />;
    if (mimeType.startsWith('audio/')) return <Music className="h-5 w-5" />;
    return <File className="h-5 w-5" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleFileSelect = (file: AdminMediaFile) => {
    if (selectionMode && onSelect) {
      onSelect(file);
    } else {
      // Toggle selection for multi-select
      setSelectedFiles(prev => 
        prev.includes(file.id) 
          ? prev.filter(id => id !== file.id)
          : [...prev, file.id]
      );
    }
  };

  const handleUpload = () => {
    // TODO: Implement file upload
    console.log('Upload files');
  };

  const handleDelete = (fileId: string) => {
    if (confirm('Are you sure you want to delete this file?')) {
      setFiles(prev => prev.filter(file => file.id !== fileId));
    }
  };

  const copyFileUrl = (url: string) => {
    navigator.clipboard.writeText(url);
    // TODO: Show toast notification
  };

  const folders = ['all', 'images', 'templates', 'documents', 'videos'];
  const fileTypes = ['all', 'images', 'documents', 'videos', 'audio'];

  if (loading) {
    return <AdminTableSkeleton rows={6} columns={4} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      {!selectionMode && (
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 font-jost">Media Library</h1>
            <p className="text-gray-600 font-jost">Manage your images, documents, and media files</p>
          </div>
          
          <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.MEDIA_UPLOAD}>
            <AdminButton onClick={handleUpload} icon={<Upload className="h-4 w-4" />}>
              Upload Files
            </AdminButton>
          </AdminPermissionWrapper>
        </div>
      )}

      {/* Filters and Controls */}
      <AdminCard>
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search files..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
              />
            </div>
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-3">
            <select
              value={folderFilter}
              onChange={(e) => setFolderFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
            >
              {folders.map(folder => (
                <option key={folder} value={folder}>
                  {folder === 'all' ? 'All Folders' : folder}
                </option>
              ))}
            </select>

            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
            >
              {fileTypes.map(type => (
                <option key={type} value={type}>
                  {type === 'all' ? 'All Types' : type}
                </option>
              ))}
            </select>

            {/* View Mode Toggle */}
            <div className="flex items-center border border-gray-300 rounded-lg">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 ${viewMode === 'grid' ? 'bg-gray-100' : ''}`}
              >
                <Grid3X3 className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 ${viewMode === 'list' ? 'bg-gray-100' : ''}`}
              >
                <List className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </AdminCard>

      {/* Files Display */}
      {files.length > 0 ? (
        <>
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {files.map((file) => (
                <div
                  key={file.id}
                  className={`group relative bg-white border-2 rounded-lg overflow-hidden cursor-pointer transition-all duration-200 hover:shadow-md ${
                    selectedFiles.includes(file.id) ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-200'
                  }`}
                  onClick={() => handleFileSelect(file)}
                >
                  {/* File Preview */}
                  <div className="aspect-square bg-gray-100 flex items-center justify-center">
                    {file.mime_type.startsWith('image/') ? (
                      <img
                        src={file.file_url}
                        alt={file.alt_text || file.filename}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="text-gray-400">
                        {getFileIcon(file.mime_type)}
                      </div>
                    )}
                  </div>

                  {/* File Info */}
                  <div className="p-2">
                    <p className="text-xs font-medium text-gray-900 truncate font-jost">
                      {file.filename}
                    </p>
                    <p className="text-xs text-gray-500 font-jost">
                      {formatFileSize(file.file_size)}
                    </p>
                  </div>

                  {/* Actions Overlay */}
                  <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center space-x-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        copyFileUrl(file.file_url);
                      }}
                      className="p-2 bg-white rounded-full text-gray-600 hover:text-gray-900"
                    >
                      <Copy className="h-4 w-4" />
                    </button>
                    <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.MEDIA_DELETE}>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDelete(file.id);
                        }}
                        className="p-2 bg-white rounded-full text-red-600 hover:text-red-500"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </AdminPermissionWrapper>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <AdminCard padding="none">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 border-b border-gray-200">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-jost">
                        File
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-jost">
                        Size
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-jost">
                        Folder
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-jost">
                        Usage
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-jost">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {files.map((file) => (
                      <tr
                        key={file.id}
                        className={`hover:bg-gray-50 cursor-pointer ${
                          selectedFiles.includes(file.id) ? 'bg-blue-50' : ''
                        }`}
                        onClick={() => handleFileSelect(file)}
                      >
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10">
                              {file.mime_type.startsWith('image/') ? (
                                <img
                                  src={file.file_url}
                                  alt={file.alt_text || file.filename}
                                  className="h-10 w-10 rounded object-cover"
                                />
                              ) : (
                                <div className="h-10 w-10 bg-gray-100 rounded flex items-center justify-center">
                                  {getFileIcon(file.mime_type)}
                                </div>
                              )}
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900 font-jost">
                                {file.filename}
                              </div>
                              <div className="text-sm text-gray-500 font-jost">
                                {file.mime_type}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-jost">
                          {formatFileSize(file.file_size)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            <FolderOpen className="h-3 w-3 mr-1" />
                            {file.folder}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-jost">
                          {file.usage_count} times
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                copyFileUrl(file.file_url);
                              }}
                              className="text-gray-600 hover:text-gray-900"
                            >
                              <Copy className="h-4 w-4" />
                            </button>
                            <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.MEDIA_DELETE}>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDelete(file.id);
                                }}
                                className="text-red-600 hover:text-red-500"
                              >
                                <Trash2 className="h-4 w-4" />
                              </button>
                            </AdminPermissionWrapper>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </AdminCard>
          )}
        </>
      ) : (
        <AdminCard className="text-center py-12">
          <div className="mx-auto h-16 w-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <Image className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2 font-jost">No files found</h3>
          <p className="text-gray-600 mb-6 font-jost">
            {searchQuery || folderFilter !== 'all' || typeFilter !== 'all'
              ? 'Try adjusting your filters to see more results.'
              : 'Upload your first file to get started.'}
          </p>
          <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.MEDIA_UPLOAD}>
            <AdminButton onClick={handleUpload} icon={<Upload className="h-4 w-4" />}>
              Upload Files
            </AdminButton>
          </AdminPermissionWrapper>
        </AdminCard>
      )}

      {/* Selection Summary */}
      {selectedFiles.length > 0 && !selectionMode && (
        <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 bg-white border border-gray-200 rounded-lg shadow-lg p-4">
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium text-gray-900 font-jost">
              {selectedFiles.length} file{selectedFiles.length > 1 ? 's' : ''} selected
            </span>
            <div className="flex items-center space-x-2">
              <AdminButton size="sm" variant="outline">
                Download
              </AdminButton>
              <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.MEDIA_DELETE}>
                <AdminButton size="sm" variant="danger">
                  Delete
                </AdminButton>
              </AdminPermissionWrapper>
              <AdminButton 
                size="sm" 
                variant="ghost"
                onClick={() => setSelectedFiles([])}
              >
                Clear
              </AdminButton>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

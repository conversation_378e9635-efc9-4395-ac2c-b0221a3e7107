import { Metadata } from 'next';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import TemplateCard from '@/components/project/TemplateCard';
import { templates, getAllCategories } from '@/lib/data/templates';

export const metadata: Metadata = {
  title: 'Website Templates | iREME Soft Hub - Premium Template Showcase',
  description: 'Discover our collection of professionally designed website templates. Modern, responsive designs built with Next.js, TypeScript, and Tailwind CSS. Perfect for businesses, portfolios, e-commerce, and more.',
  keywords: [
    'website templates',
    'Next.js templates',
    'responsive web design',
    'modern website templates',
    'business templates',
    'portfolio templates',
    'e-commerce templates',
    'SaaS templates',
    'dashboard templates',
    'blog templates',
    'iREME Soft Hub',
    'premium templates'
  ],
  openGraph: {
    title: 'Website Templates | iREME Soft Hub - Premium Template Showcase',
    description: 'Discover our collection of professionally designed website templates. Modern, responsive designs built with cutting-edge technologies.',
    type: 'website',
    url: 'https://iremesofthub.com/project',
    siteName: 'iREME Soft Hub',
    images: [
      {
        url: '/templates/showcase-hero.jpg',
        width: 1200,
        height: 630,
        alt: 'iREME Soft Hub Website Templates Showcase',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Website Templates | iREME Soft Hub',
    description: 'Discover our collection of professionally designed website templates.',
    images: ['/templates/showcase-hero.jpg'],
    creator: '@iremesofthub',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: 'https://iremesofthub.com/project',
  },
};

export default function Project() {
  const categories = getAllCategories();

  // Structured data for SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": "Website Templates | iREME Soft Hub",
    "description": "Professional website templates collection featuring modern designs built with Next.js, TypeScript, and Tailwind CSS.",
    "url": "https://iremesofthub.com/project",
    "publisher": {
      "@type": "Organization",
      "name": "iREME Soft Hub",
      "url": "https://iremesofthub.com",
      "logo": {
        "@type": "ImageObject",
        "url": "https://iremesofthub.com/logo.png"
      }
    },
    "mainEntity": {
      "@type": "ItemList",
      "numberOfItems": templates.length,
      "itemListElement": templates.map((template, index) => ({
        "@type": "SoftwareApplication",
        "position": index + 1,
        "name": template.name,
        "description": template.shortDescription,
        "url": `https://iremesofthub.com/project/${template.slug}`,
        "applicationCategory": template.category,
        "operatingSystem": "Web Browser",
        "offers": {
          "@type": "Offer",
          "price": "0",
          "priceCurrency": "USD",
          "availability": "https://schema.org/InStock"
        }
      }))
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />

      <Header />
      <main className="flex-1 py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center bg-black text-white px-4 py-2 rounded-full text-sm mb-8 hover:scale-105 transition-all duration-300 cursor-pointer animate-fade-in font-jost">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
              Premium Website Templates - Ready to Deploy
            </div>

            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight animate-slide-up font-jost">
              Website Template
              <br />
              <span className="text-gray-600">Showcase</span>
            </h1>

            <p className="text-lg sm:text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed font-jost">
              Discover our collection of professionally designed website templates.
              <br className="hidden sm:block" />
              Each template is crafted with modern technologies and best practices.
            </p>

            {/* Category Pills */}
            <div className="flex flex-wrap justify-center gap-3 mb-12">
              <span className="px-4 py-2 bg-black text-white rounded-full text-sm font-medium font-jost">
                All Templates
              </span>
              {categories.slice(0, 5).map((category) => (
                <span
                  key={category}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium font-jost hover:bg-gray-200 transition-colors duration-200 cursor-pointer"
                >
                  {category}
                </span>
              ))}
            </div>
          </div>

          {/* Templates Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {templates.map((template) => (
              <TemplateCard
                key={template.id}
                template={template}
                className="animate-fade-in"
              />
            ))}
          </div>

          {/* Call to Action */}
          <div className="text-center bg-gray-50 rounded-3xl p-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4 font-jost">
              Need a Custom Solution?
            </h2>
            <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto font-jost">
              Can't find the perfect template? Our team can create a custom website tailored to your specific needs and requirements.
            </p>
            <button className="bg-black text-white px-8 py-4 rounded-full hover:bg-gray-800 transition-all duration-300 text-lg font-medium font-jost hover:shadow-lg transform hover:-translate-y-1">
              Get Custom Quote
            </button>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}

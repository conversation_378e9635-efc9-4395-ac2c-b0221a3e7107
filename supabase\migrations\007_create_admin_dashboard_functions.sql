-- =====================================================
-- ADMIN DASHBOARD DATABASE FUNCTIONS
-- Professional-grade functions for iREME Soft Hub Admin Dashboard
-- =====================================================

-- =====================================================
-- AUDIT LOGGING FUNCTION
-- =====================================================

-- Function to automatically log admin actions
CREATE OR REPLACE FUNCTION log_admin_action(
    p_action VARCHAR(100),
    p_resource_type VARCHAR(100),
    p_resource_id UUID DEFAULT NULL,
    p_resource_name VARCHAR(255) DEFAULT NULL,
    p_description TEXT DEFAULT NULL,
    p_old_values JSONB DEFAULT NULL,
    p_new_values JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    v_admin_user_id UUID;
    v_user_email VARCHAR(255);
    v_user_name VARCHAR(255);
    v_audit_id UUID;
BEGIN
    -- Get admin user information from current session
    SELECT id, email, first_name || ' ' || last_name
    INTO v_admin_user_id, v_user_email, v_user_name
    FROM admin_users 
    WHERE supabase_user_id = auth.uid();
    
    -- Insert audit log entry
    INSERT INTO admin_audit_logs (
        admin_user_id,
        user_email,
        user_name,
        action,
        resource_type,
        resource_id,
        resource_name,
        description,
        old_values,
        new_values,
        ip_address,
        user_agent
    ) VALUES (
        v_admin_user_id,
        v_user_email,
        v_user_name,
        p_action,
        p_resource_type,
        p_resource_id,
        p_resource_name,
        p_description,
        p_old_values,
        p_new_values,
        inet_client_addr(),
        current_setting('request.headers', true)::json->>'user-agent'
    ) RETURNING id INTO v_audit_id;
    
    RETURN v_audit_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- ANALYTICS FUNCTIONS
-- =====================================================

-- Function to get dashboard analytics summary
CREATE OR REPLACE FUNCTION get_dashboard_analytics(
    p_start_date DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
    p_end_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE (
    total_page_views BIGINT,
    unique_visitors BIGINT,
    avg_session_duration NUMERIC,
    bounce_rate NUMERIC,
    new_leads BIGINT,
    conversion_rate NUMERIC,
    total_revenue NUMERIC,
    avg_project_value NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(SUM(bm.total_page_views), 0) as total_page_views,
        COALESCE(SUM(bm.unique_visitors), 0) as unique_visitors,
        COALESCE(AVG(bm.avg_session_duration), 0) as avg_session_duration,
        COALESCE(AVG(bm.bounce_rate), 0) as bounce_rate,
        COALESCE(SUM(bm.total_leads), 0) as new_leads,
        COALESCE(AVG(bm.conversion_rate), 0) as conversion_rate,
        COALESCE(SUM(bm.total_revenue), 0) as total_revenue,
        COALESCE(AVG(bm.avg_project_value), 0) as avg_project_value
    FROM admin_business_metrics bm
    WHERE bm.date BETWEEN p_start_date AND p_end_date;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get top performing content
CREATE OR REPLACE FUNCTION get_top_performing_content(
    p_limit INTEGER DEFAULT 10,
    p_content_type VARCHAR(50) DEFAULT 'all'
)
RETURNS TABLE (
    content_type VARCHAR(50),
    content_id UUID,
    content_name VARCHAR(255),
    view_count BIGINT,
    engagement_score NUMERIC
) AS $$
BEGIN
    IF p_content_type = 'templates' OR p_content_type = 'all' THEN
        RETURN QUERY
        SELECT 
            'template'::VARCHAR(50) as content_type,
            t.id as content_id,
            t.name as content_name,
            t.view_count::BIGINT as view_count,
            (t.view_count + t.download_count * 2 + t.inquiry_count * 5)::NUMERIC as engagement_score
        FROM admin_templates t
        WHERE t.status = 'published'
        ORDER BY engagement_score DESC
        LIMIT p_limit;
    END IF;
    
    IF p_content_type = 'blog' OR p_content_type = 'all' THEN
        RETURN QUERY
        SELECT 
            'blog_post'::VARCHAR(50) as content_type,
            b.id as content_id,
            b.title as content_name,
            b.view_count::BIGINT as view_count,
            (b.view_count + b.like_count * 3 + b.share_count * 5)::NUMERIC as engagement_score
        FROM admin_blog_posts b
        WHERE b.status = 'published'
        ORDER BY engagement_score DESC
        LIMIT p_limit;
    END IF;
    
    IF p_content_type = 'services' OR p_content_type = 'all' THEN
        RETURN QUERY
        SELECT 
            'service'::VARCHAR(50) as content_type,
            s.id as content_id,
            s.title as content_name,
            s.view_count::BIGINT as view_count,
            (s.view_count + s.inquiry_count * 10)::NUMERIC as engagement_score
        FROM admin_services s
        WHERE s.status = 'active'
        ORDER BY engagement_score DESC
        LIMIT p_limit;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- LEAD MANAGEMENT FUNCTIONS
-- =====================================================

-- Function to calculate lead score
CREATE OR REPLACE FUNCTION calculate_lead_score(p_quote_request_id UUID)
RETURNS INTEGER AS $$
DECLARE
    v_score INTEGER := 0;
    v_quote_request admin_quote_requests%ROWTYPE;
BEGIN
    SELECT * INTO v_quote_request 
    FROM admin_quote_requests 
    WHERE id = p_quote_request_id;
    
    IF NOT FOUND THEN
        RETURN 0;
    END IF;
    
    -- Base score for having a quote request
    v_score := 10;
    
    -- Budget range scoring
    CASE v_quote_request.budget_range
        WHEN '$50,000+' THEN v_score := v_score + 50;
        WHEN '$25,000 - $50,000' THEN v_score := v_score + 40;
        WHEN '$10,000 - $25,000' THEN v_score := v_score + 30;
        WHEN '$5,000 - $10,000' THEN v_score := v_score + 20;
        ELSE v_score := v_score + 10;
    END CASE;
    
    -- Timeline urgency scoring
    CASE v_quote_request.timeline_urgency
        WHEN 'urgent' THEN v_score := v_score + 20;
        WHEN 'high' THEN v_score := v_score + 15;
        WHEN 'normal' THEN v_score := v_score + 10;
        ELSE v_score := v_score + 5;
    END CASE;
    
    -- Company information scoring
    IF v_quote_request.company_name IS NOT NULL AND LENGTH(v_quote_request.company_name) > 0 THEN
        v_score := v_score + 10;
    END IF;
    
    IF v_quote_request.website IS NOT NULL AND LENGTH(v_quote_request.website) > 0 THEN
        v_score := v_score + 10;
    END IF;
    
    -- Project description quality scoring
    IF LENGTH(v_quote_request.project_description) > 200 THEN
        v_score := v_score + 15;
    ELSIF LENGTH(v_quote_request.project_description) > 100 THEN
        v_score := v_score + 10;
    ELSE
        v_score := v_score + 5;
    END IF;
    
    -- Contact attempts penalty (too many attempts might indicate low interest)
    IF v_quote_request.contact_attempts > 5 THEN
        v_score := v_score - 10;
    ELSIF v_quote_request.contact_attempts > 3 THEN
        v_score := v_score - 5;
    END IF;
    
    -- Update the lead score in the database
    UPDATE admin_quote_requests 
    SET lead_score = v_score 
    WHERE id = p_quote_request_id;
    
    RETURN v_score;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get leads requiring follow-up
CREATE OR REPLACE FUNCTION get_leads_requiring_followup()
RETURNS TABLE (
    id UUID,
    full_name VARCHAR(255),
    email VARCHAR(255),
    company_name VARCHAR(255),
    project_type VARCHAR(100),
    status VARCHAR(50),
    priority VARCHAR(50),
    lead_score INTEGER,
    next_follow_up_date DATE,
    days_overdue INTEGER,
    assigned_to_name VARCHAR(255)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        qr.id,
        qr.full_name,
        qr.email,
        qr.company_name,
        qr.project_type,
        qr.status,
        qr.priority,
        qr.lead_score,
        qr.next_follow_up_date,
        CASE 
            WHEN qr.next_follow_up_date < CURRENT_DATE THEN 
                CURRENT_DATE - qr.next_follow_up_date
            ELSE 0
        END as days_overdue,
        COALESCE(au.first_name || ' ' || au.last_name, 'Unassigned') as assigned_to_name
    FROM admin_quote_requests qr
    LEFT JOIN admin_users au ON qr.assigned_to = au.id
    WHERE qr.next_follow_up_date IS NOT NULL
    AND qr.status NOT IN ('won', 'lost')
    ORDER BY 
        CASE WHEN qr.next_follow_up_date < CURRENT_DATE THEN 0 ELSE 1 END,
        qr.next_follow_up_date ASC,
        qr.lead_score DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- CONTENT MANAGEMENT FUNCTIONS
-- =====================================================

-- Function to generate unique slug
CREATE OR REPLACE FUNCTION generate_unique_slug(
    p_title VARCHAR(255),
    p_table_name VARCHAR(100)
)
RETURNS VARCHAR(255) AS $$
DECLARE
    v_base_slug VARCHAR(255);
    v_slug VARCHAR(255);
    v_counter INTEGER := 0;
    v_exists BOOLEAN;
BEGIN
    -- Create base slug from title
    v_base_slug := lower(trim(regexp_replace(p_title, '[^a-zA-Z0-9\s]', '', 'g')));
    v_base_slug := regexp_replace(v_base_slug, '\s+', '-', 'g');
    v_base_slug := trim(v_base_slug, '-');
    
    -- Limit length
    IF length(v_base_slug) > 200 THEN
        v_base_slug := substring(v_base_slug, 1, 200);
    END IF;
    
    v_slug := v_base_slug;
    
    -- Check for uniqueness and increment if needed
    LOOP
        EXECUTE format('SELECT EXISTS(SELECT 1 FROM %I WHERE slug = $1)', p_table_name) 
        USING v_slug INTO v_exists;
        
        IF NOT v_exists THEN
            EXIT;
        END IF;
        
        v_counter := v_counter + 1;
        v_slug := v_base_slug || '-' || v_counter;
    END LOOP;
    
    RETURN v_slug;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

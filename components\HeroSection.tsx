'use client';

import React from 'react';
import Link from 'next/link';
import { ArrowRight, Play } from 'lucide-react';

const HeroSection = () => {
  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      <div className="max-w-7xl mx-auto">
        <div className="text-center">
          {/* Badge */}
          <div className="inline-flex items-center bg-black text-white px-4 py-2 rounded-full text-sm mb-8 hover:scale-105 transition-all duration-300 cursor-pointer animate-fade-in font-jost">
            <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
            Simple Transparent Pricing - Seamless
          </div>

          {/* Main Heading */}
          <h1 
            className="text-1xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight animate-slide-up font-jost"
          >
            Dominate the Market with Bold Tech<br />
            Elite App Builders
          </h1>

          {/* Subtext */}
          <p 
            className="text-lg sm:text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed font-jost"
          >
            We Turn Your Ideas Into Scalable Solutions of your financial future with
            <br className="hidden sm:block" />
            advanced tools and personalized insights
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-16">
            <Link
              href="/get-quote"
              className="bg-black text-white px-8 py-4 rounded-full hover:bg-gray-800 transition-all duration-500 flex items-center gap-2 text-base sm:text-lg font-medium group relative overflow-hidden hover:shadow-lg transform hover:-translate-y-1 font-jost"
            >
              <span className="relative z-10 transition-transform duration-500 group-hover:translate-x-[-8px] text-sm sm:text-base">
                Get Your Free Quote
              </span>
              <ArrowRight
                size={20}
                className="relative z-10 transition-all duration-500 group-hover:translate-x-[8px] group-hover:scale-125"
              />
              <div className="absolute inset-0 bg-gray-800 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-500 ease-in-out"></div>
            </Link>

            <button
              className="border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-full hover:border-gray-400 hover:bg-gray-50 transition-all duration-500 flex items-center gap-2 text-sm sm:text-lg font-medium hover:shadow-lg transform hover:-translate-y-1 font-jost"
            >
              <Play size={18} className="sm:w-5 sm:h-5 transition-transform duration-300 group-hover:scale-125" />
              Explore Features
            </button>
          </div>

          {/* Floating Elements with enhanced animations */}
          <div className="absolute top-20 left-10 w-20 h-20 bg-blue-100 rounded-full opacity-50 animate-float-slow hidden lg:block"></div>
          <div className="absolute top-40 right-20 w-16 h-16 bg-green-100 rounded-full opacity-50 animate-float-medium hidden lg:block"></div>
          <div className="absolute bottom-20 left-20 w-12 h-12 bg-yellow-100 rounded-full opacity-50 animate-float-fast hidden lg:block"></div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;

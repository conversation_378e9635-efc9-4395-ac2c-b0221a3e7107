/**
 * Advanced Caching System for Admin Dashboard
 * Implements multiple caching strategies for optimal performance
 */

interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  ttl: number;
  key: string;
  tags?: string[];
}

interface CacheOptions {
  ttl?: number; // Time to live in milliseconds
  tags?: string[]; // Tags for cache invalidation
  serialize?: boolean; // Whether to serialize data
  compress?: boolean; // Whether to compress data
}

interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  size: number;
  hitRate: number;
}

class CacheManager {
  private cache = new Map<string, CacheEntry>();
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    size: 0,
    hitRate: 0,
  };
  private maxSize: number = 1000;
  private defaultTTL: number = 5 * 60 * 1000; // 5 minutes
  private cleanupInterval: NodeJS.Timeout;

  constructor(maxSize: number = 1000, defaultTTL: number = 5 * 60 * 1000) {
    this.maxSize = maxSize;
    this.defaultTTL = defaultTTL;
    
    // Start cleanup interval
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 60000); // Cleanup every minute
  }

  /**
   * Get item from cache
   */
  get<T = any>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }

    // Check if expired
    if (Date.now() > entry.timestamp + entry.ttl) {
      this.cache.delete(key);
      this.stats.misses++;
      this.stats.size--;
      this.updateHitRate();
      return null;
    }

    this.stats.hits++;
    this.updateHitRate();
    return entry.data;
  }

  /**
   * Set item in cache
   */
  set<T = any>(key: string, data: T, options: CacheOptions = {}): void {
    const ttl = options.ttl || this.defaultTTL;
    const tags = options.tags || [];

    // Serialize data if requested
    let processedData = data;
    if (options.serialize && typeof data === 'object') {
      processedData = JSON.parse(JSON.stringify(data)) as T;
    }

    // Compress data if requested (simplified compression)
    if (options.compress && typeof processedData === 'string') {
      // In a real implementation, you'd use a compression library
      processedData = processedData as T;
    }

    const entry: CacheEntry<T> = {
      data: processedData,
      timestamp: Date.now(),
      ttl,
      key,
      tags,
    };

    // Check if we need to evict items
    if (this.cache.size >= this.maxSize) {
      this.evictLRU();
    }

    this.cache.set(key, entry);
    this.stats.sets++;
    this.stats.size = this.cache.size;
  }

  /**
   * Delete item from cache
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.stats.deletes++;
      this.stats.size = this.cache.size;
    }
    return deleted;
  }

  /**
   * Check if key exists in cache
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    // Check if expired
    if (Date.now() > entry.timestamp + entry.ttl) {
      this.cache.delete(key);
      this.stats.size--;
      return false;
    }
    
    return true;
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
    this.stats.size = 0;
  }

  /**
   * Invalidate cache entries by tags
   */
  invalidateByTags(tags: string[]): number {
    let invalidated = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.tags && entry.tags.some(tag => tags.includes(tag))) {
        this.cache.delete(key);
        invalidated++;
      }
    }
    
    this.stats.size = this.cache.size;
    return invalidated;
  }

  /**
   * Get or set pattern - fetch data if not in cache
   */
  async getOrSet<T = any>(
    key: string,
    fetcher: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T> {
    const cached = this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    const data = await fetcher();
    this.set(key, data, options);
    return data;
  }

  /**
   * Memoize function calls
   */
  memoize<TArgs extends any[], TReturn>(
    fn: (...args: TArgs) => Promise<TReturn>,
    keyGenerator?: (...args: TArgs) => string,
    options: CacheOptions = {}
  ) {
    return async (...args: TArgs): Promise<TReturn> => {
      const key = keyGenerator ? keyGenerator(...args) : `memoized_${JSON.stringify(args)}`;
      return this.getOrSet(key, () => fn(...args), options);
    };
  }

  /**
   * Cleanup expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.timestamp + entry.ttl) {
        this.cache.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      this.stats.size = this.cache.size;
    }
  }

  /**
   * Evict least recently used item
   */
  private evictLRU(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.stats.size = this.cache.size;
    }
  }

  /**
   * Update hit rate
   */
  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0;
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * Get cache keys
   */
  getKeys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * Get cache entries by tag
   */
  getEntriesByTag(tag: string): CacheEntry[] {
    const entries: CacheEntry[] = [];
    
    for (const entry of this.cache.values()) {
      if (entry.tags && entry.tags.includes(tag)) {
        entries.push(entry);
      }
    }
    
    return entries;
  }

  /**
   * Destroy cache manager
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.clear();
  }
}

// Create cache instances for different data types
export const adminCache = new CacheManager(500, 10 * 60 * 1000); // 10 minutes TTL
export const dataCache = new CacheManager(1000, 5 * 60 * 1000); // 5 minutes TTL
export const queryCache = new CacheManager(200, 2 * 60 * 1000); // 2 minutes TTL

// Cache keys constants
export const CACHE_KEYS = {
  // Admin data
  ADMIN_USERS: 'admin:users',
  ADMIN_PERMISSIONS: 'admin:permissions',
  ADMIN_SETTINGS: 'admin:settings',
  
  // Content data
  TEMPLATES: 'content:templates',
  SERVICES: 'content:services',
  BLOG_POSTS: 'content:blog_posts',
  MEDIA_FILES: 'content:media_files',
  
  // Lead data
  QUOTE_REQUESTS: 'leads:quote_requests',
  CUSTOMERS: 'leads:customers',
  FOLLOW_UPS: 'leads:follow_ups',
  
  // Analytics data
  DASHBOARD_STATS: 'analytics:dashboard_stats',
  CONTENT_PERFORMANCE: 'analytics:content_performance',
  
  // System data
  SYSTEM_STATUS: 'system:status',
  AUDIT_LOGS: 'system:audit_logs',
} as const;

// Cache tags for invalidation
export const CACHE_TAGS = {
  ADMIN: 'admin',
  CONTENT: 'content',
  LEADS: 'leads',
  ANALYTICS: 'analytics',
  SYSTEM: 'system',
  USER_SPECIFIC: 'user_specific',
} as const;

// Utility functions
export function generateCacheKey(prefix: string, ...parts: (string | number)[]): string {
  return `${prefix}:${parts.join(':')}`;
}

export function invalidateUserCache(userId: string): void {
  adminCache.invalidateByTags([`user:${userId}`]);
  dataCache.invalidateByTags([`user:${userId}`]);
}

export function invalidateContentCache(): void {
  dataCache.invalidateByTags([CACHE_TAGS.CONTENT]);
}

export function invalidateAnalyticsCache(): void {
  dataCache.invalidateByTags([CACHE_TAGS.ANALYTICS]);
}

// React hook for cached data
export function useCachedData<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: CacheOptions & { enabled?: boolean } = {}
) {
  const [data, setData] = React.useState<T | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<Error | null>(null);

  React.useEffect(() => {
    if (!options.enabled) {
      setLoading(false);
      return;
    }

    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const result = await dataCache.getOrSet(key, fetcher, options);
        setData(result);
      } catch (err) {
        setError(err as Error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [key, options.enabled]);

  const refetch = React.useCallback(async () => {
    dataCache.delete(key);
    try {
      setLoading(true);
      setError(null);
      const result = await dataCache.getOrSet(key, fetcher, options);
      setData(result);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [key, fetcher, options]);

  return { data, loading, error, refetch };
}

export default CacheManager;

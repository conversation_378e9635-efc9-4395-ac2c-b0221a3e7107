import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Types for database tables
export interface Project {
  id: string;
  created_at: string;
  updated_at: string;
  title: string;
  slug: string;
  description: string;
  preview_description?: string;
  technologies: string[];
  duration: string;
  difficulty_level: 'Beginner' | 'Intermediate' | 'Advanced';
  required_skills: string[];
  is_active: boolean;
  application_deadline?: string;
  max_collaborators?: number;
}

export interface JobPosting {
  id: string;
  created_at: string;
  updated_at: string;
  title: string;
  slug: string;
  description: string;
  preview_description?: string;
  employment_type: 'Full-time' | 'Part-time' | 'Contract';
  experience_level: 'Junior' | 'Mid' | 'Senior';
  location_type: 'Remote' | 'On-site' | 'Hybrid';
  compensation_range: string;
  required_skills: string[];
  is_active: boolean;
  application_deadline?: string;
}

export interface ProjectApplicationData {
  full_name: string;
  email: string;
  phone?: string | null;
  location: string;
  professional_title?: string | null;
  github_link: string;
  linkedin_link?: string | null;
  portfolio_link?: string | null;
  years_experience?: string | null;
  primary_skills: string[];
  project_interest: string;
  motivation_statement: string;
  availability_hours?: string | null;
  expected_start_date?: string | null;
  additional_comments?: string | null;
  project_id?: string | null;
}

export interface JobApplicationData {
  full_name: string;
  email: string;
  phone: string;
  location: string;
  current_position?: string;
  resume_file_url: string;
  github_link: string;
  linkedin_link?: string;
  portfolio_link?: string;
  years_experience?: string;
  salary_expectation?: string;
  position_applied_for: string;
  cover_letter: string;
  availability_start_date?: string;
  preferred_work_arrangement?: string;
  additional_information?: string;
  job_posting_id?: string;
}

// Project-related functions
export async function getActiveProjects(): Promise<Project[]> {
  try {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching projects:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in getActiveProjects:', error);
    throw error;
  }
}

export async function getProjectById(id: string): Promise<Project | null> {
  try {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('id', id)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Error fetching project:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in getProjectById:', error);
    return null;
  }
}

// Job posting-related functions
export async function getActiveJobPostings(): Promise<JobPosting[]> {
  try {
    const { data, error } = await supabase
      .from('job_postings')
      .select('*')
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching job postings:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in getActiveJobPostings:', error);
    throw error;
  }
}

export async function getJobPostingById(id: string): Promise<JobPosting | null> {
  try {
    const { data, error } = await supabase
      .from('job_postings')
      .select('*')
      .eq('id', id)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Error fetching job posting:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in getJobPostingById:', error);
    return null;
  }
}

// Application submission functions
export async function submitProjectApplication(applicationData: ProjectApplicationData): Promise<{ success: boolean; error?: string; id?: string }> {
  try {
    // Clean and validate the data before submission
    const cleanData = {
      full_name: applicationData.full_name || '',
      email: applicationData.email || '',
      phone: applicationData.phone || null,
      location: applicationData.location || '',
      professional_title: applicationData.professional_title || null,
      github_link: applicationData.github_link || '',
      linkedin_link: applicationData.linkedin_link || null,
      portfolio_link: applicationData.portfolio_link || null,
      years_experience: applicationData.years_experience || null,
      primary_skills: applicationData.primary_skills || [],
      project_interest: applicationData.project_interest || '',
      motivation_statement: applicationData.motivation_statement || '',
      availability_hours: applicationData.availability_hours || null,
      expected_start_date: applicationData.expected_start_date || null,
      additional_comments: applicationData.additional_comments || null,
      project_id: applicationData.project_id || null
    };

    console.log('Submitting project application data:', cleanData);

    const { data, error } = await supabase
      .from('project_applications')
      .insert([cleanData])
      .select('id')
      .single();

    if (error) {
      console.error('Supabase error submitting project application:', error);
      return { success: false, error: error.message };
    }

    console.log('Project application submitted successfully:', data);
    return { success: true, id: data.id };
  } catch (error) {
    console.error('Error in submitProjectApplication:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

export async function submitJobApplication(applicationData: JobApplicationData): Promise<{ success: boolean; error?: string; id?: string }> {
  try {
    // Clean and validate the data before submission
    const cleanData = {
      full_name: applicationData.full_name || '',
      email: applicationData.email || '',
      phone: applicationData.phone || '',
      location: applicationData.location || '',
      current_position: applicationData.current_position || null,
      resume_file_url: applicationData.resume_file_url || '',
      github_link: applicationData.github_link || '',
      linkedin_link: applicationData.linkedin_link || null,
      portfolio_link: applicationData.portfolio_link || null,
      years_experience: applicationData.years_experience || null,
      salary_expectation: applicationData.salary_expectation || null,
      position_applied_for: applicationData.position_applied_for || '',
      cover_letter: applicationData.cover_letter || '',
      availability_start_date: applicationData.availability_start_date || null,
      preferred_work_arrangement: applicationData.preferred_work_arrangement || null,
      additional_information: applicationData.additional_information || null,
      job_posting_id: applicationData.job_posting_id || null
    };

    console.log('Submitting job application data:', cleanData);

    const { data, error } = await supabase
      .from('job_applications')
      .insert([cleanData])
      .select('id')
      .single();

    if (error) {
      console.error('Supabase error submitting job application:', error);
      return { success: false, error: error.message };
    }

    console.log('Job application submitted successfully:', data);
    return { success: true, id: data.id };
  } catch (error) {
    console.error('Error in submitJobApplication:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// File upload function for resumes
export async function uploadResumeFile(file: File, applicationId: string): Promise<{ success: boolean; url?: string; error?: string }> {
  try {
    const fileExt = file.name.split('.').pop();
    const fileName = `${applicationId}-${Date.now()}.${fileExt}`;
    const filePath = `resumes/${fileName}`;

    const { error } = await supabase.storage
      .from('applications')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (error) {
      console.error('Error uploading file:', error);
      return { success: false, error: error.message };
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from('applications')
      .getPublicUrl(filePath);

    return { success: true, url: urlData.publicUrl };
  } catch (error) {
    console.error('Error in uploadResumeFile:', error);
    return { success: false, error: 'An unexpected error occurred during file upload' };
  }
}

// Search and filter functions
export async function searchProjects(searchTerm: string, filters?: {
  technologies?: string[];
  difficulty?: string;
  duration?: string;
}): Promise<Project[]> {
  try {
    let query = supabase
      .from('projects')
      .select('*')
      .eq('is_active', true);

    // Add search term filter
    if (searchTerm) {
      query = query.or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
    }

    // Add difficulty filter
    if (filters?.difficulty) {
      query = query.eq('difficulty_level', filters.difficulty);
    }

    // Add technology filter
    if (filters?.technologies && filters.technologies.length > 0) {
      query = query.overlaps('technologies', filters.technologies);
    }

    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) {
      console.error('Error searching projects:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in searchProjects:', error);
    throw error;
  }
}

export async function searchJobPostings(searchTerm: string, filters?: {
  employment_type?: string;
  experience_level?: string;
  location_type?: string;
}): Promise<JobPosting[]> {
  try {
    let query = supabase
      .from('job_postings')
      .select('*')
      .eq('is_active', true);

    // Add search term filter
    if (searchTerm) {
      query = query.or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
    }

    // Add filters
    if (filters?.employment_type) {
      query = query.eq('employment_type', filters.employment_type);
    }

    if (filters?.experience_level) {
      query = query.eq('experience_level', filters.experience_level);
    }

    if (filters?.location_type) {
      query = query.eq('location_type', filters.location_type);
    }

    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) {
      console.error('Error searching job postings:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in searchJobPostings:', error);
    throw error;
  }
}

// Utility function to validate email
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Utility function to validate URL
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

@echo off
title iREME Soft Hub Admin Dashboard Startup
color 0A

echo ===============================================
echo    iREME Soft Hub Admin Dashboard Startup
echo ===============================================
echo.

REM Change to project directory
cd /d "C:\Users\<USER>\Desktop\iremesofthub"
echo Current directory: %CD%
echo.

REM Check if Node.js is installed
echo [1/5] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed or not in PATH
    echo.
    echo Please install Node.js from: https://nodejs.org/
    echo After installation, restart this script.
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Node.js is available
    node --version
)
echo.

REM Check if npm is available
echo [2/5] Checking npm availability...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not available
    pause
    exit /b 1
) else (
    echo ✅ npm is available
    npm --version
)
echo.

REM Check if package.json exists
echo [3/5] Checking project files...
if not exist "package.json" (
    echo ❌ package.json not found in current directory
    echo Make sure you're in the correct project directory
    pause
    exit /b 1
) else (
    echo ✅ package.json found
)
echo.

REM Install dependencies if node_modules doesn't exist
echo [4/5] Checking dependencies...
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    echo This may take a few minutes...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies
        echo.
        echo Trying alternative installation...
        npm install --legacy-peer-deps
        if %errorlevel% neq 0 (
            echo ❌ Alternative installation also failed
            echo Please check your internet connection and try again
            pause
            exit /b 1
        )
    )
    echo ✅ Dependencies installed successfully
) else (
    echo ✅ Dependencies already installed
)
echo.

REM Start the development server
echo [5/5] Starting development server...
echo.
echo 🚀 Starting iREME Soft Hub Admin Dashboard...
echo.
echo ===============================================
echo    SERVER INFORMATION
echo ===============================================
echo Local URL:    http://localhost:3000
echo Admin Panel:  http://localhost:3000/admin
echo.
echo Default Admin Credentials:
echo Email:        <EMAIL>
echo Password:     admin123
echo.
echo ===============================================
echo.
echo Press Ctrl+C to stop the server
echo.

REM Start the server
npm run dev

REM If we reach here, the server stopped
echo.
echo Server stopped. Press any key to exit...
pause >nul

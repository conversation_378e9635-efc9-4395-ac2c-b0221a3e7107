'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import AdminProtectedRoute from '@/components/admin/auth/AdminProtectedRoute';
import AdminLayout from '@/components/admin/layout/AdminLayout';
import QuoteRequestList from '@/components/admin/leads/QuoteRequestList';
import { ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';
import { AdminQuoteRequest } from '@/lib/types/admin';

export default function AdminQuoteRequestsPage() {
  const router = useRouter();

  const handleView = (request: AdminQuoteRequest) => {
    router.push(`/admin/leads/quotes/${request.id}`);
  };

  const handleEdit = (request: AdminQuoteRequest) => {
    router.push(`/admin/leads/quotes/${request.id}/edit`);
  };

  return (
    <AdminProtectedRoute requiredPermission={ADMIN_PERMISSIONS.LEADS_VIEW}>
      <AdminLayout 
        title="Quote Requests" 
        subtitle="Manage and track your project quote requests"
      >
        <QuoteRequestList onView={handleView} onEdit={handleEdit} />
      </AdminLayout>
    </AdminProtectedRoute>
  );
}

'use client';

import React, { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import Link from 'next/link';
import { 
  ArrowRight, 
  ShoppingCart, 
  CreditCard, 
  Package, 
  BarChart3, 
  Shield, 
  Zap,
  CheckCircle,
  Clock,
  Users,
  ChevronDown,
  ChevronUp,
  TrendingUp,
  Globe,
  Smartphone
} from 'lucide-react';

interface Feature {
  title: string;
  description: string;
  icon: React.ReactNode;
}

interface Package {
  name: string;
  price: string;
  description: string;
  features: string[];
  timeline: string;
  popular?: boolean;
}

export default function EcommercePage() {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  const features: Feature[] = [
    {
      title: 'Custom E-commerce Development',
      description: 'Tailored online stores built with modern technologies for optimal performance and user experience.',
      icon: <ShoppingCart className="w-6 h-6" />
    },
    {
      title: 'Payment Gateway Integration',
      description: 'Secure payment processing with support for multiple payment methods and currencies.',
      icon: <CreditCard className="w-6 h-6" />
    },
    {
      title: 'Inventory Management',
      description: 'Advanced inventory tracking, automated stock alerts, and multi-warehouse support.',
      icon: <Package className="w-6 h-6" />
    },
    {
      title: 'Mobile Commerce',
      description: 'Mobile-optimized shopping experiences that convert on all devices and screen sizes.',
      icon: <Smartphone className="w-6 h-6" />
    },
    {
      title: 'Analytics & Reporting',
      description: 'Comprehensive sales analytics, customer insights, and performance tracking.',
      icon: <BarChart3 className="w-6 h-6" />
    },
    {
      title: 'Multi-channel Selling',
      description: 'Sell across multiple platforms including social media, marketplaces, and your website.',
      icon: <Globe className="w-6 h-6" />
    }
  ];

  const packages: Package[] = [
    {
      name: 'Starter Store',
      price: '$8,000',
      description: 'Perfect for small businesses launching their first online store with essential features.',
      features: [
        'Up to 100 products',
        'Responsive design',
        'Payment gateway integration',
        'Basic inventory management',
        'SSL certificate',
        'SEO optimization',
        '30 days support'
      ],
      timeline: '4-6 weeks'
    },
    {
      name: 'Business E-commerce',
      price: '$18,000',
      description: 'Comprehensive e-commerce solution for growing businesses with advanced features.',
      features: [
        'Unlimited products',
        'Custom design & branding',
        'Advanced inventory management',
        'Multi-payment options',
        'Customer accounts & wishlist',
        'Analytics & reporting',
        'Mobile app integration',
        '60 days support'
      ],
      timeline: '8-12 weeks',
      popular: true
    },
    {
      name: 'Enterprise E-commerce',
      price: '$35,000+',
      description: 'Advanced e-commerce platform for large businesses with complex requirements.',
      features: [
        'Multi-store management',
        'B2B & B2C capabilities',
        'Advanced integrations',
        'Custom functionality',
        'Multi-language & currency',
        'Advanced security',
        'Performance optimization',
        '90 days support'
      ],
      timeline: '12-20 weeks'
    }
  ];

  const faqs = [
    {
      question: 'Which e-commerce platform do you recommend?',
      answer: 'We recommend platforms based on your specific needs. Shopify is great for quick launches, WooCommerce offers flexibility, and custom solutions provide maximum control. We help you choose the best platform for your business goals and budget.'
    },
    {
      question: 'How do you ensure my online store is secure?',
      answer: 'We implement multiple security layers including SSL certificates, secure payment processing, regular security updates, and compliance with PCI DSS standards. Your customers\' data and transactions are fully protected.'
    },
    {
      question: 'Can you integrate with my existing systems?',
      answer: 'Yes! We can integrate your e-commerce store with existing CRM, ERP, accounting software, inventory management systems, and other business tools to streamline your operations.'
    },
    {
      question: 'Do you provide ongoing maintenance and support?',
      answer: 'Absolutely! We offer comprehensive maintenance packages including security updates, performance optimization, feature additions, and technical support to keep your store running smoothly.'
    },
    {
      question: 'How do you optimize for conversions?',
      answer: 'We use proven conversion optimization techniques including streamlined checkout processes, trust signals, product recommendations, abandoned cart recovery, and A/B testing to maximize your sales.'
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden bg-gradient-to-br from-green-50 to-white">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="inline-flex items-center bg-green-600 text-white px-4 py-2 rounded-full text-sm mb-6 font-jost">
                <ShoppingCart className="w-4 h-4 mr-2" />
                E-commerce Solutions
              </div>
              
              <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-black mb-6 leading-tight font-jost">
                E-commerce Stores That 
                <span className="text-green-600"> Convert & Scale</span>
              </h1>
              
              <p className="text-xl text-gray-600 mb-8 leading-relaxed font-jost">
                We build high-converting e-commerce platforms that turn browsers into buyers. 
                From custom online stores to marketplace integrations, we create shopping experiences that drive sales and grow your business.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <Link
                  href="/get-quote"
                  className="bg-black text-white px-8 py-4 rounded-full hover:bg-gray-800 transition-all duration-300 flex items-center justify-center gap-2 font-medium font-jost group"
                >
                  Get Free Quote
                  <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
                </Link>
                <Link
                  href="/project"
                  className="border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-full hover:border-gray-400 hover:bg-gray-50 transition-all duration-300 font-medium font-jost"
                >
                  View Portfolio
                </Link>
              </div>
              
              <div className="flex items-center gap-8 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  <span className="font-jost">4-20 weeks delivery</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  <span className="font-jost">E-commerce experts</span>
                </div>
              </div>
            </div>
            
            <div className="relative">
              <div className="bg-white p-8 rounded-2xl shadow-2xl border border-gray-100">
                <div className="space-y-6">
                  {/* E-commerce Dashboard */}
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-semibold text-gray-800 font-jost">Store Dashboard</h3>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-xs text-gray-500">Live</span>
                    </div>
                  </div>
                  
                  {/* Sales Metrics */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="text-green-600 font-bold text-xl">$24.5K</div>
                      <div className="text-xs text-gray-600">Monthly Sales</div>
                      <div className="flex items-center mt-1">
                        <TrendingUp className="w-3 h-3 text-green-500 mr-1" />
                        <span className="text-xs text-green-600">+15%</span>
                      </div>
                    </div>
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="text-blue-600 font-bold text-xl">1,247</div>
                      <div className="text-xs text-gray-600">Orders</div>
                      <div className="flex items-center mt-1">
                        <TrendingUp className="w-3 h-3 text-blue-500 mr-1" />
                        <span className="text-xs text-blue-600">+8%</span>
                      </div>
                    </div>
                  </div>
                  
                  {/* Product Grid */}
                  <div className="space-y-3">
                    <h4 className="text-sm font-medium text-gray-700 font-jost">Top Products</h4>
                    <div className="grid grid-cols-3 gap-2">
                      <div className="bg-gray-100 aspect-square rounded-lg flex items-center justify-center">
                        <Package className="w-6 h-6 text-gray-400" />
                      </div>
                      <div className="bg-gray-100 aspect-square rounded-lg flex items-center justify-center">
                        <Package className="w-6 h-6 text-gray-400" />
                      </div>
                      <div className="bg-gray-100 aspect-square rounded-lg flex items-center justify-center">
                        <Package className="w-6 h-6 text-gray-400" />
                      </div>
                    </div>
                  </div>
                  
                  {/* Action Button */}
                  <div className="bg-green-600 text-white p-3 rounded-lg text-center">
                    <div className="font-medium font-jost">Add New Product</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              Why Choose Our E-commerce Solutions?
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto font-jost">
              We don't just build online stores – we create complete e-commerce ecosystems that drive sales,
              delight customers, and scale with your business growth.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group"
              >
                <div className="flex items-center justify-center w-12 h-12 bg-green-600 text-white rounded-xl mb-6 group-hover:scale-110 transition-transform duration-300">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold text-black mb-4 font-jost">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed font-jost">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Packages Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              E-commerce Development Packages
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto font-jost">
              From startup stores to enterprise marketplaces, we have the perfect e-commerce solution
              to launch and grow your online business.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {packages.map((pkg, index) => (
              <div
                key={index}
                className={`bg-white p-8 rounded-2xl shadow-lg border-2 transition-all duration-300 hover:shadow-xl hover:-translate-y-1 relative ${
                  pkg.popular ? 'border-green-600' : 'border-gray-100'
                }`}
              >
                {pkg.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="bg-green-600 text-white px-4 py-2 rounded-full text-sm font-medium font-jost">
                      Most Popular
                    </div>
                  </div>
                )}

                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-black mb-2 font-jost">
                    {pkg.name}
                  </h3>
                  <div className="text-4xl font-bold text-black mb-2 font-jost">
                    {pkg.price}
                  </div>
                  <p className="text-gray-600 font-jost">
                    {pkg.description}
                  </p>
                </div>

                <ul className="space-y-3 mb-8">
                  {pkg.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center text-gray-600 font-jost">
                      <CheckCircle className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>

                <div className="text-center mb-6">
                  <div className="text-sm text-gray-500 font-jost">Timeline: {pkg.timeline}</div>
                </div>

                <Link
                  href="/get-quote"
                  className={`w-full py-3 px-6 rounded-full transition-all duration-300 flex items-center justify-center gap-2 font-medium font-jost ${
                    pkg.popular
                      ? 'bg-green-600 text-white hover:bg-green-700'
                      : 'bg-black text-white hover:bg-gray-800'
                  }`}
                >
                  Get Started
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-gray-600 font-jost">
              Get answers to common questions about our e-commerce development services.
            </p>
          </div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div
                key={index}
                className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"
              >
                <button
                  onClick={() => setOpenFAQ(openFAQ === index ? null : index)}
                  className="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
                >
                  <h3 className="text-lg font-semibold text-black font-jost pr-4">
                    {faq.question}
                  </h3>
                  {openFAQ === index ? (
                    <ChevronUp className="w-5 h-5 text-gray-500 flex-shrink-0" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-gray-500 flex-shrink-0" />
                  )}
                </button>

                {openFAQ === index && (
                  <div className="px-8 pb-6">
                    <p className="text-gray-600 leading-relaxed font-jost">
                      {faq.answer}
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-green-600 to-teal-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl sm:text-5xl font-bold text-white mb-6 font-jost">
            Ready to Launch Your Online Store?
          </h2>
          <p className="text-xl text-green-100 mb-8 leading-relaxed font-jost">
            Join thousands of successful businesses that have transformed their sales with our e-commerce solutions.
            Let's build an online store that converts visitors into loyal customers.
          </p>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-8">
            <Link
              href="/get-quote"
              className="bg-white text-green-600 px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 flex items-center gap-2 font-medium font-jost group"
            >
              Get Free E-commerce Consultation
              <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
            </Link>
            <Link
              href="/contact-us"
              className="border-2 border-white text-white px-8 py-4 rounded-full hover:bg-white hover:text-green-600 transition-all duration-300 font-medium font-jost"
            >
              Schedule a Call
            </Link>
          </div>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-green-100">
            <div className="flex items-center gap-2">
              <Clock className="w-5 h-5" />
              <span className="font-jost">Free consultation included</span>
            </div>
            <div className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              <span className="font-jost">Secure payment processing</span>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

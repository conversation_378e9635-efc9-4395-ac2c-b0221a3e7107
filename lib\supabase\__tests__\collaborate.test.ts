// Test file for collaborate functionality
// Run with: npm test collaborate.test.ts

import { isValidEmail, isValidUrl } from '../collaborate';

describe('Collaborate Utility Functions', () => {
  describe('isValidEmail', () => {
    test('should validate correct email addresses', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
    });

    test('should reject invalid email addresses', () => {
      expect(isValidEmail('invalid-email')).toBe(false);
      expect(isValidEmail('test@')).toBe(false);
      expect(isValidEmail('@domain.com')).toBe(false);
      expect(isValidEmail('')).toBe(false);
    });
  });

  describe('isValidUrl', () => {
    test('should validate correct URLs', () => {
      expect(isValidUrl('https://github.com/username')).toBe(true);
      expect(isValidUrl('https://linkedin.com/in/username')).toBe(true);
      expect(isValidUrl('http://portfolio.com')).toBe(true);
    });

    test('should reject invalid URLs', () => {
      expect(isValidUrl('not-a-url')).toBe(false);
      expect(isValidUrl('github.com/username')).toBe(false);
      expect(isValidUrl('')).toBe(false);
    });
  });
});

// Mock data for testing
export const mockProject = {
  id: 'test-project-1',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  title: 'Test AI Chatbot Project',
  description: 'A test project for building an AI chatbot with local language support.',
  technologies: ['React', 'Python', 'NLP'],
  duration: '3-4 months',
  difficulty_level: 'Advanced' as const,
  required_skills: ['React', 'Python', 'API Design'],
  is_active: true,
  application_deadline: '2024-12-31',
  max_collaborators: 4
};

export const mockJobPosting = {
  id: 'test-job-1',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  title: 'Test Frontend Developer',
  description: 'A test job posting for a frontend developer position.',
  employment_type: 'Full-time' as const,
  experience_level: 'Junior' as const,
  location_type: 'Remote' as const,
  compensation_range: '$800-1200/month',
  required_skills: ['React', 'TypeScript', 'CSS'],
  is_active: true,
  application_deadline: '2024-12-01'
};

export const mockProjectApplication = {
  full_name: 'John Doe',
  email: '<EMAIL>',
  phone: '+250123456789',
  location: 'Kigali, Rwanda',
  professional_title: 'Frontend Developer',
  github_link: 'https://github.com/johndoe',
  linkedin_link: 'https://linkedin.com/in/johndoe',
  portfolio_link: 'https://johndoe.dev',
  years_experience: '2-3 years',
  primary_skills: ['React', 'JavaScript', 'CSS'],
  project_interest: 'Test AI Chatbot Project',
  motivation_statement: 'I am very interested in this project because it combines my passion for AI and web development. I have experience building chatbots and would love to contribute to this meaningful project that supports local languages.',
  availability_hours: '20-30 hours/week',
  expected_start_date: '2024-02-01',
  additional_comments: 'I am excited to learn and contribute to this project.',
  project_id: 'test-project-1'
};

export const mockJobApplication = {
  full_name: 'Jane Smith',
  email: '<EMAIL>',
  phone: '+250987654321',
  location: 'Musanze, Rwanda',
  current_position: 'Junior Developer at TechCorp',
  resume_file_url: 'https://example.com/resume.pdf',
  github_link: 'https://github.com/janesmith',
  linkedin_link: 'https://linkedin.com/in/janesmith',
  portfolio_link: 'https://janesmith.dev',
  years_experience: '1-2 years',
  salary_expectation: '$800-1200/month',
  position_applied_for: 'Test Frontend Developer',
  cover_letter: 'I am writing to express my strong interest in the Frontend Developer position at iREME Soft Hub. With my background in React and TypeScript development, I am confident that I can contribute effectively to your team. I am particularly excited about the opportunity to work on innovative projects that make a real impact in the local tech ecosystem.',
  availability_start_date: '2024-02-15',
  preferred_work_arrangement: 'Remote',
  additional_information: 'I am passionate about continuous learning and would love to grow with the team.',
  job_posting_id: 'test-job-1'
};

// Test data validation functions
export const validateProjectApplicationData = (data: any): string[] => {
  const errors: string[] = [];

  if (!data.full_name || data.full_name.trim().length < 2) {
    errors.push('Full name must be at least 2 characters');
  }

  if (!isValidEmail(data.email)) {
    errors.push('Invalid email address');
  }

  if (!data.location || data.location.trim().length < 2) {
    errors.push('Location is required');
  }

  if (!isValidUrl(data.github_link) || !data.github_link.includes('github.com')) {
    errors.push('Valid GitHub URL is required');
  }

  if (!data.primary_skills || data.primary_skills.length === 0) {
    errors.push('At least one skill must be selected');
  }

  if (!data.motivation_statement || data.motivation_statement.length < 200) {
    errors.push('Motivation statement must be at least 200 characters');
  }

  if (data.motivation_statement && data.motivation_statement.length > 500) {
    errors.push('Motivation statement must be less than 500 characters');
  }

  return errors;
};

export const validateJobApplicationData = (data: any): string[] => {
  const errors: string[] = [];

  if (!data.full_name || data.full_name.trim().length < 2) {
    errors.push('Full name must be at least 2 characters');
  }

  if (!isValidEmail(data.email)) {
    errors.push('Invalid email address');
  }

  if (!data.phone || data.phone.trim().length < 10) {
    errors.push('Valid phone number is required');
  }

  if (!data.location || data.location.trim().length < 2) {
    errors.push('Location is required');
  }

  if (!data.resume_file_url) {
    errors.push('Resume file is required');
  }

  if (!isValidUrl(data.github_link) || !data.github_link.includes('github.com')) {
    errors.push('Valid GitHub URL is required');
  }

  if (!data.cover_letter || data.cover_letter.length < 300) {
    errors.push('Cover letter must be at least 300 characters');
  }

  if (data.cover_letter && data.cover_letter.length > 800) {
    errors.push('Cover letter must be less than 800 characters');
  }

  return errors;
};

// Example usage and testing
describe('Data Validation', () => {
  test('should validate project application data', () => {
    const errors = validateProjectApplicationData(mockProjectApplication);
    expect(errors).toHaveLength(0);
  });

  test('should validate job application data', () => {
    const errors = validateJobApplicationData(mockJobApplication);
    expect(errors).toHaveLength(0);
  });

  test('should catch invalid project application data', () => {
    const invalidData = { ...mockProjectApplication, email: 'invalid-email' };
    const errors = validateProjectApplicationData(invalidData);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors).toContain('Invalid email address');
  });

  test('should catch invalid job application data', () => {
    const invalidData = { ...mockJobApplication, cover_letter: 'Too short' };
    const errors = validateJobApplicationData(invalidData);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors).toContain('Cover letter must be at least 300 characters');
  });
});

// Performance testing helpers
export const measureFormSubmissionTime = async (submitFunction: () => Promise<any>) => {
  const startTime = performance.now();
  await submitFunction();
  const endTime = performance.now();
  return endTime - startTime;
};

// Accessibility testing helpers
export const checkFormAccessibility = (formElement: HTMLElement) => {
  const issues: string[] = [];

  // Check for proper labels
  const inputs = formElement.querySelectorAll('input, textarea, select');
  inputs.forEach((input) => {
    const label = formElement.querySelector(`label[for="${input.id}"]`);
    if (!label && !input.getAttribute('aria-label')) {
      issues.push(`Input ${input.id || 'unnamed'} missing label`);
    }
  });

  // Check for proper error messages
  const errorElements = formElement.querySelectorAll('[role="alert"], .error-message');
  if (errorElements.length === 0) {
    issues.push('No error message containers found');
  }

  return issues;
};

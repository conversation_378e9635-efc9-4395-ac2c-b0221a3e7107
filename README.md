# iremesofthub

A modern Next.js application built with TypeScript, Tailwind CSS, and Supabase.

## 🚀 Tech Stack

- **Framework**: Next.js 14+ with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with custom design system
- **Database**: Supabase
- **Authentication**: Supabase Auth
- **Performance**:
  - @vercel/analytics
  - @vercel/speed-insights
  - next-pwa (Progressive Web App)
  - Sharp (Image optimization)
- **UI Components**: Headless UI, Lucide React
- **Forms**: React Hook Form with Zod validation
- **Animation**: Framer Motion
- **Code Quality**: ESLint, Prettier, TypeScript

## 📁 Project Structure

```
├── app/                    # Next.js App Router pages
├── components/
│   ├── ui/                # Reusable UI components
│   └── layout/            # Layout components
├── lib/
│   ├── supabase/          # Supabase client configuration
│   ├── utils/             # Utility functions
│   └── types/             # TypeScript type definitions
├── hooks/                 # Custom React hooks
├── styles/                # Additional styles
└── public/                # Static assets
```

## 🛠️ Getting Started

1. **Clone the repository**
   ```bash
   git clone https://github.com/intoregaby/iremesofthub.git
   cd iremesofthub
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   ```
   Fill in your Supabase credentials and other environment variables.

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📝 Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint errors
- `npm run format` - Format code with Prettier
- `npm run type-check` - Run TypeScript type checking
- `npm run analyze` - Analyze bundle size

## 🔧 Configuration

### Supabase Setup
1. Create a new project at [supabase.com](https://supabase.com)
2. Copy your project URL and anon key to `.env.local`
3. Set up your database schema
4. Configure authentication providers if needed

### PWA Configuration
The app is configured as a Progressive Web App with:
- Service worker for offline functionality
- Web app manifest
- Optimized caching strategies

## 🎨 Design System

The project includes a comprehensive design system with:
- CSS custom properties for theming
- Dark mode support
- Responsive design utilities
- Custom animations and transitions
- Accessible color palette

## 📱 Features

- ⚡ Fast loading with Next.js 14+ and Turbopack
- 🎨 Beautiful UI with Tailwind CSS
- 🔐 Authentication with Supabase
- 📱 Progressive Web App (PWA)
- 🌙 Dark mode support
- 📊 Analytics and performance monitoring
- 🔍 SEO optimized
- ♿ Accessibility focused
- 📝 Type-safe with TypeScript

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy!

### Other Platforms
The app can be deployed to any platform that supports Node.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

This project is private and proprietary.

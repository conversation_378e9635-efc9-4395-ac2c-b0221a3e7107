import React from 'react';
import Image from 'next/image';

interface Brand {
  name: string;
  logo: string;
}

const TrustedBrands = () => {
  const brands: Brand[] = [
    {
      name: 'Amazon',
      logo: 'https://logos-world.net/wp-content/uploads/2020/04/Amazon-Logo.png'
    },
    {
      name: 'Netflix',
      logo: 'https://logos-world.net/wp-content/uploads/2020/04/Netflix-Logo.png'
    },
    {
      name: 'Spotify',
      logo: 'https://logos-world.net/wp-content/uploads/2020/06/Spotify-Logo.png'
    },
    {
      name: 'Microsoft',
      logo: 'https://logos-world.net/wp-content/uploads/2020/09/Microsoft-Logo.png'
    },
    {
      name: 'Facebook',
      logo: 'https://logos-world.net/wp-content/uploads/2020/04/Facebook-Logo.png'
    },
    {
      name: 'Yahoo',
      logo: 'https://logos-world.net/wp-content/uploads/2020/09/Yahoo-Logo.png'
    }
  ];

  return (
    <section className="bg-white py-16 px-4">
      <div className="max-w-7xl mx-auto">
        {/* Brand Logos Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 items-center justify-items-center mb-12">
          {brands.map((brand, index) => (
            <div 
              key={index}
              className="flex items-center justify-center h-12 w-32 transition-all duration-300 opacity-80 hover:opacity-100"
            >
              <Image
                src={brand.logo}
                alt={brand.name}
                width={128}
                height={48}
                className="max-h-10 max-w-full object-contain filter brightness-0"
                style={{ 
                  fontFamily: 'var(--font-jost), system-ui, sans-serif',
                }}
              />
            </div>
          ))}
        </div>

        {/* Trust Text */}
        <div className="text-center">
          <p 
            className="text-gray-500 text-sm font-normal tracking-wide font-jost"
          >
            Trusted by more than 500 software developer and companies across the globe
          </p>
        </div>
      </div>
    </section>
  );
};

export default TrustedBrands;

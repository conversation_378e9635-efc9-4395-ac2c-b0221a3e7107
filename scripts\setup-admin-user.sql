-- =====================================================
-- ADMIN USER SETUP SCRIPT
-- Run this AFTER running the database migrations
-- =====================================================

-- IMPORTANT: Make sure you've run these migrations first:
-- 1. supabase/migrations/006_create_admin_dashboard_schema.sql
-- 2. supabase/migrations/007_create_admin_dashboard_functions.sql

-- Step 1: Create the admin user in Supabase Auth
INSERT INTO auth.users (
  id,
  instance_id,
  email,
  encrypted_password,
  email_confirmed_at,
  created_at,
  updated_at,
  raw_app_meta_data,
  raw_user_meta_data,
  is_super_admin,
  role,
  aud
) VALUES (
  gen_random_uuid(),
  '00000000-0000-0000-0000-000000000000',
  '<EMAIL>',
  crypt('Nike14##', gen_salt('bf')),
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"first_name": "System", "last_name": "Administrator"}',
  false,
  'authenticated',
  'authenticated'
);

-- Step 2: Create the admin user in admin_users table
INSERT INTO admin_users (
  email,
  supabase_user_id,
  first_name,
  last_name,
  role,
  is_active,
  is_verified
) VALUES (
  '<EMAIL>',
  (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
  'System',
  'Administrator',
  'super_admin',
  true,
  true
);

-- Step 3: Verify the setup
SELECT
  au.email,
  au.first_name,
  au.last_name,
  au.role,
  au.is_active,
  au.is_verified,
  u.email_confirmed_at IS NOT NULL as auth_confirmed
FROM admin_users au
LEFT JOIN auth.users u ON au.supabase_user_id = u.id
WHERE au.email = '<EMAIL>';

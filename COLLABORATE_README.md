# iREME Collaborate Page

A comprehensive collaboration platform that allows developers to apply for public projects and job opportunities while maintaining the existing website's design consistency.

## 🚀 Features

### Core Functionality
- **Project Collaboration**: Browse and apply for public development projects
- **Job Opportunities**: View and apply for full-time, part-time, and contract positions
- **Dual Application Forms**: Separate optimized forms for project and job applications
- **File Upload**: Resume/CV upload functionality for job applications
- **Search & Filter**: Advanced filtering by technology, difficulty, experience level, etc.
- **Real-time Validation**: Form validation with clear error messages
- **Responsive Design**: Mobile-first approach with excellent mobile experience

### Design Integration
- **Color Consistency**: Uses exact same color palette as existing iREME website
- **Typography**: Jost variable font family throughout
- **Component Harmony**: Matches existing button styles, form layouts, and card designs
- **Animation Patterns**: Consistent hover effects and transitions

## 📁 File Structure

```
app/collaborate/
├── page.tsx                    # Main collaborate page
├── apply/
│   ├── project/
│   │   └── page.tsx           # Project application form
│   └── job/
│       └── page.tsx           # Job application form

components/collaborate/
├── ProjectCard.tsx             # Project display card
├── JobCard.tsx                # Job display card
└── FilterModal.tsx            # Advanced filtering modal

lib/supabase/
└── collaborate.ts             # Supabase integration functions

supabase/migrations/
├── 001_create_collaborate_tables.sql  # Database schema
└── 002_insert_sample_data.sql         # Sample data
```

## 🗄️ Database Schema

### Tables Created
1. **projects** - Available collaboration projects
2. **job_postings** - Available job positions  
3. **project_applications** - Project application submissions
4. **job_applications** - Job application submissions

### Key Features
- **Row Level Security (RLS)** - Proper access control
- **File Storage** - Resume upload to Supabase storage
- **Indexes** - Optimized for search and filtering
- **Triggers** - Auto-update timestamps

## 🛠️ Setup Instructions

### 1. Environment Variables
Add to your `.env.local`:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 2. Database Setup
Run the migrations in your Supabase dashboard:
```sql
-- Run 001_create_collaborate_tables.sql first
-- Then run 002_insert_sample_data.sql
```

### 3. Storage Setup
Create a storage bucket named `applications` in Supabase for resume uploads.

### 4. Navigation Update
The collaborate page has been added to the main navigation in `components/Header.tsx`.

## 🧪 Testing Guidelines

### Manual Testing Checklist

#### Main Collaborate Page (`/collaborate`)
- [ ] Page loads without errors
- [ ] Projects and jobs load from Supabase
- [ ] Tab switching between Projects and Jobs works
- [ ] Search functionality works for both tabs
- [ ] Filter modal opens and applies filters correctly
- [ ] Loading states display properly
- [ ] Error states handle network issues gracefully
- [ ] Responsive design works on mobile devices

#### Project Application Form (`/collaborate/apply/project`)
- [ ] Form loads with proper project information
- [ ] All three steps of the form work correctly
- [ ] Form validation shows appropriate error messages
- [ ] Skills selection works (multi-select)
- [ ] Date picker functions properly
- [ ] Form submission works and shows success message
- [ ] Navigation between steps works
- [ ] Character count for motivation statement works
- [ ] Required field validation prevents submission

#### Job Application Form (`/collaborate/apply/job`)
- [ ] Form loads with proper job information
- [ ] File upload (drag & drop and click) works
- [ ] File validation (type and size) works
- [ ] All form fields validate correctly
- [ ] Form submission uploads file and submits data
- [ ] Success page displays after submission
- [ ] Error handling works for failed uploads
- [ ] Cover letter character count works

#### Filter Functionality
- [ ] Filter modal opens and closes properly
- [ ] Project filters (difficulty, technology, duration) work
- [ ] Job filters (employment type, experience, location) work
- [ ] Clear filters functionality works
- [ ] Filter count badge displays correctly
- [ ] Applied filters affect search results

### Automated Testing
Consider adding tests for:
- Form validation logic
- Supabase integration functions
- Component rendering
- Filter functionality

## 🎨 Design Compliance

### Color Palette Used
- **Primary**: Black (#000000) for buttons and headers
- **Background**: White (#ffffff)
- **Text**: Gray-900 for headings, Gray-600/700 for body text
- **Borders**: Gray-100/300 for cards and inputs
- **Focus**: Blue-500 for focus states
- **Success**: Green-400/500 for status indicators
- **Error**: Red-500/600 for error states

### Typography
- **Font Family**: Jost variable font (existing site standard)
- **Font Weights**: Regular (400), Medium (500), Semibold (600), Bold (700)

### Component Consistency
- **Buttons**: Black background, white text, gray-800 hover
- **Cards**: White background, gray-100 border, shadow-lg
- **Forms**: Gray-300 borders, blue-500 focus rings
- **Animations**: Consistent hover transforms and transitions

## 🔧 Customization

### Adding New Project Types
Update the `skillOptions` array in the project application form and add corresponding entries to the filter modal.

### Modifying Application Fields
Update the TypeScript interfaces in `lib/supabase/collaborate.ts` and corresponding database schema.

### Extending Search Functionality
Modify the search functions in `lib/supabase/collaborate.ts` to include additional fields or more complex queries.

## 📱 Mobile Optimization

- **Responsive Grid**: Projects display in 1-3 columns based on screen size
- **Touch-Friendly**: All buttons and interactive elements are properly sized
- **Mobile Navigation**: Hamburger menu integration
- **Form Optimization**: Mobile-optimized form layouts and inputs
- **File Upload**: Mobile-friendly file selection

## 🚀 Performance Considerations

- **Lazy Loading**: Consider implementing for large project/job lists
- **Image Optimization**: Use Next.js Image component for any future images
- **Database Queries**: Indexed for optimal search performance
- **Caching**: Consider implementing query caching for frequently accessed data

## 🔒 Security Features

- **Input Validation**: Both client-side and server-side validation
- **File Upload Security**: File type and size restrictions
- **SQL Injection Prevention**: Parameterized queries via Supabase
- **XSS Protection**: Proper input sanitization
- **Rate Limiting**: Consider implementing for form submissions

## 📈 Analytics & Monitoring

Consider tracking:
- Application submission rates
- Popular project types and technologies
- Form abandonment rates
- Mobile vs desktop usage
- Search query patterns

## 🤝 Contributing

When making changes:
1. Maintain design consistency with existing iREME website
2. Follow TypeScript best practices
3. Add proper error handling
4. Test on multiple devices and browsers
5. Update this documentation as needed

## 📞 Support

For issues or questions about the Collaborate page:
- Check the browser console for error messages
- Verify Supabase connection and permissions
- Ensure all environment variables are set correctly
- Test database migrations in a development environment first

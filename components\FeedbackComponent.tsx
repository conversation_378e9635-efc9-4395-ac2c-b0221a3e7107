'use client';

import React, { useState } from 'react';
import { ThumbsUp, ThumbsDown, Send, Heart } from 'lucide-react';

type ResponseType = 'yes' | 'no' | null;

const FeedbackComponent = () => {
  const [selectedResponse, setSelectedResponse] = useState<ResponseType>(null);
  const [feedback, setFeedback] = useState<string>('');
  const [isSubmitted, setIsSubmitted] = useState<boolean>(false);

  const handleResponseClick = (response: ResponseType) => {
    setSelectedResponse(response);
    if (response === 'yes') {
      setIsSubmitted(true);
    }
  };

  const handleSubmitFeedback = () => {
    if (feedback.trim()) {
      setIsSubmitted(true);
    }
  };

  if (isSubmitted) {
    return (
      <section className="py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="bg-gradient-to-r from-green-400 via-blue-400 to-cyan-400 rounded-3xl p-6 sm:p-8 shadow-xl">
            <div className="text-center">
              <div className="mb-4">
                <Heart className="w-12 h-12 text-white mx-auto mb-3 animate-pulse" />
              </div>
              
              <h2 
                className="text-2xl sm:text-3xl font-bold text-white mb-3 font-jost"
              >
                Thank you for your feedback!
              </h2>
              
              <p 
                className="text-lg text-white/90 mb-6 max-w-2xl mx-auto font-jost"
              >
                Your insights help us create better experiences.
              </p>

              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <button 
                  className="bg-white text-gray-800 px-6 py-2 rounded-full hover:bg-gray-100 transition-all duration-300 font-medium text-base transform hover:scale-105 font-jost"
                >
                  Join Our Community
                </button>
                
                <button 
                  className="border-2 border-white text-white px-6 py-2 rounded-full hover:bg-white/10 transition-all duration-300 font-medium text-base font-jost"
                  onClick={() => {
                    setIsSubmitted(false);
                    setSelectedResponse(null);
                    setFeedback('');
                  }}
                >
                  Give More Feedback
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        <div className="bg-gradient-to-r from-green-400 via-blue-400 to-cyan-400 rounded-3xl p-6 sm:p-8 shadow-xl">
          <div className="flex flex-col lg:flex-row items-center justify-between gap-6">
            {/* Left Content */}
            <div className="flex-1 text-center lg:text-left">
              <h2 
                className="text-2xl sm:text-2xl font-bold text-white mb-3 font-jost"
              >
                Did you find what you were looking for today?
              </h2>
              
              <p 
                className="text-lg text-white/90 mb-4 font-jost"
              >
                Let us know so we can improve the quality of the content on our pages.
              </p>

              {/* Feedback textarea for "No" response */}
              {selectedResponse === 'no' && (
                <div className="mb-4 text-left">
                  <textarea
                    value={feedback}
                    onChange={(e) => setFeedback(e.target.value)}
                    placeholder="Tell us what you were looking for and how we can improve..."
                    className="w-full p-4 rounded-2xl border-0 focus:ring-4 focus:ring-white/30 focus:outline-none resize-none text-gray-800 placeholder-gray-500 font-jost"
                    rows={4}
                  />
                  
                  <button
                    onClick={handleSubmitFeedback}
                    disabled={!feedback.trim()}
                    className="mt-4 bg-white text-gray-800 px-6 py-2 rounded-full hover:bg-gray-100 transition-all duration-300 font-medium flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 font-jost"
                  >
                    <Send size={18} />
                    Submit Feedback
                  </button>
                </div>
              )}
            </div>

            {/* Right Buttons */}
            <div className="flex gap-4">
              <button
                onClick={() => handleResponseClick('yes')}
                className={`flex items-center gap-3 px-8 py-4 rounded-full font-medium text-lg transition-all duration-300 transform hover:scale-105 font-jost ${
                  selectedResponse === 'yes'
                    ? 'bg-white text-gray-800 shadow-lg'
                    : 'bg-black/80 text-white hover:bg-black'
                }`}
              >
                <ThumbsUp size={20} />
                Yes
              </button>
              
              <button
                onClick={() => handleResponseClick('no')}
                className={`flex items-center gap-3 px-8 py-4 rounded-full font-medium text-lg transition-all duration-300 transform hover:scale-105 font-jost ${
                  selectedResponse === 'no'
                    ? 'bg-white text-gray-800 shadow-lg'
                    : 'bg-black/80 text-white hover:bg-black'
                }`}
              >
                <ThumbsDown size={20} />
                No
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeedbackComponent;

'use client';

import React, { useState } from 'react';
import { ArrowRight, CheckCircle, Clock, Users, Star, Upload, X } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

interface FormData {
  // Contact Information
  fullName: string;
  email: string;
  phone: string;
  company: string;
  website: string;
  
  // Project Details
  projectType: string;
  budgetRange: string;
  timeline: string;
  description: string;
  requirements: string[];
  
  // Additional Information
  hearAboutUs: string;
  preferredContact: string;
  additionalComments: string;
}

const GetQuotePage = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [formData, setFormData] = useState<FormData>({
    fullName: '',
    email: '',
    phone: '',
    company: '',
    website: '',
    projectType: '',
    budgetRange: '',
    timeline: '',
    description: '',
    requirements: [],
    hearAboutUs: '',
    preferredContact: 'email',
    additionalComments: ''
  });

  const totalSteps = 3;

  const projectTypes = [
    'Web Development',
    'Mobile App Development',
    'E-commerce Solution',
    'Custom Software',
    'UI/UX Design',
    'Digital Marketing',
    'Other'
  ];

  const budgetRanges = [
    '$5,000 - $10,000',
    '$10,000 - $25,000',
    '$25,000 - $50,000',
    '$50,000 - $100,000',
    '$100,000+'
  ];

  const timelineOptions = [
    'ASAP (Rush Job)',
    '1-2 months',
    '3-4 months',
    '5-6 months',
    '6+ months',
    'Flexible'
  ];

  const requirementOptions = [
    'Responsive Design',
    'SEO Optimization',
    'Content Management System',
    'E-commerce Integration',
    'Third-party API Integration',
    'Analytics & Tracking',
    'Multi-language Support',
    'Payment Gateway',
    'User Authentication',
    'Admin Dashboard'
  ];

  const handleInputChange = (field: keyof FormData, value: string | string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    if (step === 1) {
      if (!formData.fullName.trim()) newErrors.fullName = 'Full name is required';
      if (!formData.email.trim()) newErrors.email = 'Email is required';
      else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = 'Please enter a valid email';
    }

    if (step === 2) {
      if (!formData.projectType) newErrors.projectType = 'Please select a project type';
      if (!formData.budgetRange) newErrors.budgetRange = 'Please select a budget range';
      if (!formData.timeline) newErrors.timeline = 'Please select a timeline';
      if (!formData.description.trim()) newErrors.description = 'Project description is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleRequirementToggle = (requirement: string) => {
    setFormData(prev => ({
      ...prev,
      requirements: prev.requirements.includes(requirement)
        ? prev.requirements.filter(r => r !== requirement)
        : [...prev.requirements, requirement]
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    setIsSubmitted(true);
  };

  const nextStep = () => {
    if (validateStep(currentStep) && currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <div className="bg-white p-12 rounded-2xl shadow-lg border border-gray-100">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-6" />
              <h1 className="text-3xl font-bold text-black mb-4 font-jost">
                Thank You for Your Quote Request!
              </h1>
              <p className="text-lg text-gray-600 mb-8 font-jost">
                We've received your project details and will get back to you within 24 hours with a detailed proposal.
              </p>
              <div className="bg-gray-50 p-6 rounded-lg mb-8">
                <h3 className="text-lg font-semibold text-black mb-2 font-jost">What happens next?</h3>
                <div className="space-y-2 text-left">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-700 font-jost">Our team reviews your requirements</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-700 font-jost">We prepare a detailed proposal and timeline</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-700 font-jost">You receive your custom quote via email</span>
                  </div>
                </div>
              </div>
              <button 
                onClick={() => window.location.href = '/'}
                className="bg-black text-white px-8 py-3 rounded-full hover:bg-gray-800 transition-colors duration-300 font-jost"
              >
                Return to Homepage
              </button>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1 py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center bg-black text-white px-4 py-2 rounded-full text-sm mb-8 hover:scale-105 transition-all duration-300 cursor-pointer animate-fade-in font-jost">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
              Free Custom Quote - No Commitment Required
            </div>
            
            <h1 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              Get Your Custom Quote
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8 font-jost">
              Tell us about your project and we'll provide you with a detailed proposal tailored to your needs and budget.
            </p>
            
            {/* Progress Indicator */}
            <div className="flex items-center justify-center mb-12">
              {[1, 2, 3].map((step) => (
                <div key={step} className="flex items-center">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300 ${
                    step <= currentStep 
                      ? 'bg-black text-white' 
                      : 'bg-gray-200 text-gray-500'
                  }`}>
                    {step}
                  </div>
                  {step < 3 && (
                    <div className={`w-16 h-1 mx-2 transition-all duration-300 ${
                      step < currentStep ? 'bg-black' : 'bg-gray-200'
                    }`}></div>
                  )}
                </div>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Trust Signals Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100 sticky top-8">
                <h3 className="text-xl font-semibold text-black mb-6 font-jost">Why Choose iREME Soft Hub?</h3>
                
                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <Clock className="w-6 h-6 text-blue-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-black font-jost">24-Hour Response</h4>
                      <p className="text-sm text-gray-600 font-jost">Quick turnaround on all quote requests</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <Users className="w-6 h-6 text-green-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-black font-jost">Expert Team</h4>
                      <p className="text-sm text-gray-600 font-jost">Experienced developers and designers</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <Star className="w-6 h-6 text-yellow-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-black font-jost">5-Star Rating</h4>
                      <p className="text-sm text-gray-600 font-jost">Trusted by 500+ satisfied clients</p>
                    </div>
                  </div>
                </div>

                <div className="mt-8 p-4 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-700 italic font-jost">
                    "iREME Soft Hub delivered exactly what we needed, on time and within budget. Highly recommended!"
                  </p>
                  <p className="text-xs text-gray-500 mt-2 font-jost">- Sarah Johnson, CEO TechStart</p>
                </div>
              </div>
            </div>

            {/* Main Form */}
            <div className="lg:col-span-2">
              <div className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100">
                <form onSubmit={handleSubmit}>
                  {/* Step 1: Contact Information */}
                  {currentStep === 1 && (
                    <div className="space-y-6">
                      <h2 className="text-2xl font-bold text-black mb-6 font-jost">Contact Information</h2>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                            Full Name *
                          </label>
                          <input
                            type="text"
                            required
                            value={formData.fullName}
                            onChange={(e) => handleInputChange('fullName', e.target.value)}
                            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost ${
                              errors.fullName ? 'border-red-500' : 'border-gray-300'
                            }`}
                            placeholder="Your full name"
                          />
                          {errors.fullName && (
                            <p className="text-red-500 text-sm mt-1 font-jost">{errors.fullName}</p>
                          )}
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                            Email Address *
                          </label>
                          <input
                            type="email"
                            required
                            value={formData.email}
                            onChange={(e) => handleInputChange('email', e.target.value)}
                            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost ${
                              errors.email ? 'border-red-500' : 'border-gray-300'
                            }`}
                            placeholder="<EMAIL>"
                          />
                          {errors.email && (
                            <p className="text-red-500 text-sm mt-1 font-jost">{errors.email}</p>
                          )}
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                            Phone Number
                          </label>
                          <input
                            type="tel"
                            value={formData.phone}
                            onChange={(e) => handleInputChange('phone', e.target.value)}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost"
                            placeholder="+****************"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                            Company Name
                          </label>
                          <input
                            type="text"
                            value={formData.company}
                            onChange={(e) => handleInputChange('company', e.target.value)}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost"
                            placeholder="Your company"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                          Website URL
                        </label>
                        <input
                          type="url"
                          value={formData.website}
                          onChange={(e) => handleInputChange('website', e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost"
                          placeholder="https://yourwebsite.com"
                        />
                      </div>
                    </div>
                  )}

                  {/* Step 2: Project Details */}
                  {currentStep === 2 && (
                    <div className="space-y-6">
                      <h2 className="text-2xl font-bold text-black mb-6 font-jost">Project Details</h2>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-3 font-jost">
                          Project Type *
                        </label>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                          {projectTypes.map((type) => (
                            <button
                              key={type}
                              type="button"
                              onClick={() => handleInputChange('projectType', type)}
                              className={`p-3 text-sm rounded-lg border transition-all duration-300 font-jost ${
                                formData.projectType === type
                                  ? 'bg-black text-white border-black'
                                  : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'
                              }`}
                            >
                              {type}
                            </button>
                          ))}
                        </div>
                        {errors.projectType && (
                          <p className="text-red-500 text-sm mt-1 font-jost">{errors.projectType}</p>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-3 font-jost">
                            Budget Range *
                          </label>
                          <select
                            required
                            value={formData.budgetRange}
                            onChange={(e) => handleInputChange('budgetRange', e.target.value)}
                            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost ${
                              errors.budgetRange ? 'border-red-500' : 'border-gray-300'
                            }`}
                          >
                            <option value="">Select budget range</option>
                            {budgetRanges.map((range) => (
                              <option key={range} value={range}>{range}</option>
                            ))}
                          </select>
                          {errors.budgetRange && (
                            <p className="text-red-500 text-sm mt-1 font-jost">{errors.budgetRange}</p>
                          )}
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-3 font-jost">
                            Timeline *
                          </label>
                          <select
                            required
                            value={formData.timeline}
                            onChange={(e) => handleInputChange('timeline', e.target.value)}
                            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost ${
                              errors.timeline ? 'border-red-500' : 'border-gray-300'
                            }`}
                          >
                            <option value="">Select timeline</option>
                            {timelineOptions.map((timeline) => (
                              <option key={timeline} value={timeline}>{timeline}</option>
                            ))}
                          </select>
                          {errors.timeline && (
                            <p className="text-red-500 text-sm mt-1 font-jost">{errors.timeline}</p>
                          )}
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-3 font-jost">
                          Project Description *
                        </label>
                        <textarea
                          required
                          rows={4}
                          value={formData.description}
                          onChange={(e) => handleInputChange('description', e.target.value)}
                          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost ${
                            errors.description ? 'border-red-500' : 'border-gray-300'
                          }`}
                          placeholder="Tell us about your project goals, target audience, and key features you need..."
                        />
                        {errors.description && (
                          <p className="text-red-500 text-sm mt-1 font-jost">{errors.description}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-3 font-jost">
                          Specific Requirements (Select all that apply)
                        </label>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                          {requirementOptions.map((requirement) => (
                            <button
                              key={requirement}
                              type="button"
                              onClick={() => handleRequirementToggle(requirement)}
                              className={`p-3 text-sm rounded-lg border transition-all duration-300 font-jost ${
                                formData.requirements.includes(requirement)
                                  ? 'bg-black text-white border-black'
                                  : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'
                              }`}
                            >
                              {requirement}
                            </button>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Step 3: Additional Information */}
                  {currentStep === 3 && (
                    <div className="space-y-6">
                      <h2 className="text-2xl font-bold text-black mb-6 font-jost">Additional Information</h2>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-3 font-jost">
                            How did you hear about us?
                          </label>
                          <select
                            value={formData.hearAboutUs}
                            onChange={(e) => handleInputChange('hearAboutUs', e.target.value)}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost"
                          >
                            <option value="">Select an option</option>
                            <option value="google">Google Search</option>
                            <option value="social-media">Social Media</option>
                            <option value="referral">Referral</option>
                            <option value="website">Your Website</option>
                            <option value="other">Other</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-3 font-jost">
                            Preferred Contact Method
                          </label>
                          <div className="space-y-2">
                            {['email', 'phone', 'both'].map((method) => (
                              <label key={method} className="flex items-center">
                                <input
                                  type="radio"
                                  name="preferredContact"
                                  value={method}
                                  checked={formData.preferredContact === method}
                                  onChange={(e) => handleInputChange('preferredContact', e.target.value)}
                                  className="mr-3"
                                />
                                <span className="text-sm text-gray-700 font-jost capitalize">{method}</span>
                              </label>
                            ))}
                          </div>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-3 font-jost">
                          Additional Comments
                        </label>
                        <textarea
                          rows={4}
                          value={formData.additionalComments}
                          onChange={(e) => handleInputChange('additionalComments', e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost"
                          placeholder="Any additional information you'd like to share..."
                        />
                      </div>

                      <div className="bg-gray-50 p-6 rounded-lg">
                        <h3 className="text-lg font-semibold text-black mb-3 font-jost">Quote Summary</h3>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-600 font-jost">Project Type:</span>
                            <span className="text-black font-jost">{formData.projectType || 'Not specified'}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600 font-jost">Budget Range:</span>
                            <span className="text-black font-jost">{formData.budgetRange || 'Not specified'}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600 font-jost">Timeline:</span>
                            <span className="text-black font-jost">{formData.timeline || 'Not specified'}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600 font-jost">Requirements:</span>
                            <span className="text-black font-jost">{formData.requirements.length} selected</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Navigation Buttons */}
                  <div className="flex justify-between mt-8">
                    <button
                      type="button"
                      onClick={prevStep}
                      disabled={currentStep === 1}
                      className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 font-jost ${
                        currentStep === 1
                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                      }`}
                    >
                      Previous
                    </button>
                    
                    {currentStep < totalSteps ? (
                      <button
                        type="button"
                        onClick={nextStep}
                        className="bg-black text-white px-8 py-3 rounded-lg hover:bg-gray-800 transition-all duration-300 flex items-center gap-2 font-jost"
                      >
                        Next Step
                        <ArrowRight size={18} />
                      </button>
                    ) : (
                      <button
                        type="submit"
                        disabled={isSubmitting}
                        className="bg-black text-white px-8 py-3 rounded-lg hover:bg-gray-800 transition-all duration-300 flex items-center gap-2 disabled:opacity-50 font-jost"
                      >
                        {isSubmitting ? 'Submitting...' : 'Get My Quote'}
                        {!isSubmitting && <ArrowRight size={18} />}
                      </button>
                    )}
                  </div>
                </form>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mt-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-black mb-4 font-jost">Frequently Asked Questions</h2>
              <p className="text-lg text-gray-600 font-jost">Get answers to common questions about our quote process</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-6">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-lg font-semibold text-black mb-3 font-jost">How long does it take to receive a quote?</h3>
                  <p className="text-gray-600 font-jost">We typically respond within 24 hours with a detailed proposal. For complex projects, we may schedule a call to better understand your requirements.</p>
                </div>

                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-lg font-semibold text-black mb-3 font-jost">Is the quote free?</h3>
                  <p className="text-gray-600 font-jost">Yes, absolutely! Our quotes are completely free with no obligation. We believe in transparent pricing and want you to make an informed decision.</p>
                </div>

                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-lg font-semibold text-black mb-3 font-jost">What information do you need for an accurate quote?</h3>
                  <p className="text-gray-600 font-jost">The more details you provide about your project goals, features, and timeline, the more accurate our quote will be. Don't worry if you're not sure about everything - we can help clarify during our consultation.</p>
                </div>
              </div>

              <div className="space-y-6">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-lg font-semibold text-black mb-3 font-jost">Can I modify the project scope after receiving a quote?</h3>
                  <p className="text-gray-600 font-jost">Of course! We understand that project requirements can evolve. We'll work with you to adjust the scope and provide updated pricing as needed.</p>
                </div>

                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-lg font-semibold text-black mb-3 font-jost">Do you offer payment plans?</h3>
                  <p className="text-gray-600 font-jost">Yes, we offer flexible payment plans for most projects. We typically work with milestone-based payments to ensure you're comfortable with the progress.</p>
                </div>

                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-lg font-semibold text-black mb-3 font-jost">What happens after I accept the quote?</h3>
                  <p className="text-gray-600 font-jost">Once you accept, we'll schedule a kickoff meeting to finalize details, create a project timeline, and begin the development process. You'll have a dedicated project manager throughout.</p>
                </div>
              </div>
            </div>
          </div>

          {/* Process Overview */}
          <div className="mt-16 bg-gray-50 p-12 rounded-2xl">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-black mb-4 font-jost">Our Quote Process</h2>
              <p className="text-lg text-gray-600 font-jost">Simple, transparent, and designed to get you the best results</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-black text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4 font-jost">1</div>
                <h3 className="text-lg font-semibold text-black mb-2 font-jost">Submit Request</h3>
                <p className="text-gray-600 text-sm font-jost">Fill out our detailed form with your project requirements and goals.</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-black text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4 font-jost">2</div>
                <h3 className="text-lg font-semibold text-black mb-2 font-jost">Review & Analysis</h3>
                <p className="text-gray-600 text-sm font-jost">Our team analyzes your requirements and may reach out for clarification.</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-black text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4 font-jost">3</div>
                <h3 className="text-lg font-semibold text-black mb-2 font-jost">Custom Proposal</h3>
                <p className="text-gray-600 text-sm font-jost">Receive a detailed proposal with timeline, pricing, and project breakdown.</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-black text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4 font-jost">4</div>
                <h3 className="text-lg font-semibold text-black mb-2 font-jost">Project Kickoff</h3>
                <p className="text-gray-600 text-sm font-jost">Once approved, we begin development with regular updates and milestones.</p>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default GetQuotePage;

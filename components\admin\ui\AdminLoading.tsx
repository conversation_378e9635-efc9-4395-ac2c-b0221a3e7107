import React from 'react';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AdminLoadingProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  className?: string;
  fullScreen?: boolean;
}

export default function AdminLoading({
  size = 'md',
  text = 'Loading...',
  className,
  fullScreen = false,
}: AdminLoadingProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  const content = (
    <div className={cn(
      'flex flex-col items-center justify-center space-y-3',
      className
    )}>
      <Loader2 className={cn(
        sizeClasses[size],
        'animate-spin text-gray-600'
      )} />
      {text && (
        <p className={cn(
          textSizeClasses[size],
          'text-gray-600 font-jost'
        )}>
          {text}
        </p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        {content}
      </div>
    );
  }

  return content;
}

// Skeleton Loading Components
interface AdminSkeletonProps {
  className?: string;
  width?: string;
  height?: string;
}

export function AdminSkeleton({
  className,
  width = 'w-full',
  height = 'h-4',
}: AdminSkeletonProps) {
  return (
    <div
      className={cn(
        'animate-pulse bg-gray-200 rounded',
        width,
        height,
        className
      )}
    />
  );
}

// Card Skeleton
export function AdminCardSkeleton() {
  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="animate-pulse">
        <div className="flex items-center space-x-4">
          <div className="h-12 w-12 bg-gray-200 rounded-lg"></div>
          <div className="flex-1 space-y-2">
            <AdminSkeleton height="h-4" width="w-3/4" />
            <AdminSkeleton height="h-6" width="w-1/2" />
          </div>
        </div>
      </div>
    </div>
  );
}

// Table Skeleton
interface AdminTableSkeletonProps {
  rows?: number;
  columns?: number;
}

export function AdminTableSkeleton({
  rows = 5,
  columns = 4,
}: AdminTableSkeletonProps) {
  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      {/* Header */}
      <div className="border-b border-gray-200 p-4">
        <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }).map((_, index) => (
            <AdminSkeleton key={index} height="h-4" width="w-3/4" />
          ))}
        </div>
      </div>
      
      {/* Rows */}
      <div className="divide-y divide-gray-200">
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div key={rowIndex} className="p-4">
            <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
              {Array.from({ length: columns }).map((_, colIndex) => (
                <AdminSkeleton key={colIndex} height="h-4" />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Stats Grid Skeleton
export function AdminStatsGridSkeleton() {
  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
      {Array.from({ length: 4 }).map((_, index) => (
        <AdminCardSkeleton key={index} />
      ))}
    </div>
  );
}

// Page Loading with Layout
export function AdminPageLoading() {
  return (
    <div className="space-y-6">
      {/* Header Skeleton */}
      <div className="space-y-2">
        <AdminSkeleton height="h-8" width="w-1/3" />
        <AdminSkeleton height="h-4" width="w-1/2" />
      </div>

      {/* Stats Grid Skeleton */}
      <AdminStatsGridSkeleton />

      {/* Content Skeleton */}
      <div className="space-y-4">
        <AdminSkeleton height="h-6" width="w-1/4" />
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, index) => (
            <AdminCardSkeleton key={index} />
          ))}
        </div>
      </div>
    </div>
  );
}

-- Fix RLS policies to allow anonymous users to submit applications
-- This migration updates the Row Level Security policies

-- Drop existing restrictive policies
DROP POLICY IF EXISTS "Anyone can submit project applications" ON project_applications;
DROP POLICY IF EXISTS "Anyone can submit job applications" ON job_applications;
DROP POLICY IF EXISTS "Users can view their own project applications" ON project_applications;
DROP POLICY IF EXISTS "Users can view their own job applications" ON job_applications;

-- Create new policies that allow anonymous submissions
CREATE POLICY "Allow anonymous project application submissions" ON project_applications
    FOR INSERT TO anon, authenticated WITH CHECK (true);

CREATE POLICY "Allow anonymous job application submissions" ON job_applications
    FOR INSERT TO anon, authenticated WITH CHECK (true);

-- Allow users to view their own applications (for authenticated users only)
CREATE POLICY "Users can view their own project applications" ON project_applications
    FOR SELECT TO authenticated USING (auth.jwt() ->> 'email' = email);

CREATE POLICY "Users can view their own job applications" ON job_applications
    FOR SELECT TO authenticated USING (auth.jwt() ->> 'email' = email);

-- Allow anonymous users to read their applications by email (optional - for confirmation pages)
CREATE POLICY "Allow reading project applications by email" ON project_applications
    FOR SELECT TO anon, authenticated USING (true);

CREATE POLICY "Allow reading job applications by email" ON job_applications
    FOR SELECT TO anon, authenticated USING (true);

-- Update grants to ensure anonymous users can insert
GRANT INSERT ON project_applications TO anon;
GRANT INSERT ON job_applications TO anon;
GRANT SELECT ON project_applications TO anon;
GRANT SELECT ON job_applications TO anon;

-- Ensure sequence permissions for auto-generated IDs
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO anon;

-- Add comments for clarity
COMMENT ON POLICY "Allow anonymous project application submissions" ON project_applications IS 
'Allows anyone (including anonymous users) to submit project applications';

COMMENT ON POLICY "Allow anonymous job application submissions" ON job_applications IS 
'Allows anyone (including anonymous users) to submit job applications';

COMMENT ON POLICY "Allow reading project applications by email" ON project_applications IS 
'Allows reading applications for confirmation and admin purposes';

COMMENT ON POLICY "Allow reading job applications by email" ON job_applications IS 
'Allows reading applications for confirmation and admin purposes';

import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Calendar, Tag, Zap } from 'lucide-react';

import Header from '@/components/Header';
import Footer from '@/components/Footer';
import TemplateGallery from '@/components/project/TemplateGallery';
import DemoButton from '@/components/project/DemoButton';
import { getTemplateBySlug, templates } from '@/lib/data/templates';

interface TemplatePageProps {
  params: {
    slug: string;
  };
}

// Generate static params for all templates
export async function generateStaticParams() {
  return templates.map((template) => ({
    slug: template.slug,
  }));
}

// Generate metadata for each template page
export async function generateMetadata({ params }: TemplatePageProps): Promise<Metadata> {
  const template = getTemplateBySlug(params.slug);

  if (!template) {
    return {
      title: 'Template Not Found | iREME Soft Hub',
    };
  }

  return {
    title: `${template.name} | iREME Soft Hub Templates`,
    description: template.shortDescription,
    keywords: [template.name, template.category, ...template.technologies],
    openGraph: {
      title: `${template.name} | iREME Soft Hub Templates`,
      description: template.shortDescription,
      images: [template.heroImage],
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: `${template.name} | iREME Soft Hub Templates`,
      description: template.shortDescription,
      images: [template.heroImage],
    },
  };
}

export default function TemplatePage({ params }: TemplatePageProps) {
  const template = getTemplateBySlug(params.slug);

  if (!template) {
    notFound();
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1 py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Breadcrumb Navigation */}
          <div className="mb-8">
            <Link
              href="/project"
              className="inline-flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors duration-200 font-jost"
            >
              <ArrowLeft size={16} />
              Back to Templates
            </Link>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-8">
              {/* Template Header */}
              <div>
                <div className="flex items-center gap-3 mb-4">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-black text-white">
                    {template.category}
                  </span>
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <Calendar size={14} />
                    Updated {new Date(template.updatedAt).toLocaleDateString()}
                  </div>
                </div>
                
                <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6 font-jost">
                  {template.name}
                </h1>
                
                <p className="text-xl text-gray-600 leading-relaxed font-jost">
                  {template.shortDescription}
                </p>
              </div>

              {/* Image Gallery */}
              <TemplateGallery
                images={template.images}
                templateName={template.name}
              />

              {/* Full Description */}
              <div className="prose prose-lg max-w-none">
                <h2 className="text-2xl font-bold text-gray-900 mb-4 font-jost">About This Template</h2>
                <p className="text-gray-600 leading-relaxed font-jost">
                  {template.fullDescription}
                </p>
              </div>

              {/* Features */}
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-6 font-jost flex items-center gap-2">
                  <Zap size={24} />
                  Key Features
                </h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {template.features.map((feature, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg"
                    >
                      <div className="w-2 h-2 bg-green-500 rounded-full flex-shrink-0" />
                      <span className="text-gray-700 font-jost">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-8">
              {/* Demo Button */}
              <div className="bg-white p-6 rounded-2xl shadow-lg border border-gray-100">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 font-jost">
                  Try Live Demo
                </h3>
                <p className="text-gray-600 mb-6 font-jost">
                  Experience the template in action with our live preview.
                </p>
                <DemoButton
                  demoUrl={template.demoUrl}
                  templateName={template.name}
                  variant="primary"
                  size="lg"
                  className="w-full"
                />
              </div>

              {/* Technologies */}
              <div className="bg-white p-6 rounded-2xl shadow-lg border border-gray-100">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 font-jost flex items-center gap-2">
                  <Tag size={18} />
                  Technologies Used
                </h3>
                <div className="flex flex-wrap gap-2">
                  {template.technologies.map((tech, index) => (
                    <span
                      key={index}
                      className="px-3 py-2 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium font-jost"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>

              {/* Template Info */}
              <div className="bg-white p-6 rounded-2xl shadow-lg border border-gray-100">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 font-jost">
                  Template Details
                </h3>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 font-jost">Category:</span>
                    <span className="text-gray-900 font-medium font-jost">{template.category}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 font-jost">Created:</span>
                    <span className="text-gray-900 font-medium font-jost">
                      {new Date(template.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 font-jost">Last Updated:</span>
                    <span className="text-gray-900 font-medium font-jost">
                      {new Date(template.updatedAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>

              {/* Contact CTA */}
              <div className="bg-gray-50 p-6 rounded-2xl">
                <h3 className="text-lg font-semibold text-gray-900 mb-3 font-jost">
                  Need Customization?
                </h3>
                <p className="text-gray-600 mb-4 text-sm font-jost">
                  Our team can customize this template to match your brand and requirements.
                </p>
                <Link
                  href="/contact-us"
                  className="inline-flex items-center justify-center w-full px-4 py-3 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors duration-200 text-sm font-medium font-jost"
                >
                  Get Custom Quote
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}

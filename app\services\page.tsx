'use client';

import React, { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import TrustedBrands from '@/components/TrustedBrands';
import Link from 'next/link';
import {
  ArrowRight,
  Code,
  Smartphone,
  Cloud,
  Palette,
  ShoppingCart,
  BarChart3,
  CheckCircle,
  Users,
  Clock,
  Shield,
  Zap,
  ChevronDown,
  ChevronUp,
  Star,
  Quote
} from 'lucide-react';

interface Service {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  features: string[];
  benefits: string[];
  startingPrice: string;
  timeline: string;
  href: string;
}

interface ProcessStep {
  step: number;
  title: string;
  description: string;
  icon: React.ReactNode;
}

interface FAQ {
  question: string;
  answer: string;
}

interface Testimonial {
  name: string;
  company: string;
  role: string;
  content: string;
  rating: number;
}

export default function Services() {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  const services: Service[] = [
    {
      id: 'web-development',
      title: 'Web Development',
      description: 'Custom web applications built with cutting-edge technologies like Next.js, React, and TypeScript. We create scalable, fast, and SEO-optimized websites that drive results.',
      icon: <Code className="w-8 h-8" />,
      features: [
        'Next.js 14+ with App Router',
        'TypeScript & Modern JavaScript',
        'Responsive Design',
        'SEO Optimization',
        'Performance Optimization',
        'Database Integration'
      ],
      benefits: [
        'Faster loading times boost conversions',
        'Mobile-first design reaches more customers',
        'SEO optimization increases visibility',
        'Scalable architecture grows with your business'
      ],
      startingPrice: '$5,000',
      timeline: '4-8 weeks',
      href: '/services/web-development'
    },
    {
      id: 'mobile-apps',
      title: 'Mobile App Development',
      description: 'Native iOS and Android applications plus cross-platform solutions using React Native and Flutter. We build apps that users love and businesses rely on.',
      icon: <Smartphone className="w-8 h-8" />,
      features: [
        'Native iOS & Android',
        'Cross-platform Development',
        'App Store Optimization',
        'Push Notifications',
        'Offline Functionality',
        'Analytics Integration'
      ],
      benefits: [
        'Reach customers on their preferred platform',
        'Increase engagement with push notifications',
        'Generate revenue through app monetization',
        'Build stronger customer relationships'
      ],
      startingPrice: '$10,000',
      timeline: '8-16 weeks',
      href: '/services/mobile-development'
    },
    {
      id: 'cloud-solutions',
      title: 'Cloud Solutions',
      description: 'Scalable cloud infrastructure, deployment automation, and DevOps solutions. We help you leverage AWS, Google Cloud, and Azure for maximum efficiency.',
      icon: <Cloud className="w-8 h-8" />,
      features: [
        'AWS, GCP, Azure Setup',
        'CI/CD Pipeline Implementation',
        'Auto-scaling Infrastructure',
        'Security & Compliance',
        'Monitoring & Analytics',
        'Cost Optimization'
      ],
      benefits: [
        'Reduce infrastructure costs by up to 40%',
        'Improve application reliability and uptime',
        'Scale automatically based on demand',
        'Enhanced security and compliance'
      ],
      startingPrice: '$7,500',
      timeline: '6-12 weeks',
      href: '/services/cloud-solutions'
    },
    {
      id: 'ui-ux-design',
      title: 'UI/UX Design',
      description: 'User-centered design that converts visitors into customers. We create intuitive interfaces and seamless user experiences that drive business growth.',
      icon: <Palette className="w-8 h-8" />,
      features: [
        'User Research & Analysis',
        'Wireframing & Prototyping',
        'Visual Design Systems',
        'Usability Testing',
        'Responsive Design',
        'Accessibility Compliance'
      ],
      benefits: [
        'Increase conversion rates by up to 200%',
        'Reduce user acquisition costs',
        'Improve customer satisfaction scores',
        'Build stronger brand recognition'
      ],
      startingPrice: '$4,000',
      timeline: '3-6 weeks',
      href: '/services/ui-ux-design'
    },
    {
      id: 'ecommerce',
      title: 'E-commerce Solutions',
      description: 'Complete e-commerce platforms that sell. From Shopify customizations to custom-built solutions, we create online stores that convert browsers into buyers.',
      icon: <ShoppingCart className="w-8 h-8" />,
      features: [
        'Custom E-commerce Development',
        'Payment Gateway Integration',
        'Inventory Management',
        'Order Processing Automation',
        'Multi-channel Selling',
        'Analytics & Reporting'
      ],
      benefits: [
        'Increase online sales by up to 150%',
        'Reduce cart abandonment rates',
        'Automate order processing',
        'Expand to new sales channels'
      ],
      startingPrice: '$8,000',
      timeline: '6-10 weeks',
      href: '/services/ecommerce'
    },
    {
      id: 'digital-marketing',
      title: 'Digital Marketing',
      description: 'Data-driven marketing strategies that generate leads and drive growth. SEO, PPC, content marketing, and social media management that delivers ROI.',
      icon: <BarChart3 className="w-8 h-8" />,
      features: [
        'SEO & Content Strategy',
        'Google Ads Management',
        'Social Media Marketing',
        'Email Marketing Automation',
        'Analytics & Reporting',
        'Conversion Optimization'
      ],
      benefits: [
        'Generate 3x more qualified leads',
        'Improve search engine rankings',
        'Increase brand awareness by 250%',
        'Achieve measurable ROI on ad spend'
      ],
      startingPrice: '$3,000',
      timeline: '2-4 weeks setup',
      href: '/services/digital-marketing'
    }
  ];

  const processSteps: ProcessStep[] = [
    {
      step: 1,
      title: 'Discovery & Strategy',
      description: 'We start by understanding your business goals, target audience, and technical requirements. Our team conducts thorough research to create a strategic roadmap.',
      icon: <Users className="w-6 h-6" />
    },
    {
      step: 2,
      title: 'Design & Planning',
      description: 'Our designers create wireframes and prototypes while our developers plan the technical architecture. You\'ll see exactly what we\'re building before we build it.',
      icon: <Palette className="w-6 h-6" />
    },
    {
      step: 3,
      title: 'Development & Testing',
      description: 'We build your solution using agile methodology with regular check-ins. Rigorous testing ensures everything works perfectly before launch.',
      icon: <Code className="w-6 h-6" />
    },
    {
      step: 4,
      title: 'Launch & Support',
      description: 'We handle the deployment and provide ongoing support. Your success is our success, so we\'re here for maintenance, updates, and growth.',
      icon: <Zap className="w-6 h-6" />
    }
  ];

  const faqs: FAQ[] = [
    {
      question: 'How long does a typical project take?',
      answer: 'Project timelines vary based on complexity and scope. Simple websites take 4-6 weeks, while complex applications can take 12-20 weeks. We provide detailed timelines during our initial consultation and keep you updated throughout the process.'
    },
    {
      question: 'What\'s included in your pricing?',
      answer: 'Our pricing includes strategy, design, development, testing, deployment, and 30 days of post-launch support. We provide transparent pricing with no hidden fees. Additional features or ongoing maintenance are clearly outlined separately.'
    },
    {
      question: 'Do you work with startups or only established businesses?',
      answer: 'We work with businesses of all sizes, from early-stage startups to Fortune 500 companies. We have flexible engagement models and pricing structures to accommodate different budgets and growth stages.'
    },
    {
      question: 'Can you help with ongoing maintenance and updates?',
      answer: 'Absolutely! We offer comprehensive maintenance packages including security updates, performance monitoring, content updates, and feature enhancements. We believe in long-term partnerships with our clients.'
    },
    {
      question: 'What technologies do you specialize in?',
      answer: 'We specialize in modern technologies including Next.js, React, TypeScript, Node.js, Python, AWS, and more. We choose the best technology stack for each project based on your specific requirements and goals.'
    },
    {
      question: 'How do you ensure project success?',
      answer: 'We use proven methodologies including agile development, regular client communication, milestone-based delivery, and comprehensive testing. Our 95% client satisfaction rate speaks to our commitment to project success.'
    }
  ];

  const testimonials: Testimonial[] = [
    {
      name: 'Sarah Johnson',
      company: 'TechStart Inc.',
      role: 'CEO',
      content: 'iREME Soft Hub transformed our vision into a powerful web application that exceeded our expectations. Their expertise in Next.js and attention to detail is unmatched.',
      rating: 5
    },
    {
      name: 'Michael Chen',
      company: 'E-commerce Plus',
      role: 'Founder',
      content: 'Our online sales increased by 180% after iREME Soft Hub redesigned our e-commerce platform. The new system is fast, user-friendly, and converts amazingly well.',
      rating: 5
    },
    {
      name: 'Emily Rodriguez',
      company: 'HealthTech Solutions',
      role: 'CTO',
      content: 'The mobile app they built for us has over 50,000 active users. The development process was smooth, and they delivered exactly what we needed on time and on budget.',
      rating: 5
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            {/* Badge */}
            <div className="inline-flex items-center bg-black text-white px-4 py-2 rounded-full text-sm mb-8 hover:scale-105 transition-all duration-300 cursor-pointer animate-fade-in font-jost">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
              Elite Development Services - Proven Results
            </div>

            {/* Main Heading */}
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-black mb-6 leading-tight animate-slide-up font-jost">
              Transform Your Ideas Into<br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
                Powerful Digital Solutions
              </span>
            </h1>

            {/* Subheading */}
            <p className="text-xl text-gray-600 max-w-4xl mx-auto mb-8 leading-relaxed font-jost">
              We build scalable web applications, mobile apps, and cloud solutions that drive real business results.
              From startups to enterprises, we turn your vision into reality with cutting-edge technology and proven expertise.
            </p>

            {/* Stats */}
            <div className="flex flex-col sm:flex-row items-center justify-center gap-8 mb-12">
              <div className="text-center">
                <div className="text-3xl font-bold text-black font-jost">500+</div>
                <div className="text-gray-600 font-jost">Projects Delivered</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-black font-jost">95%</div>
                <div className="text-gray-600 font-jost">Client Satisfaction</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-black font-jost">24/7</div>
                <div className="text-gray-600 font-jost">Support Available</div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-16">
              <Link
                href="/get-quote"
                className="bg-black text-white px-8 py-4 rounded-full hover:bg-gray-800 transition-all duration-500 flex items-center gap-2 text-base sm:text-lg font-medium group relative overflow-hidden hover:shadow-lg transform hover:-translate-y-1 font-jost"
              >
                <span className="relative z-10 transition-transform duration-500 group-hover:translate-x-[-8px] text-sm sm:text-base">
                  Get Your Free Quote
                </span>
                <ArrowRight
                  size={20}
                  className="relative z-10 transition-all duration-500 group-hover:translate-x-[8px] group-hover:scale-125"
                />
                <div className="absolute inset-0 bg-gray-800 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-500 ease-in-out"></div>
              </Link>

              <Link
                href="/project"
                className="border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-full hover:border-gray-400 hover:bg-gray-50 transition-all duration-500 flex items-center gap-2 text-sm sm:text-lg font-medium hover:shadow-lg transform hover:-translate-y-1 font-jost"
              >
                View Our Work
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              Our Services
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto font-jost">
              We offer comprehensive development services designed to accelerate your business growth
              and deliver measurable results. Each service is tailored to your specific needs and goals.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div
                key={service.id}
                className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group"
              >
                {/* Service Icon */}
                <div className="flex items-center justify-center w-16 h-16 bg-black text-white rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300">
                  {service.icon}
                </div>

                {/* Service Title */}
                <h3 className="text-2xl font-bold text-black mb-4 font-jost">
                  {service.title}
                </h3>

                {/* Service Description */}
                <p className="text-gray-600 mb-6 leading-relaxed font-jost">
                  {service.description}
                </p>

                {/* Key Features */}
                <div className="mb-6">
                  <h4 className="text-sm font-semibold text-black mb-3 font-jost uppercase tracking-wide">
                    What's Included:
                  </h4>
                  <ul className="space-y-2">
                    {service.features.slice(0, 4).map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600 font-jost">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Pricing and Timeline */}
                <div className="flex items-center justify-between mb-6 p-4 bg-gray-50 rounded-xl">
                  <div>
                    <div className="text-sm text-gray-500 font-jost">Starting at</div>
                    <div className="text-xl font-bold text-black font-jost">{service.startingPrice}</div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-gray-500 font-jost">Timeline</div>
                    <div className="text-sm font-semibold text-black font-jost">{service.timeline}</div>
                  </div>
                </div>

                {/* CTA Button */}
                <Link
                  href={service.href}
                  className="w-full bg-black text-white py-3 px-6 rounded-full hover:bg-gray-800 transition-all duration-300 flex items-center justify-center gap-2 font-medium font-jost group"
                >
                  Learn More
                  <ArrowRight className="w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" />
                </Link>
              </div>
            ))}
          </div>

          {/* Bottom CTA */}
          <div className="text-center mt-16">
            <p className="text-lg text-gray-600 mb-6 font-jost">
              Need a custom solution? We create tailored services for unique business requirements.
            </p>
            <Link
              href="/get-quote"
              className="inline-flex items-center bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-full hover:from-blue-700 hover:to-purple-700 transition-all duration-300 font-medium font-jost group"
            >
              Discuss Your Project
              <ArrowRight className="w-5 h-5 ml-2 transition-transform duration-300 group-hover:translate-x-1" />
            </Link>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              How We Work
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto font-jost">
              Our proven 4-step process ensures your project is delivered on time, on budget,
              and exceeds your expectations. We believe in transparency and collaboration every step of the way.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {processSteps.map((step, index) => (
              <div key={step.step} className="text-center group">
                {/* Step Number */}
                <div className="relative mb-6">
                  <div className="flex items-center justify-center w-16 h-16 bg-black text-white rounded-full mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    {step.icon}
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold font-jost">
                    {step.step}
                  </div>
                  {/* Connector Line */}
                  {index < processSteps.length - 1 && (
                    <div className="hidden lg:block absolute top-8 left-full w-full h-0.5 bg-gray-300 transform -translate-y-1/2"></div>
                  )}
                </div>

                {/* Step Content */}
                <h3 className="text-xl font-bold text-black mb-4 font-jost">
                  {step.title}
                </h3>
                <p className="text-gray-600 leading-relaxed font-jost">
                  {step.description}
                </p>
              </div>
            ))}
          </div>

          {/* Process CTA */}
          <div className="text-center mt-16">
            <div className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100 max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold text-black mb-4 font-jost">
                Ready to Get Started?
              </h3>
              <p className="text-gray-600 mb-6 font-jost">
                Let's discuss your project and create a custom solution that drives results for your business.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/get-quote"
                  className="bg-black text-white px-6 py-3 rounded-full hover:bg-gray-800 transition-colors duration-300 font-medium font-jost"
                >
                  Start Your Project
                </Link>
                <Link
                  href="/contact-us"
                  className="border-2 border-gray-300 text-gray-700 px-6 py-3 rounded-full hover:border-gray-400 hover:bg-gray-50 transition-all duration-300 font-medium font-jost"
                >
                  Schedule a Call
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Social Proof Section */}
      <TrustedBrands />

      {/* Testimonials Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              What Our Clients Say
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto font-jost">
              Don't just take our word for it. Here's what business leaders say about working with iREME Soft Hub.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div
                key={index}
                className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300 relative"
              >
                {/* Quote Icon */}
                <Quote className="w-8 h-8 text-blue-600 mb-4" />

                {/* Rating */}
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>

                {/* Testimonial Content */}
                <p className="text-gray-600 mb-6 leading-relaxed font-jost italic">
                  "{testimonial.content}"
                </p>

                {/* Author Info */}
                <div className="border-t border-gray-100 pt-4">
                  <div className="font-semibold text-black font-jost">
                    {testimonial.name}
                  </div>
                  <div className="text-sm text-gray-500 font-jost">
                    {testimonial.role} at {testimonial.company}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Testimonial CTA */}
          <div className="text-center mt-16">
            <p className="text-lg text-gray-600 mb-6 font-jost">
              Join hundreds of satisfied clients who trust iREME Soft Hub with their digital transformation.
            </p>
            <Link
              href="/get-quote"
              className="inline-flex items-center bg-black text-white px-8 py-4 rounded-full hover:bg-gray-800 transition-colors duration-300 font-medium font-jost group"
            >
              Start Your Success Story
              <ArrowRight className="w-5 h-5 ml-2 transition-transform duration-300 group-hover:translate-x-1" />
            </Link>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-gray-600 font-jost">
              Get answers to common questions about our services, process, and pricing.
            </p>
          </div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div
                key={index}
                className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"
              >
                <button
                  onClick={() => setOpenFAQ(openFAQ === index ? null : index)}
                  className="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
                >
                  <h3 className="text-lg font-semibold text-black font-jost pr-4">
                    {faq.question}
                  </h3>
                  {openFAQ === index ? (
                    <ChevronUp className="w-5 h-5 text-gray-500 flex-shrink-0" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-gray-500 flex-shrink-0" />
                  )}
                </button>

                {openFAQ === index && (
                  <div className="px-8 pb-6">
                    <p className="text-gray-600 leading-relaxed font-jost">
                      {faq.answer}
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* FAQ CTA */}
          <div className="text-center mt-12">
            <p className="text-gray-600 mb-4 font-jost">
              Still have questions? We're here to help.
            </p>
            <Link
              href="/contact-us"
              className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium font-jost group"
            >
              Contact Our Team
              <ArrowRight className="w-4 h-4 ml-1 transition-transform duration-300 group-hover:translate-x-1" />
            </Link>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-black to-gray-800">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl sm:text-5xl font-bold text-white mb-6 font-jost">
            Ready to Transform Your Business?
          </h2>
          <p className="text-xl text-gray-300 mb-8 leading-relaxed font-jost">
            Join the ranks of successful businesses that have accelerated their growth with our expert development services.
            Let's build something amazing together.
          </p>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-8">
            <Link
              href="/get-quote"
              className="bg-white text-black px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 flex items-center gap-2 font-medium font-jost group"
            >
              Get Your Free Quote
              <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
            </Link>
            <Link
              href="/project"
              className="border-2 border-white text-white px-8 py-4 rounded-full hover:bg-white hover:text-black transition-all duration-300 font-medium font-jost"
            >
              View Our Portfolio
            </Link>
          </div>

          {/* Contact Info */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-gray-300">
            <div className="flex items-center gap-2">
              <Clock className="w-5 h-5" />
              <span className="font-jost">24/7 Support Available</span>
            </div>
            <div className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              <span className="font-jost">100% Satisfaction Guarantee</span>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import AdminProtectedRoute from '@/components/admin/auth/AdminProtectedRoute';
import AdminLayout from '@/components/admin/layout/AdminLayout';
import TemplateForm from '@/components/admin/templates/TemplateForm';
import { ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';
import { AdminTemplate } from '@/lib/types/admin';

export default function NewTemplatePage() {
  const router = useRouter();

  const handleSave = (template: AdminTemplate) => {
    router.push('/admin/templates');
  };

  const handleCancel = () => {
    router.push('/admin/templates');
  };

  return (
    <AdminProtectedRoute requiredPermission={ADMIN_PERMISSIONS.TEMPLATES_CREATE}>
      <AdminLayout 
        title="New Template" 
        subtitle="Create a new project template"
      >
        <TemplateForm onSave={handleSave} onCancel={handleCancel} />
      </AdminLayout>
    </AdminProtectedRoute>
  );
}

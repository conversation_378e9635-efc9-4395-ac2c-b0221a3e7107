'use client';

import React, { useState, useEffect } from 'react';
import { AdminCard, AdminStatCard } from '@/components/admin/ui/AdminCard';
import AdminButton from '@/components/admin/ui/AdminButton';
import { AdminTableSkeleton } from '@/components/admin/ui/AdminLoading';
import { useAdminAuth, ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';
import { AdminPermissionWrapper } from '@/components/admin/auth/AdminProtectedRoute';
import { adminAnalytics } from '@/lib/supabase/admin';
import { DashboardStats, TopPerformingContent } from '@/lib/types/admin';
import {
  TrendingUp,
  TrendingDown,
  Eye,
  Users,
  DollarSign,
  Clock,
  Download,
  Calendar,
  Filter,
  RefreshCw,
  BarChart3,
  PieChart,
  LineChart,
  Activity,
} from 'lucide-react';

interface DateRange {
  start: string;
  end: string;
  label: string;
}

export default function AnalyticsDashboard() {
  const { hasPermission } = useAdminAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [topContent, setTopContent] = useState<TopPerformingContent[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRange, setSelectedRange] = useState<DateRange>({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] || '',
    end: new Date().toISOString().split('T')[0] || '',
    label: 'Last 30 days',
  });

  const dateRanges: DateRange[] = [
    {
      start: new Date().toISOString().split('T')[0] || '',
      end: new Date().toISOString().split('T')[0] || '',
      label: 'Today',
    },
    {
      start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] || '',
      end: new Date().toISOString().split('T')[0] || '',
      label: 'Last 7 days',
    },
    {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] || '',
      end: new Date().toISOString().split('T')[0] || '',
      label: 'Last 30 days',
    },
    {
      start: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] || '',
      end: new Date().toISOString().split('T')[0] || '',
      label: 'Last 90 days',
    },
  ];

  // Fetch analytics data
  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      
      // Fetch dashboard stats
      const dashboardStats = await adminAnalytics.getDashboardStats(
        selectedRange.start,
        selectedRange.end
      );
      setStats(dashboardStats);

      // Fetch top performing content
      const topPerformingContent = await adminAnalytics.getTopPerformingContent(10, 'all');
      setTopContent(topPerformingContent);
    } catch (error) {
      console.error('Error fetching analytics:', error);
      
      // Mock data for development
      const mockStats: DashboardStats = {
        totalPageViews: 12345,
        uniqueVisitors: 8901,
        avgSessionDuration: 245,
        bounceRate: 32.5,
        newLeads: 23,
        conversionRate: 4.2,
        totalRevenue: 125000,
        avgProjectValue: 5434,
      };
      
      const mockTopContent: TopPerformingContent[] = [
        {
          id: '1',
          title: 'E-commerce Website Template',
          type: 'template',
          views: 1250,
          conversions: 45,
          conversionRate: 3.6,
          revenue: 22500,
        },
        {
          id: '2',
          title: 'Mobile App Development Service',
          type: 'service',
          views: 890,
          conversions: 12,
          conversionRate: 1.3,
          revenue: 48000,
        },
        {
          id: '3',
          title: 'How to Build a Modern Web App',
          type: 'blog',
          views: 2340,
          conversions: 8,
          conversionRate: 0.3,
          revenue: 0,
        },
      ];
      
      setStats(mockStats);
      setTopContent(mockTopContent);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, [selectedRange]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  if (loading) {
    return <AdminTableSkeleton rows={4} columns={4} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 font-jost">Analytics Dashboard</h1>
          <p className="text-gray-600 font-jost">Track your business performance and insights</p>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* Date Range Selector */}
          <select
            value={selectedRange.label}
            onChange={(e) => {
              const range = dateRanges.find(r => r.label === e.target.value);
              if (range) setSelectedRange(range);
            }}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
          >
            {dateRanges.map(range => (
              <option key={range.label} value={range.label}>{range.label}</option>
            ))}
          </select>

          <AdminButton 
            variant="outline" 
            onClick={fetchAnalytics}
            icon={<RefreshCw className="h-4 w-4" />}
          >
            Refresh
          </AdminButton>

          <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.ANALYTICS_EXPORT}>
            <AdminButton 
              variant="outline"
              icon={<Download className="h-4 w-4" />}
            >
              Export
            </AdminButton>
          </AdminPermissionWrapper>
        </div>
      </div>

      {/* Key Metrics */}
      {stats && (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          <AdminStatCard
            title="Total Page Views"
            value={formatNumber(stats.totalPageViews)}
            change={{ value: '+12%', type: 'positive' }}
            icon={<Eye className="h-6 w-6 text-gray-600" />}
          />
          <AdminStatCard
            title="Unique Visitors"
            value={formatNumber(stats.uniqueVisitors)}
            change={{ value: '+8%', type: 'positive' }}
            icon={<Users className="h-6 w-6 text-gray-600" />}
          />
          <AdminStatCard
            title="New Leads"
            value={formatNumber(stats.newLeads)}
            change={{ value: '+15%', type: 'positive' }}
            icon={<TrendingUp className="h-6 w-6 text-gray-600" />}
          />
          <AdminStatCard
            title="Total Revenue"
            value={formatCurrency(stats.totalRevenue)}
            change={{ value: '+22%', type: 'positive' }}
            icon={<DollarSign className="h-6 w-6 text-gray-600" />}
          />
        </div>
      )}

      {/* Secondary Metrics */}
      {stats && (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          <AdminStatCard
            title="Avg Session Duration"
            value={formatDuration(stats.avgSessionDuration)}
            change={{ value: '+5%', type: 'positive' }}
            icon={<Clock className="h-6 w-6 text-gray-600" />}
          />
          <AdminStatCard
            title="Bounce Rate"
            value={formatPercentage(stats.bounceRate)}
            change={{ value: '-3%', type: 'positive' }}
            icon={<Activity className="h-6 w-6 text-gray-600" />}
          />
          <AdminStatCard
            title="Conversion Rate"
            value={formatPercentage(stats.conversionRate)}
            change={{ value: '+0.8%', type: 'positive' }}
            icon={<TrendingUp className="h-6 w-6 text-gray-600" />}
          />
          <AdminStatCard
            title="Avg Project Value"
            value={formatCurrency(stats.avgProjectValue)}
            change={{ value: '+12%', type: 'positive' }}
            icon={<DollarSign className="h-6 w-6 text-gray-600" />}
          />
        </div>
      )}

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Traffic Chart Placeholder */}
        <AdminCard>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 font-jost">Traffic Overview</h3>
            <BarChart3 className="h-5 w-5 text-gray-400" />
          </div>
          <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <LineChart className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500 font-jost">Traffic chart will be displayed here</p>
              <p className="text-sm text-gray-400 font-jost">Integration with charting library needed</p>
            </div>
          </div>
        </AdminCard>

        {/* Conversion Funnel Placeholder */}
        <AdminCard>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 font-jost">Conversion Funnel</h3>
            <PieChart className="h-5 w-5 text-gray-400" />
          </div>
          <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <PieChart className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500 font-jost">Conversion funnel will be displayed here</p>
              <p className="text-sm text-gray-400 font-jost">Shows visitor to customer journey</p>
            </div>
          </div>
        </AdminCard>
      </div>

      {/* Top Performing Content */}
      <AdminCard>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-gray-900 font-jost">Top Performing Content</h3>
          <AdminButton variant="outline" size="sm">
            View All
          </AdminButton>
        </div>

        {topContent.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-jost">
                    Content
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-jost">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-jost">
                    Views
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-jost">
                    Conversions
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-jost">
                    Conv. Rate
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-jost">
                    Revenue
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {topContent.map((content, index) => (
                  <tr key={content.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-8 w-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                          <span className="text-sm font-medium text-gray-600">#{index + 1}</span>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900 font-jost">
                            {content.title}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        content.type === 'template' ? 'bg-blue-100 text-blue-800' :
                        content.type === 'service' ? 'bg-green-100 text-green-800' :
                        'bg-purple-100 text-purple-800'
                      }`}>
                        {content.type}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-jost">
                      {formatNumber(content.views)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-jost">
                      {formatNumber(content.conversions)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-jost">
                      {formatPercentage(content.conversionRate)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-jost">
                      {content.revenue > 0 ? formatCurrency(content.revenue) : '-'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-8">
            <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 font-jost">No performance data available for the selected period</p>
          </div>
        )}
      </AdminCard>
    </div>
  );
}

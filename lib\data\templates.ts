import { Template } from '@/lib/types';

// Generate local SVG placeholder data URLs
const createPlaceholder = (text: string) => {
  const svgContent = `
    <svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#f3f4f6"/>
      <text x="50%" y="50%" font-family="system-ui, sans-serif" font-size="32"
            fill="#6b7280" text-anchor="middle" dominant-baseline="middle">
        ${text}
      </text>
    </svg>
  `;
  return `data:image/svg+xml;base64,${btoa(svgContent)}`;
};

export const templates: Template[] = [
  {
    id: '1',
    slug: 'modern-ecommerce-platform',
    name: 'Modern E-commerce Platform',
    shortDescription: 'A comprehensive e-commerce solution with advanced features, seamless checkout, and modern design.',
    fullDescription: 'This modern e-commerce platform combines cutting-edge design with powerful functionality. Built with Next.js 14 and featuring a responsive design, advanced product filtering, secure payment integration, and comprehensive admin dashboard. Perfect for businesses looking to establish a strong online presence with scalable architecture.',
    heroImage: createPlaceholder('Modern E-commerce Platform'),
    images: [
      createPlaceholder('E-commerce Hero'),
      createPlaceholder('Product Grid'),
      createPlaceholder('Product Detail'),
      createPlaceholder('Checkout Process'),
      createPlaceholder('Admin Dashboard')
    ],
    demoUrl: 'https://ecommerce-demo.vercel.app',
    category: 'E-commerce',
    technologies: ['Next.js 14', 'TypeScript', 'Tailwind CSS', 'Stripe', 'Supabase', 'Framer Motion'],
    features: [
      'Responsive Design',
      'Product Management',
      'Shopping Cart',
      'Secure Payments',
      'User Authentication',
      'Admin Dashboard',
      'Order Tracking',
      'Inventory Management'
    ],
    createdAt: '2024-01-15',
    updatedAt: '2024-01-20'
  },
  {
    id: '2',
    slug: 'creative-portfolio-showcase',
    name: 'Creative Portfolio Showcase',
    shortDescription: 'Stunning portfolio template for designers, photographers, and creative professionals.',
    fullDescription: 'Showcase your creative work with this elegant portfolio template. Features smooth animations, interactive galleries, and a clean, minimalist design that puts your work front and center. Built with performance in mind and optimized for all devices.',
    heroImage: createPlaceholder('Creative Portfolio Showcase'),
    images: [
      createPlaceholder('Portfolio Hero'),
      createPlaceholder('Gallery View'),
      createPlaceholder('About Page'),
      createPlaceholder('Contact Form')
    ],
    demoUrl: 'https://portfolio-demo.vercel.app',
    category: 'Portfolio',
    technologies: ['Next.js 14', 'TypeScript', 'Tailwind CSS', 'Framer Motion', 'Sanity CMS'],
    features: [
      'Interactive Gallery',
      'Smooth Animations',
      'Contact Forms',
      'Blog Integration',
      'SEO Optimized',
      'Mobile Responsive',
      'Fast Loading'
    ],
    createdAt: '2024-01-10',
    updatedAt: '2024-01-18'
  },
  {
    id: '3',
    slug: 'saas-landing-page',
    name: 'SaaS Landing Page',
    shortDescription: 'High-converting landing page template designed specifically for SaaS products.',
    fullDescription: 'Drive conversions with this professionally designed SaaS landing page. Features compelling hero sections, feature highlights, pricing tables, testimonials, and strong call-to-action elements. Optimized for conversion and built with modern web standards.',
    heroImage: createPlaceholder('SaaS Landing Page'),
    images: [
      createPlaceholder('SaaS Hero Section'),
      createPlaceholder('Features Overview'),
      createPlaceholder('Pricing Tables'),
      createPlaceholder('Customer Testimonials')
    ],
    demoUrl: 'https://saas-landing-demo.vercel.app',
    category: 'SaaS',
    technologies: ['Next.js 14', 'TypeScript', 'Tailwind CSS', 'React Hook Form', 'Lucide Icons'],
    features: [
      'Hero Section',
      'Feature Showcase',
      'Pricing Tables',
      'Testimonials',
      'FAQ Section',
      'Newsletter Signup',
      'Analytics Ready'
    ],
    createdAt: '2024-01-12',
    updatedAt: '2024-01-22'
  },
  {
    id: '4',
    slug: 'business-corporate-website',
    name: 'Business Corporate Website',
    shortDescription: 'Professional corporate website template for established businesses and enterprises.',
    fullDescription: 'Establish credibility with this professional corporate website template. Features comprehensive service pages, team profiles, case studies, and contact forms. Perfect for consulting firms, agencies, and established businesses looking to strengthen their online presence.',
    heroImage: createPlaceholder('Business Corporate Website'),
    images: [
      createPlaceholder('Corporate Hero'),
      createPlaceholder('Services Page'),
      createPlaceholder('Team Profiles'),
      createPlaceholder('Contact Information')
    ],
    demoUrl: 'https://business-demo.vercel.app',
    category: 'Business',
    technologies: ['Next.js 14', 'TypeScript', 'Tailwind CSS', 'React Hook Form', 'Next SEO'],
    features: [
      'Service Pages',
      'Team Profiles',
      'Case Studies',
      'Contact Forms',
      'Blog Section',
      'SEO Optimized',
      'Multi-language Support'
    ],
    createdAt: '2024-01-08',
    updatedAt: '2024-01-16'
  },
  {
    id: '5',
    slug: 'modern-blog-platform',
    name: 'Modern Blog Platform',
    shortDescription: 'Feature-rich blog template with CMS integration and modern design aesthetics.',
    fullDescription: 'Create engaging content with this modern blog platform. Features article management, category filtering, search functionality, and social sharing. Built with headless CMS integration for easy content management and optimized for SEO.',
    heroImage: createPlaceholder('Modern Blog Platform'),
    images: [
      createPlaceholder('Blog Homepage'),
      createPlaceholder('Article Listing'),
      createPlaceholder('Article Detail'),
      createPlaceholder('Category Pages')
    ],
    demoUrl: 'https://blog-demo.vercel.app',
    category: 'Blog',
    technologies: ['Next.js 14', 'TypeScript', 'Tailwind CSS', 'MDX', 'Contentful', 'Algolia Search'],
    features: [
      'Article Management',
      'Category Filtering',
      'Search Functionality',
      'Social Sharing',
      'Comment System',
      'Newsletter Integration',
      'RSS Feed'
    ],
    createdAt: '2024-01-05',
    updatedAt: '2024-01-14'
  },
  {
    id: '6',
    slug: 'analytics-dashboard',
    name: 'Analytics Dashboard',
    shortDescription: 'Comprehensive dashboard template with charts, metrics, and data visualization.',
    fullDescription: 'Visualize your data with this comprehensive analytics dashboard. Features interactive charts, real-time metrics, customizable widgets, and responsive design. Perfect for SaaS applications, business intelligence tools, and data-driven applications.',
    heroImage: createPlaceholder('Analytics Dashboard'),
    images: [
      createPlaceholder('Dashboard Overview'),
      createPlaceholder('Charts & Analytics'),
      createPlaceholder('Data Tables'),
      createPlaceholder('Settings Panel')
    ],
    demoUrl: 'https://dashboard-demo.vercel.app',
    category: 'Dashboard',
    technologies: ['Next.js 14', 'TypeScript', 'Tailwind CSS', 'Chart.js', 'React Query', 'Zustand'],
    features: [
      'Interactive Charts',
      'Real-time Data',
      'Customizable Widgets',
      'Data Export',
      'User Management',
      'Dark Mode',
      'Mobile Responsive'
    ],
    createdAt: '2024-01-03',
    updatedAt: '2024-01-12'
  }
];

// Helper functions
export const getTemplateBySlug = (slug: string): Template | undefined => {
  return templates.find(template => template.slug === slug);
};

export const getTemplatesByCategory = (category: string): Template[] => {
  return templates.filter(template => template.category === category);
};

export const getAllCategories = (): string[] => {
  return Array.from(new Set(templates.map(template => template.category)));
};

import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Contact Us - iREME Soft Hub | Get In Touch Today',
  description: 'Contact iREME Soft Hub for professional software development services in Rwanda. Call +*********** 255 or send us a message. Based in Musanze, Rwanda.',
  keywords: 'contact iREME Soft Hub, software development Rwanda, Musanze tech company, web development contact, mobile app development Rwanda',
  openGraph: {
    title: 'Contact iREME Soft Hub - Professional Software Development',
    description: 'Get in touch with Rwanda\'s leading software development company. Professional web and mobile app development services.',
    type: 'website',
    url: '/contact-us',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Contact iREME Soft Hub - Professional Software Development',
    description: 'Get in touch with Rwanda\'s leading software development company.',
  },
  alternates: {
    canonical: '/contact-us',
  },
};

export default function ContactLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}

'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import AdminProtectedRoute from '@/components/admin/auth/AdminProtectedRoute';
import AdminLayout from '@/components/admin/layout/AdminLayout';
import UserList from '@/components/admin/users/UserList';
import { ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';
import { AdminUser } from '@/lib/types/admin';

export default function AdminUsersPage() {
  const router = useRouter();

  const handleEdit = (user: AdminUser) => {
    router.push(`/admin/users/${user.id}/edit`);
  };

  const handleDelete = (user: AdminUser) => {
    // User deletion is handled in the UserList component
    console.log('User deleted:', user.email);
  };

  return (
    <AdminProtectedRoute requiredPermission={ADMIN_PERMISSIONS.USERS_VIEW}>
      <AdminLayout 
        title="User Management" 
        subtitle="Manage admin users and their permissions"
      >
        <UserList onEdit={handleEdit} onDelete={handleDelete} />
      </AdminLayout>
    </AdminProtectedRoute>
  );
}

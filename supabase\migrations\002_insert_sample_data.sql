-- Insert sample data for testing the Collaborate page
-- This migration adds sample projects and job postings

-- Insert sample projects
INSERT INTO projects (
  title,
  description,
  technologies,
  duration,
  difficulty_level,
  required_skills,
  is_active,
  application_deadline,
  max_collaborators
) VALUES 
(
  'AI Chatbot for Local Language Support',
  'Build an intelligent chatbot that supports local languages for better user accessibility and engagement. This project involves natural language processing, machine learning, and modern web technologies to create a conversational AI that can understand and respond in Kinyarwanda, French, and English.',
  ARRAY['React', 'Python', 'NLP', 'TensorFlow', 'Node.js'],
  '3-4 months',
  'Advanced',
  ARRAY['React', 'Python', 'API Design', 'Machine Learning', 'Natural Language Processing'],
  true,
  '2024-12-31',
  4
),
(
  'E-commerce Mobile App',
  'Develop a cross-platform mobile application for local businesses to sell products online. The app will feature product catalogs, shopping cart functionality, payment integration, and order management. Focus on creating an intuitive user experience for both buyers and sellers.',
  ARRAY['React Native', 'Node.js', 'MongoDB', 'Stripe'],
  '2-3 months',
  'Intermediate',
  ARRAY['React Native', 'Node.js', 'Database Design', 'Payment Integration'],
  true,
  '2024-11-30',
  3
),
(
  'School Management System',
  'Create a comprehensive web-based school management system for local educational institutions. Features include student enrollment, grade management, attendance tracking, parent communication, and administrative tools. The system should be user-friendly and accessible on various devices.',
  ARRAY['Next.js', 'PostgreSQL', 'Prisma', 'TypeScript'],
  '4-5 months',
  'Intermediate',
  ARRAY['Next.js', 'PostgreSQL', 'Database Design', 'TypeScript', 'Authentication'],
  true,
  '2025-01-15',
  5
),
(
  'Healthcare Appointment Booking',
  'Develop a digital platform that allows patients to book appointments with healthcare providers, manage medical records, and receive appointment reminders. The system should prioritize data security and HIPAA compliance while providing an excellent user experience.',
  ARRAY['Vue.js', 'Laravel', 'MySQL', 'PHP'],
  '3-4 months',
  'Advanced',
  ARRAY['Vue.js', 'Laravel', 'Security', 'Database Design', 'API Development'],
  true,
  '2024-12-15',
  4
),
(
  'Community Event Platform',
  'Build a platform where community members can discover, create, and manage local events. Features include event creation, RSVP management, location mapping, social sharing, and community engagement tools. The platform should foster local community connections.',
  ARRAY['React', 'Express.js', 'MongoDB', 'Socket.io'],
  '2-3 months',
  'Beginner',
  ARRAY['React', 'Express.js', 'MongoDB', 'Real-time Communication'],
  true,
  '2024-11-20',
  3
),
(
  'Agricultural Data Analytics Dashboard',
  'Create a data visualization dashboard for farmers and agricultural organizations to track crop yields, weather patterns, market prices, and farming insights. The dashboard should help farmers make data-driven decisions to improve productivity.',
  ARRAY['React', 'D3.js', 'Python', 'FastAPI', 'PostgreSQL'],
  '4-6 months',
  'Advanced',
  ARRAY['React', 'Data Visualization', 'Python', 'API Development', 'Analytics'],
  true,
  '2025-02-28',
  5
);

-- Insert sample job postings
INSERT INTO job_postings (
  title,
  description,
  employment_type,
  experience_level,
  location_type,
  compensation_range,
  required_skills,
  is_active,
  application_deadline
) VALUES 
(
  'Junior Frontend Developer',
  'Join our frontend team working on innovative web applications using modern React and TypeScript. You''ll collaborate with designers and backend developers to create beautiful, responsive user interfaces. This is a great opportunity for a junior developer to grow their skills in a supportive environment.',
  'Full-time',
  'Junior',
  'Remote',
  '$800-1200/month',
  ARRAY['React', 'TypeScript', 'CSS', 'HTML', 'Git'],
  true,
  '2024-12-01'
),
(
  'Senior Backend Developer',
  'Lead backend development for scalable applications using Node.js and cloud technologies. You''ll architect robust APIs, optimize database performance, and mentor junior developers. We''re looking for someone with strong technical skills and leadership experience.',
  'Full-time',
  'Senior',
  'Hybrid',
  '$2000-3000/month',
  ARRAY['Node.js', 'AWS', 'PostgreSQL', 'Docker', 'Microservices'],
  true,
  '2024-11-25'
),
(
  'UI/UX Designer',
  'Create exceptional user experiences for our web and mobile applications. You''ll conduct user research, design wireframes and prototypes, and collaborate closely with development teams. We value creativity, user-centered design thinking, and attention to detail.',
  'Full-time',
  'Mid',
  'Remote',
  '$1200-1800/month',
  ARRAY['Figma', 'Adobe Creative Suite', 'User Research', 'Prototyping', 'Design Systems'],
  true,
  '2024-12-10'
),
(
  'DevOps Engineer',
  'Manage and optimize our cloud infrastructure, CI/CD pipelines, and deployment processes. You''ll work with development teams to ensure smooth, reliable deployments and maintain high availability systems. Experience with AWS and containerization is essential.',
  'Full-time',
  'Mid',
  'Remote',
  '$1500-2200/month',
  ARRAY['AWS', 'Docker', 'Kubernetes', 'CI/CD', 'Linux', 'Terraform'],
  true,
  '2024-11-30'
),
(
  'Mobile App Developer',
  'Develop cross-platform mobile applications using React Native. You''ll work on consumer-facing apps with focus on performance, user experience, and platform-specific features. Experience with both iOS and Android development is preferred.',
  'Full-time',
  'Mid',
  'Hybrid',
  '$1400-2000/month',
  ARRAY['React Native', 'JavaScript', 'iOS', 'Android', 'Mobile UI/UX'],
  true,
  '2024-12-05'
),
(
  'Data Analyst',
  'Analyze business data to provide insights that drive decision-making. You''ll create reports, dashboards, and data visualizations while working closely with various teams to understand their data needs. Strong analytical skills and attention to detail are crucial.',
  'Part-time',
  'Junior',
  'Remote',
  '$600-900/month',
  ARRAY['SQL', 'Python', 'Excel', 'Data Visualization', 'Statistics'],
  true,
  '2024-12-15'
),
(
  'Technical Writer',
  'Create clear, comprehensive documentation for our software products and APIs. You''ll work with engineering teams to understand complex technical concepts and translate them into user-friendly documentation. Strong writing skills and technical aptitude required.',
  'Contract',
  'Mid',
  'Remote',
  '$1000-1500/month',
  ARRAY['Technical Writing', 'API Documentation', 'Markdown', 'Git', 'Software Documentation'],
  true,
  '2024-11-28'
),
(
  'Quality Assurance Engineer',
  'Ensure the quality of our software products through manual and automated testing. You''ll design test cases, identify bugs, and work with development teams to resolve issues. Experience with test automation frameworks is a plus.',
  'Full-time',
  'Junior',
  'Hybrid',
  '$900-1300/month',
  ARRAY['Manual Testing', 'Test Automation', 'Selenium', 'Bug Tracking', 'Quality Assurance'],
  true,
  '2024-12-08'
),
(
  'Product Manager',
  'Drive product strategy and roadmap for our software solutions. You''ll work with stakeholders to define requirements, prioritize features, and coordinate with development teams. Strong communication skills and product management experience required.',
  'Full-time',
  'Senior',
  'Hybrid',
  '$2200-3200/month',
  ARRAY['Product Management', 'Agile', 'Stakeholder Management', 'Market Research', 'Strategy'],
  true,
  '2024-11-22'
),
(
  'Cybersecurity Specialist',
  'Protect our systems and data from security threats. You''ll conduct security assessments, implement security measures, and respond to security incidents. Knowledge of security frameworks and compliance requirements is essential.',
  'Full-time',
  'Senior',
  'On-site',
  '$2500-3500/month',
  ARRAY['Cybersecurity', 'Penetration Testing', 'Security Frameworks', 'Incident Response', 'Compliance'],
  true,
  '2024-12-20'
);

-- Add some comments for documentation
COMMENT ON TABLE projects IS 'Stores information about available collaboration projects';
COMMENT ON TABLE job_postings IS 'Stores information about available job positions';
COMMENT ON TABLE project_applications IS 'Stores applications submitted for collaboration projects';
COMMENT ON TABLE job_applications IS 'Stores applications submitted for job positions';

COMMENT ON COLUMN projects.technologies IS 'Array of technology names used in the project';
COMMENT ON COLUMN projects.required_skills IS 'Array of skills required for the project';
COMMENT ON COLUMN projects.difficulty_level IS 'Project difficulty: Beginner, Intermediate, or Advanced';

COMMENT ON COLUMN job_postings.employment_type IS 'Type of employment: Full-time, Part-time, or Contract';
COMMENT ON COLUMN job_postings.experience_level IS 'Required experience level: Junior, Mid, or Senior';
COMMENT ON COLUMN job_postings.location_type IS 'Work location type: Remote, On-site, or Hybrid';

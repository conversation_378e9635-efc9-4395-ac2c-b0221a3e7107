import Header from '@/components/Header';
import Footer from '@/components/Footer';
import Link from 'next/link';
import { ArrowRight, Code, Shield, Users, Zap } from 'lucide-react';

export default function AboutUs() {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
          <div className="max-w-7xl mx-auto text-center">
            {/* Badge */}
            <div className="inline-flex items-center bg-black text-white px-4 py-2 rounded-full text-sm mb-8 hover:scale-105 transition-all duration-300 cursor-pointer animate-fade-in font-jost">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
              Trusted by 200+ Businesses Worldwide
            </div>

            <h1 className="text-4xl sm:text-5xl font-bold text-black mb-6 leading-tight animate-slide-up font-jost">
              About iREME Soft Hub
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed font-jost">
              We are a software development company specializing in custom web applications and mobile solutions.
              Since 2020, we've helped businesses streamline operations and enhance user experiences through innovative technology.
            </p>
          </div>
        </section>

        {/* What We Do Section */}
        <section className="bg-white py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl font-bold text-black mb-6 font-jost">What We Do</h2>
                <p className="text-gray-600 mb-6 font-jost">
                  Our team excels in modern web technologies including React, Node.js, and cloud platforms.
                  We develop scalable applications for startups and enterprises across healthcare, finance, and e-commerce sectors.
                </p>
                <p className="text-gray-600 mb-6 font-jost">
                  We deliver production-ready code through rigorous testing and code reviews. Our agile approach
                  ensures transparent communication and on-time delivery of robust, maintainable solutions.
                </p>
                <Link
                  href="/services"
                  className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium transition-all duration-300 relative after:content-[''] after:absolute after:w-0 after:h-[2px] after:bottom-0 after:left-0 after:bg-blue-800 after:transition-all after:duration-300 hover:after:w-full font-jost"
                >
                  Explore Our Services
                  <ArrowRight size={16} className="ml-2" />
                </Link>
              </div>
              <div className="bg-gray-100 rounded-2xl p-8">
                <h3 className="text-xl font-semibold text-black mb-6 font-jost">Our Expertise</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center text-gray-600 font-jost">
                    <Code size={16} className="mr-3 text-black" />
                    <span className="text-sm">Full-Stack Development</span>
                  </div>
                  <div className="flex items-center text-gray-600 font-jost">
                    <Shield size={16} className="mr-3 text-black" />
                    <span className="text-sm">Security & Testing</span>
                  </div>
                  <div className="flex items-center text-gray-600 font-jost">
                    <Users size={16} className="mr-3 text-black" />
                    <span className="text-sm">Team Augmentation</span>
                  </div>
                  <div className="flex items-center text-gray-600 font-jost">
                    <Zap size={16} className="mr-3 text-black" />
                    <span className="text-sm">Performance Optimization</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="bg-gray-50 py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-black mb-4 font-jost">Why Choose iREME Soft Hub</h2>
              <p className="text-gray-600 max-w-2xl mx-auto font-jost">
                We combine technical excellence with business understanding to deliver solutions that drive real results.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center bg-white rounded-2xl p-6 hover:shadow-lg transition-all duration-300">
                <div className="w-12 h-12 bg-black rounded-full flex items-center justify-center mx-auto mb-4">
                  <Code size={24} className="text-white" />
                </div>
                <h3 className="text-xl font-semibold text-black mb-3 font-jost">Technical Excellence</h3>
                <p className="text-gray-600 font-jost text-sm">
                  Clean, maintainable code following industry best practices and modern development methodologies.
                </p>
              </div>
              <div className="text-center bg-white rounded-2xl p-6 hover:shadow-lg transition-all duration-300">
                <div className="w-12 h-12 bg-black rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield size={24} className="text-white" />
                </div>
                <h3 className="text-xl font-semibold text-black mb-3 font-jost">Proven Security</h3>
                <p className="text-gray-600 font-jost text-sm">
                  Enterprise-grade security protocols and comprehensive testing to protect your applications and data.
                </p>
              </div>
              <div className="text-center bg-white rounded-2xl p-6 hover:shadow-lg transition-all duration-300">
                <div className="w-12 h-12 bg-black rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users size={24} className="text-white" />
                </div>
                <h3 className="text-xl font-semibold text-black mb-3 font-jost">Dedicated Partnership</h3>
                <p className="text-gray-600 font-jost text-sm">
                  Transparent communication, agile processes, and ongoing support throughout your project lifecycle.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Team & Culture Section */}
        {/* <section className="bg-white py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-black mb-6 font-jost">Our Team & Culture</h2>
            <p className="text-gray-600 max-w-3xl mx-auto mb-8 font-jost">
              Our diverse team of experienced developers, designers, and project managers work collaboratively
              to deliver exceptional results. We foster a culture of continuous learning, innovation, and
              professional excellence that directly benefits our clients.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              <div className="bg-gray-100 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-black mb-3 font-jost">Our Approach</h3>
                <ul className="space-y-2 text-left">
                  <li className="flex items-center text-gray-600 font-jost text-sm">
                    <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                    Agile development methodology
                  </li>
                  <li className="flex items-center text-gray-600 font-jost text-sm">
                    <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                    Continuous integration & deployment
                  </li>
                  <li className="flex items-center text-gray-600 font-jost text-sm">
                    <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                    Regular client communication
                  </li>
                </ul>
              </div>
              <div className="bg-gray-100 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-black mb-3 font-jost">Our Standards</h3>
                <ul className="space-y-2 text-left">
                  <li className="flex items-center text-gray-600 font-jost text-sm">
                    <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                    Code reviews & quality assurance
                  </li>
                  <li className="flex items-center text-gray-600 font-jost text-sm">
                    <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                    Performance optimization focus
                  </li>
                  <li className="flex items-center text-gray-600 font-jost text-sm">
                    <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                    Scalable architecture design
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section> */}

        {/* CTA Section */}
        <section className="bg-gradient-to-r from-black to-gray-800 py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-white mb-6 font-jost">Ready to Build Something Amazing?</h2>
            <p className="text-gray-300 mb-8 max-w-2xl mx-auto font-jost">
              Let's discuss your project and explore how our expertise can help bring your vision to life.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Link
                href="/get-quote"
                className="bg-white text-black px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 flex items-center gap-2 text-base font-medium group hover:shadow-lg transform hover:-translate-y-1 font-jost"
              >
                <span className="transition-transform duration-300 group-hover:translate-x-[-4px]">
                  Get Your Free Quote
                </span>
                <ArrowRight
                  size={20}
                  className="transition-all duration-300 group-hover:translate-x-[4px] group-hover:scale-110"
                />
              </Link>
              <Link
                href="/contact-us"
                className="border-2 border-gray-300 text-white px-8 py-4 rounded-full hover:border-white hover:bg-white hover:text-black transition-all duration-300 text-base font-medium hover:shadow-lg transform hover:-translate-y-1 font-jost"
              >
                Contact Us
              </Link>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}

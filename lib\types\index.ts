// Database types - extend these based on your Supabase schema
export interface User {
  id: string;
  email: string;
  created_at: string;
  updated_at: string;
}

// Common component props
export interface BaseProps {
  className?: string;
  children?: React.ReactNode;
}

// API response types
export interface ApiResponse<T = unknown> {
  data?: T;
  error?: string;
  message?: string;
}

// Form types
export interface FormState {
  isLoading: boolean;
  error?: string;
  success?: boolean;
}

// Navigation types
export interface NavItem {
  title: string;
  href: string;
  disabled?: boolean;
  external?: boolean;
  icon?: React.ComponentType<{ className?: string }>;
}

export interface NavConfig {
  mainNav: NavItem[];
  sidebarNav?: NavItem[];
}

// Template showcase types
export interface Template {
  id: string;
  slug: string;
  name: string;
  shortDescription: string;
  fullDescription: string;
  heroImage: string;
  images: string[];
  demoUrl: string;
  category: TemplateCategory;
  technologies: string[];
  features: string[];
  createdAt: string;
  updatedAt: string;
}

export type TemplateCategory =
  | 'E-commerce'
  | 'Portfolio'
  | 'Business'
  | 'Blog'
  | 'Landing Page'
  | 'Dashboard'
  | 'SaaS'
  | 'Mobile App'
  | 'Web App';

export interface TemplateCardProps {
  template: Template;
  className?: string;
}

export interface TemplateGalleryProps {
  images: string[];
  templateName: string;
  className?: string;
}

export interface DemoButtonProps {
  demoUrl: string;
  templateName: string;
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

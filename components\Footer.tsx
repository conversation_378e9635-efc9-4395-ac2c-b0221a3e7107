'use client';

import React, { useState } from 'react';

import <PERSON> from 'next/link';

const Footer = () => {
  const [email, setEmail] = useState<string>('');

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // TODO: Implement email submission logic
    setEmail('');
  };

  return (
    <footer className="bg-black text-white py-12 px-6 md:px-12">
      <div className="max-w-7xl mx-auto">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-12 text-left">
          {/* Product Column */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium mb-4">Product</h3>
            <ul className="space-y-2 text-gray-300">
              <li><Link href="#" className="hover:text-white transition-colors">API Documentation</Link></li>
              <li><Link href="#" className="hover:text-white transition-colors">SDKs & Tools</Link></li>
              <li><Link href="#" className="hover:text-white transition-colors">CLI Tooling</Link></li>
              <li><Link href="#" className="hover:text-white transition-colors">iREME Soft Hub Features</Link></li>
              <li><Link href="#" className="hover:text-white transition-colors">Changelog</Link></li>
            </ul>
          </div>

          {/* For Developers Column */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium mb-4">For Developers</h3>
            <ul className="space-y-2 text-gray-300">
              <li><Link href="#" className="hover:text-white transition-colors">Code Editor</Link></li>
              <li><Link href="#" className="hover:text-white transition-colors">Project Templates</Link></li>
              <li><Link href="#" className="hover:text-white transition-colors">iREME Soft Hub CLI</Link></li>
              <li><Link href="#" className="hover:text-white transition-colors">GitHub Integration</Link></li>
              <li><Link href="#" className="hover:text-white transition-colors">Terminal Access</Link></li>
              <li><Link href="#" className="hover:text-white transition-colors">Issue Tracker</Link></li>
              <li><Link href="#" className="hover:text-white transition-colors">Deployments</Link></li>
            </ul>
          </div>

          {/* For Suppliers Column */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium mb-4">For Suppliers</h3>
            <ul className="space-y-2 text-gray-300">
              <li><Link href="#" className="hover:text-white transition-colors">Partner Program</Link></li>
              <li><Link href="#" className="hover:text-white transition-colors">Extension Marketplace</Link></li>
              <li><Link href="#" className="hover:text-white transition-colors">Developer Console</Link></li>
              <li><Link href="#" className="hover:text-white transition-colors">Submit Plugin</Link></li>
              <li><Link href="#" className="hover:text-white transition-colors">API Monetization</Link></li>
              <li><Link href="#" className="hover:text-white transition-colors">Usage Reports</Link></li>
            </ul>
          </div>

          {/* Resources Column */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium mb-4">Resources</h3>
            <ul className="space-y-2 text-gray-300">
              <li><Link href="#" className="hover:text-white transition-colors">iREME Soft Hub Blog</Link></li>
              <li><Link href="#" className="hover:text-white transition-colors">Tutorials</Link></li>
              <li><Link href="#" className="hover:text-white transition-colors">Community Forum</Link></li>
              <li><Link href="#" className="hover:text-white transition-colors">Status Page</Link></li>
              <li><Link href="#" className="hover:text-white transition-colors">Pricing</Link></li>
            </ul>
          </div>

          {/* About Column */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium mb-4">About</h3>
            <ul className="space-y-2 text-gray-300">
              <li><Link href="#" className="hover:text-white transition-colors">About DevHub</Link></li>
              <li><Link href="#" className="hover:text-white transition-colors">Careers</Link></li>
              <li><Link href="#" className="hover:text-white transition-colors">Contact Us</Link></li>
              <li><Link href="#" className="hover:text-white transition-colors">Press</Link></li>
            </ul>
          </div>
        </div>

        {/* Newsletter Section */}
        <div className="border-t border-gray-800 pt-8 mb-8">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
            {/* Left Side - Logo and Text */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                  <div className="w-3 h-3 bg-black rounded-full"></div>
                  <div className="w-2 h-2 bg-black rounded-full ml-1"></div>
                </div>
                <span className="text-lg font-medium">iREME Soft Hub</span>
              </div>
              <p className="text-gray-300 text-sm">Building the Future Together.</p>
            </div>

            {/* Right Side - Newsletter Signup */}
            <div className="space-y-4">
              <p className="text-sm text-gray-300">Subscribe to our newsletter</p>
              <form onSubmit={handleSubmit} className="flex gap-2">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  className="px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-white transition-colors"
                />
                <button
                  type="submit"
                  className="px-6 py-2 bg-white text-black rounded-lg font-medium hover:bg-gray-200 transition-colors"
                >
                  Subscribe
                </button>
              </form>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 pt-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 text-sm text-gray-400">
            {/* Left Side - Copyright and Links */}
            <div className="flex flex-col md:flex-row gap-4 md:gap-6">
              <span>© Programs {new Date().getFullYear()}</span>
              <div className="flex gap-4">
                <Link href="#" className="hover:text-white transition-colors">Instagram</Link>
                <Link href="#" className="hover:text-white transition-colors">Facebook</Link>
                <Link href="#" className="hover:text-white transition-colors">LinkedIn</Link>
                <Link href="#" className="hover:text-white transition-colors">GitHub</Link>
              </div>
              <Link href="#" className="hover:text-white transition-colors">What is Programs?</Link>
            </div>

            {/* Right Side - Legal Links */}
            <div className="flex flex-col md:flex-row gap-2 md:gap-4">
              <Link href="#" className="hover:text-white transition-colors">Terms & Conditions</Link>
              <Link href="#" className="hover:text-white transition-colors">Privacy Policy</Link>
              <Link href="#" className="hover:text-white transition-colors">Accessibility</Link>
              <Link href="#" className="hover:text-white transition-colors">Dedicated Web Clipper</Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

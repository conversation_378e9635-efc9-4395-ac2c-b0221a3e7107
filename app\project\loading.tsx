import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function ProjectLoading() {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1 py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Hero Section Skeleton */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center bg-gray-200 animate-pulse px-4 py-2 rounded-full text-sm mb-8 w-64 h-8"></div>
            
            <div className="space-y-4 mb-6">
              <div className="h-12 bg-gray-200 animate-pulse rounded-lg mx-auto max-w-2xl"></div>
              <div className="h-12 bg-gray-200 animate-pulse rounded-lg mx-auto max-w-xl"></div>
            </div>
            
            <div className="space-y-3 mb-12 max-w-3xl mx-auto">
              <div className="h-6 bg-gray-200 animate-pulse rounded-lg"></div>
              <div className="h-6 bg-gray-200 animate-pulse rounded-lg max-w-2xl mx-auto"></div>
            </div>

            {/* Category Pills Skeleton */}
            <div className="flex flex-wrap justify-center gap-3 mb-12">
              {Array.from({ length: 6 }).map((_, index) => (
                <div
                  key={index}
                  className="h-8 bg-gray-200 animate-pulse rounded-full w-20"
                ></div>
              ))}
            </div>
          </div>

          {/* Templates Grid Skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {Array.from({ length: 6 }).map((_, index) => (
              <div
                key={index}
                className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"
              >
                {/* Image Skeleton */}
                <div className="h-64 bg-gray-200 animate-pulse"></div>
                
                {/* Content Skeleton */}
                <div className="p-6 space-y-4">
                  {/* Title */}
                  <div className="h-6 bg-gray-200 animate-pulse rounded-lg"></div>
                  
                  {/* Description */}
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 animate-pulse rounded-lg"></div>
                    <div className="h-4 bg-gray-200 animate-pulse rounded-lg max-w-3/4"></div>
                    <div className="h-4 bg-gray-200 animate-pulse rounded-lg max-w-1/2"></div>
                  </div>
                  
                  {/* Technologies */}
                  <div className="flex flex-wrap gap-2">
                    {Array.from({ length: 3 }).map((_, techIndex) => (
                      <div
                        key={techIndex}
                        className="h-6 bg-gray-200 animate-pulse rounded-md w-16"
                      ></div>
                    ))}
                  </div>
                  
                  {/* Buttons */}
                  <div className="flex gap-3 pt-2">
                    <div className="flex-1 h-12 bg-gray-200 animate-pulse rounded-lg"></div>
                    <div className="h-12 w-24 bg-gray-200 animate-pulse rounded-lg"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* CTA Section Skeleton */}
          <div className="bg-gray-50 rounded-3xl p-12">
            <div className="text-center space-y-6">
              <div className="h-8 bg-gray-200 animate-pulse rounded-lg max-w-md mx-auto"></div>
              <div className="space-y-2 max-w-2xl mx-auto">
                <div className="h-5 bg-gray-200 animate-pulse rounded-lg"></div>
                <div className="h-5 bg-gray-200 animate-pulse rounded-lg max-w-3/4 mx-auto"></div>
              </div>
              <div className="h-12 bg-gray-200 animate-pulse rounded-full w-48 mx-auto"></div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}

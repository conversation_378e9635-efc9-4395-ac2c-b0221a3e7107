'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AdminCard } from '@/components/admin/ui/AdminCard';
import AdminButton from '@/components/admin/ui/AdminButton';
import { adminContent } from '@/lib/supabase/admin';
import { AdminTemplate } from '@/lib/types/admin';
import {
  Save,
  Eye,
  Upload,
  X,
  Plus,
  ExternalLink,
  AlertCircle,
} from 'lucide-react';

interface TemplateFormProps {
  template?: AdminTemplate;
  onSave?: (template: AdminTemplate) => void;
  onCancel?: () => void;
}

interface FormData {
  name: string;
  slug: string;
  short_description: string;
  full_description: string;
  category: string;
  technologies: string[];
  features: string[];
  hero_image_url: string;
  gallery_images: string[];
  demo_url: string;
  source_code_url: string;
  base_price: number | null;
  custom_price: number | null;
  license_type: string;
  meta_title: string;
  meta_description: string;
  keywords: string[];
  status: 'draft' | 'published' | 'archived';
  is_featured: boolean;
}

interface FormErrors {
  [key: string]: string;
}

export default function TemplateForm({ template, onSave, onCancel }: TemplateFormProps) {
  const router = useRouter();
  const [loading, setSaving] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [newTechnology, setNewTechnology] = useState('');
  const [newFeature, setNewFeature] = useState('');
  const [newKeyword, setNewKeyword] = useState('');
  const [newGalleryImage, setNewGalleryImage] = useState('');

  const [formData, setFormData] = useState<FormData>({
    name: '',
    slug: '',
    short_description: '',
    full_description: '',
    category: '',
    technologies: [],
    features: [],
    hero_image_url: '',
    gallery_images: [],
    demo_url: '',
    source_code_url: '',
    base_price: null,
    custom_price: null,
    license_type: 'standard',
    meta_title: '',
    meta_description: '',
    keywords: [],
    status: 'draft',
    is_featured: false,
  });

  // Initialize form with template data if editing
  useEffect(() => {
    if (template) {
      setFormData({
        name: template.name,
        slug: template.slug,
        short_description: template.short_description || '',
        full_description: template.full_description || '',
        category: template.category,
        technologies: template.technologies,
        features: template.features,
        hero_image_url: template.hero_image_url || '',
        gallery_images: template.gallery_images,
        demo_url: template.demo_url || '',
        source_code_url: template.source_code_url || '',
        base_price: template.base_price ?? null,
        custom_price: template.custom_price ?? null,
        license_type: template.license_type,
        meta_title: template.meta_title || '',
        meta_description: template.meta_description || '',
        keywords: template.keywords,
        status: template.status,
        is_featured: template.is_featured,
      });
    }
  }, [template]);

  // Generate slug from name
  useEffect(() => {
    if (formData.name && !template) {
      const slug = formData.name
        .toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, '-')
        .trim();
      setFormData(prev => ({ ...prev, slug }));
    }
  }, [formData.name, template]);

  const categories = [
    'Web Development',
    'Mobile Apps',
    'E-commerce',
    'Landing Pages',
    'Dashboards',
    'SaaS',
    'Portfolio',
    'Blog',
    'Corporate',
    'Other',
  ];

  const licenseTypes = [
    { value: 'standard', label: 'Standard License' },
    { value: 'extended', label: 'Extended License' },
    { value: 'exclusive', label: 'Exclusive License' },
  ];

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) newErrors.name = 'Name is required';
    if (!formData.slug.trim()) newErrors.slug = 'Slug is required';
    if (!formData.category) newErrors.category = 'Category is required';
    if (!formData.short_description.trim()) newErrors.short_description = 'Short description is required';

    if (formData.demo_url && !isValidUrl(formData.demo_url)) {
      newErrors.demo_url = 'Please enter a valid URL';
    }

    if (formData.source_code_url && !isValidUrl(formData.source_code_url)) {
      newErrors.source_code_url = 'Please enter a valid URL';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const isValidUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const addArrayItem = (field: 'technologies' | 'features' | 'keywords' | 'gallery_images', value: string) => {
    if (value.trim() && !formData[field].includes(value.trim())) {
      setFormData(prev => ({
        ...prev,
        [field]: [...prev[field], value.trim()],
      }));
    }
  };

  const removeArrayItem = (field: 'technologies' | 'features' | 'keywords' | 'gallery_images', index: number) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      setSaving(true);
      
      let savedTemplate: AdminTemplate;
      
      if (template) {
        // Update existing template
        savedTemplate = await adminContent.updateTemplate(template.id, formData);
      } else {
        // Create new template
        savedTemplate = await adminContent.createTemplate(formData);
      }

      if (onSave) {
        onSave(savedTemplate);
      } else {
        router.push('/admin/templates');
      }
    } catch (error) {
      console.error('Error saving template:', error);
      setErrors({ general: 'Failed to save template. Please try again.' });
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      router.push('/admin/templates');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* General Error */}
      {errors.general && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start space-x-3">
          <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
          <p className="text-sm text-red-700 font-jost">{errors.general}</p>
        </div>
      )}

      {/* Basic Information */}
      <AdminCard>
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 font-jost">Basic Information</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                Template Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost ${
                  errors.name ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Enter template name"
              />
              {errors.name && <p className="mt-1 text-sm text-red-600 font-jost">{errors.name}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                Slug *
              </label>
              <input
                type="text"
                value={formData.slug}
                onChange={(e) => handleInputChange('slug', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost ${
                  errors.slug ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="template-slug"
              />
              {errors.slug && <p className="mt-1 text-sm text-red-600 font-jost">{errors.slug}</p>}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
              Category *
            </label>
            <select
              value={formData.category}
              onChange={(e) => handleInputChange('category', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost ${
                errors.category ? 'border-red-300' : 'border-gray-300'
              }`}
            >
              <option value="">Select a category</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
            {errors.category && <p className="mt-1 text-sm text-red-600 font-jost">{errors.category}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
              Short Description *
            </label>
            <textarea
              value={formData.short_description}
              onChange={(e) => handleInputChange('short_description', e.target.value)}
              rows={3}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost ${
                errors.short_description ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Brief description of the template"
            />
            {errors.short_description && <p className="mt-1 text-sm text-red-600 font-jost">{errors.short_description}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
              Full Description
            </label>
            <textarea
              value={formData.full_description}
              onChange={(e) => handleInputChange('full_description', e.target.value)}
              rows={6}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
              placeholder="Detailed description of the template"
            />
          </div>
        </div>
      </AdminCard>

      {/* Technologies */}
      <AdminCard>
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 font-jost">Technologies</h3>
          
          <div className="flex space-x-2">
            <input
              type="text"
              value={newTechnology}
              onChange={(e) => setNewTechnology(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
              placeholder="Add technology (e.g., React, Next.js)"
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  addArrayItem('technologies', newTechnology);
                  setNewTechnology('');
                }
              }}
            />
            <AdminButton
              type="button"
              variant="outline"
              onClick={() => {
                addArrayItem('technologies', newTechnology);
                setNewTechnology('');
              }}
              icon={<Plus className="h-4 w-4" />}
            >
              Add
            </AdminButton>
          </div>

          <div className="flex flex-wrap gap-2">
            {formData.technologies.map((tech, index) => (
              <span
                key={index}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
              >
                {tech}
                <button
                  type="button"
                  onClick={() => removeArrayItem('technologies', index)}
                  className="ml-2 text-blue-600 hover:text-blue-500"
                >
                  <X className="h-3 w-3" />
                </button>
              </span>
            ))}
          </div>
        </div>
      </AdminCard>

      {/* Actions */}
      <div className="flex items-center justify-between pt-6 border-t border-gray-200">
        <div className="flex items-center space-x-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.is_featured}
              onChange={(e) => handleInputChange('is_featured', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span className="ml-2 text-sm text-gray-700 font-jost">Featured Template</span>
          </label>

          <select
            value={formData.status}
            onChange={(e) => handleInputChange('status', e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
          >
            <option value="draft">Draft</option>
            <option value="published">Published</option>
            <option value="archived">Archived</option>
          </select>
        </div>

        <div className="flex items-center space-x-3">
          <AdminButton
            type="button"
            variant="outline"
            onClick={handleCancel}
          >
            Cancel
          </AdminButton>
          
          <AdminButton
            type="submit"
            loading={loading}
            icon={<Save className="h-4 w-4" />}
          >
            {template ? 'Update Template' : 'Create Template'}
          </AdminButton>
        </div>
      </div>
    </form>
  );
}

#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to run the preview description migration
 * This adds preview_description columns to projects and job_postings tables
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  console.error('\nPlease check your .env.local file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runMigration() {
  try {
    console.log('🚀 Starting preview description migration...');
    
    // Read the migration file
    const migrationPath = path.join(__dirname, '../supabase/migrations/004_add_preview_descriptions.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📝 Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`⏳ Executing statement ${i + 1}/${statements.length}...`);
      
      const { error } = await supabase.rpc('exec_sql', { sql: statement });
      
      if (error) {
        // Try direct query if RPC fails
        const { error: directError } = await supabase
          .from('_supabase_migrations')
          .select('*')
          .limit(1);
        
        if (directError) {
          console.error(`❌ Error executing statement ${i + 1}:`, error);
          throw error;
        }
        
        // Execute using raw SQL
        const { error: rawError } = await supabase.rpc('exec', { sql: statement });
        if (rawError) {
          console.error(`❌ Error executing statement ${i + 1}:`, rawError);
          throw rawError;
        }
      }
      
      console.log(`✅ Statement ${i + 1} executed successfully`);
    }
    
    console.log('🎉 Migration completed successfully!');
    console.log('\n📋 Summary of changes:');
    console.log('   ✓ Added preview_description column to projects table');
    console.log('   ✓ Added preview_description column to job_postings table');
    console.log('   ✓ Updated existing records with preview descriptions');
    console.log('   ✓ Added search indexes for better performance');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Alternative method using direct SQL execution
async function runMigrationDirect() {
  try {
    console.log('🚀 Starting preview description migration (direct method)...');
    
    // Add columns
    console.log('⏳ Adding preview_description columns...');
    
    const addColumnsSQL = `
      ALTER TABLE projects ADD COLUMN IF NOT EXISTS preview_description VARCHAR(200);
      ALTER TABLE job_postings ADD COLUMN IF NOT EXISTS preview_description VARCHAR(200);
    `;
    
    // Note: This is a simplified approach. In production, you should use proper migration tools.
    console.log('📝 Please run the following SQL manually in your Supabase SQL editor:');
    console.log('\n' + '='.repeat(80));
    
    const migrationPath = path.join(__dirname, '../supabase/migrations/004_add_preview_descriptions.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    console.log(migrationSQL);
    
    console.log('='.repeat(80) + '\n');
    console.log('🔗 Go to: https://supabase.com/dashboard/project/[your-project]/sql');
    console.log('📋 Copy and paste the SQL above, then click "Run"');
    
  } catch (error) {
    console.error('❌ Error reading migration file:', error);
    process.exit(1);
  }
}

// Run the migration
if (process.argv.includes('--direct')) {
  runMigrationDirect();
} else {
  runMigration();
}

'use client';

import React from 'react';
import AdminProtectedRoute from '@/components/admin/auth/AdminProtectedRoute';
import AdminLayout from '@/components/admin/layout/AdminLayout';
import MediaLibrary from '@/components/admin/media/MediaLibrary';
import { ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';

export default function AdminMediaPage() {
  return (
    <AdminProtectedRoute requiredPermission={ADMIN_PERMISSIONS.MEDIA_VIEW}>
      <AdminLayout 
        title="Media Library" 
        subtitle="Manage your images, documents, and media files"
      >
        <MediaLibrary />
      </AdminLayout>
    </AdminProtectedRoute>
  );
}

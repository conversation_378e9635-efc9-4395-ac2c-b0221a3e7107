'use client';

import React, { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import Link from 'next/link';
import { 
  ArrowRight, 
  Palette, 
  Users, 
  Eye, 
  Zap, 
  Target, 
  BarChart3,
  CheckCircle,
  Clock,
  Star,
  Quote,
  ChevronDown,
  ChevronUp,
  Figma,
  Layers,
  MousePointer
} from 'lucide-react';

interface Feature {
  title: string;
  description: string;
  icon: React.ReactNode;
}

interface Package {
  name: string;
  price: string;
  description: string;
  features: string[];
  timeline: string;
  popular?: boolean;
}

export default function UIUXDesignPage() {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  const features: Feature[] = [
    {
      title: 'User Research & Analysis',
      description: 'Deep dive into your users\' needs, behaviors, and pain points to create data-driven design decisions.',
      icon: <Users className="w-6 h-6" />
    },
    {
      title: 'Conversion-Focused Design',
      description: 'Every design element is optimized to guide users toward your business goals and increase conversions.',
      icon: <Target className="w-6 h-6" />
    },
    {
      title: 'Interactive Prototyping',
      description: 'High-fidelity prototypes that let you experience the user journey before development begins.',
      icon: <MousePointer className="w-6 h-6" />
    },
    {
      title: 'Design Systems',
      description: 'Comprehensive design systems that ensure consistency and scalability across all touchpoints.',
      icon: <Layers className="w-6 h-6" />
    },
    {
      title: 'Usability Testing',
      description: 'Real user testing to validate design decisions and optimize the user experience.',
      icon: <Eye className="w-6 h-6" />
    },
    {
      title: 'Performance Optimization',
      description: 'Designs optimized for fast loading and smooth interactions across all devices.',
      icon: <Zap className="w-6 h-6" />
    }
  ];

  const packages: Package[] = [
    {
      name: 'UI/UX Audit',
      price: '$2,500',
      description: 'Comprehensive analysis of your current design with actionable improvement recommendations.',
      features: [
        'Heuristic evaluation',
        'User journey analysis',
        'Conversion optimization audit',
        'Accessibility assessment',
        'Detailed report with recommendations',
        '30 days support'
      ],
      timeline: '1-2 weeks'
    },
    {
      name: 'Complete UI/UX Design',
      price: '$8,000',
      description: 'Full design solution from research to high-fidelity prototypes ready for development.',
      features: [
        'User research & personas',
        'Information architecture',
        'Wireframing & prototyping',
        'Visual design & branding',
        'Design system creation',
        'Usability testing',
        'Developer handoff',
        '60 days support'
      ],
      timeline: '6-10 weeks',
      popular: true
    },
    {
      name: 'Enterprise Design System',
      price: '$15,000+',
      description: 'Comprehensive design system for large organizations with multiple products and teams.',
      features: [
        'Multi-product design system',
        'Component library',
        'Design tokens & guidelines',
        'Team training & workshops',
        'Ongoing design support',
        'Brand consistency audit',
        'Documentation & governance',
        '90 days support'
      ],
      timeline: '10-16 weeks'
    }
  ];

  const faqs = [
    {
      question: 'What\'s the difference between UI and UX design?',
      answer: 'UI (User Interface) design focuses on the visual elements and interactions, while UX (User Experience) design focuses on the overall user journey and how users feel when using your product. We combine both to create designs that are both beautiful and functional.'
    },
    {
      question: 'Do you work with existing brands or create new ones?',
      answer: 'We work with both! We can enhance your existing brand identity or create a completely new brand from scratch. Our designs always align with your brand values and business objectives.'
    },
    {
      question: 'How do you measure design success?',
      answer: 'We measure success through key metrics like conversion rates, user engagement, task completion rates, and user satisfaction scores. We also conduct A/B testing to validate design decisions.'
    },
    {
      question: 'Can you redesign my existing website or app?',
      answer: 'Absolutely! We specialize in redesigning existing products to improve user experience, increase conversions, and modernize the visual design while maintaining brand consistency.'
    },
    {
      question: 'Do you provide design files for developers?',
      answer: 'Yes, we provide complete developer handoff packages including design files, specifications, assets, and documentation to ensure smooth implementation of the designs.'
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden bg-gradient-to-br from-pink-50 to-white">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="inline-flex items-center bg-pink-600 text-white px-4 py-2 rounded-full text-sm mb-6 font-jost">
                <Palette className="w-4 h-4 mr-2" />
                UI/UX Design Services
              </div>
              
              <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-black mb-6 leading-tight font-jost">
                Design That Converts 
                <span className="text-pink-600"> Visitors Into Customers</span>
              </h1>
              
              <p className="text-xl text-gray-600 mb-8 leading-relaxed font-jost">
                We create user-centered designs that not only look stunning but drive real business results. 
                From research to prototyping, we design experiences that users love and businesses profit from.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <Link
                  href="/get-quote"
                  className="bg-black text-white px-8 py-4 rounded-full hover:bg-gray-800 transition-all duration-300 flex items-center justify-center gap-2 font-medium font-jost group"
                >
                  Get Free Quote
                  <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
                </Link>
                <Link
                  href="/project"
                  className="border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-full hover:border-gray-400 hover:bg-gray-50 transition-all duration-300 font-medium font-jost"
                >
                  View Portfolio
                </Link>
              </div>
              
              <div className="flex items-center gap-8 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  <span className="font-jost">1-16 weeks delivery</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  <span className="font-jost">Design experts team</span>
                </div>
              </div>
            </div>
            
            <div className="relative">
              <div className="bg-white p-8 rounded-2xl shadow-2xl border border-gray-100">
                <div className="space-y-6">
                  {/* Design Tools */}
                  <div className="flex items-center justify-between">
                    <div className="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center">
                      <Figma className="w-6 h-6 text-white" />
                    </div>
                    <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                      <Palette className="w-6 h-6 text-white" />
                    </div>
                    <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                      <Layers className="w-6 h-6 text-white" />
                    </div>
                  </div>
                  
                  {/* Design Preview */}
                  <div className="space-y-4">
                    <div className="bg-gradient-to-r from-pink-100 to-purple-100 h-16 rounded-lg flex items-center justify-center">
                      <div className="text-gray-600 font-jost text-sm">Hero Section</div>
                    </div>
                    <div className="grid grid-cols-3 gap-2">
                      <div className="bg-gray-100 h-12 rounded flex items-center justify-center">
                        <div className="w-6 h-6 bg-pink-300 rounded"></div>
                      </div>
                      <div className="bg-gray-100 h-12 rounded flex items-center justify-center">
                        <div className="w-6 h-6 bg-purple-300 rounded"></div>
                      </div>
                      <div className="bg-gray-100 h-12 rounded flex items-center justify-center">
                        <div className="w-6 h-6 bg-blue-300 rounded"></div>
                      </div>
                    </div>
                    <div className="bg-pink-600 h-10 rounded-lg flex items-center justify-center">
                      <div className="text-white font-jost text-sm">Call to Action</div>
                    </div>
                  </div>
                  
                  {/* Metrics */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <div className="text-green-600 font-semibold">+200%</div>
                      <div className="text-xs text-gray-600">Conversions</div>
                    </div>
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <div className="text-blue-600 font-semibold">4.9/5</div>
                      <div className="text-xs text-gray-600">User Rating</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              Why Choose Our UI/UX Design?
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto font-jost">
              We don't just make things look pretty – we create strategic designs that solve business problems,
              delight users, and drive measurable results for your bottom line.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group"
              >
                <div className="flex items-center justify-center w-12 h-12 bg-pink-600 text-white rounded-xl mb-6 group-hover:scale-110 transition-transform duration-300">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold text-black mb-4 font-jost">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed font-jost">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Packages Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              UI/UX Design Packages
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto font-jost">
              From quick audits to comprehensive design systems, we have the perfect solution
              to elevate your user experience and drive business growth.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {packages.map((pkg, index) => (
              <div
                key={index}
                className={`bg-white p-8 rounded-2xl shadow-lg border-2 transition-all duration-300 hover:shadow-xl hover:-translate-y-1 relative ${
                  pkg.popular ? 'border-pink-600' : 'border-gray-100'
                }`}
              >
                {pkg.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="bg-pink-600 text-white px-4 py-2 rounded-full text-sm font-medium font-jost">
                      Most Popular
                    </div>
                  </div>
                )}

                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-black mb-2 font-jost">
                    {pkg.name}
                  </h3>
                  <div className="text-4xl font-bold text-black mb-2 font-jost">
                    {pkg.price}
                  </div>
                  <p className="text-gray-600 font-jost">
                    {pkg.description}
                  </p>
                </div>

                <ul className="space-y-3 mb-8">
                  {pkg.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center text-gray-600 font-jost">
                      <CheckCircle className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>

                <div className="text-center mb-6">
                  <div className="text-sm text-gray-500 font-jost">Timeline: {pkg.timeline}</div>
                </div>

                <Link
                  href="/get-quote"
                  className={`w-full py-3 px-6 rounded-full transition-all duration-300 flex items-center justify-center gap-2 font-medium font-jost ${
                    pkg.popular
                      ? 'bg-pink-600 text-white hover:bg-pink-700'
                      : 'bg-black text-white hover:bg-gray-800'
                  }`}
                >
                  Get Started
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-600 mb-4 font-jost">
              Need a custom design solution? We create tailored experiences for unique brand requirements.
            </p>
            <Link
              href="/contact-us"
              className="inline-flex items-center text-pink-600 hover:text-pink-800 font-medium font-jost group"
            >
              Discuss Custom Design Project
              <ArrowRight className="w-4 h-4 ml-1 transition-transform duration-300 group-hover:translate-x-1" />
            </Link>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-gray-600 font-jost">
              Get answers to common questions about our UI/UX design services and process.
            </p>
          </div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div
                key={index}
                className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"
              >
                <button
                  onClick={() => setOpenFAQ(openFAQ === index ? null : index)}
                  className="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
                >
                  <h3 className="text-lg font-semibold text-black font-jost pr-4">
                    {faq.question}
                  </h3>
                  {openFAQ === index ? (
                    <ChevronUp className="w-5 h-5 text-gray-500 flex-shrink-0" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-gray-500 flex-shrink-0" />
                  )}
                </button>

                {openFAQ === index && (
                  <div className="px-8 pb-6">
                    <p className="text-gray-600 leading-relaxed font-jost">
                      {faq.answer}
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-pink-600 to-purple-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl sm:text-5xl font-bold text-white mb-6 font-jost">
            Ready to Transform Your User Experience?
          </h2>
          <p className="text-xl text-pink-100 mb-8 leading-relaxed font-jost">
            Let's create designs that not only look amazing but drive real business results.
            Start your design transformation today with a free consultation.
          </p>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-8">
            <Link
              href="/get-quote"
              className="bg-white text-pink-600 px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 flex items-center gap-2 font-medium font-jost group"
            >
              Get Free Design Consultation
              <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
            </Link>
            <Link
              href="/contact-us"
              className="border-2 border-white text-white px-8 py-4 rounded-full hover:bg-white hover:text-pink-600 transition-all duration-300 font-medium font-jost"
            >
              Schedule a Call
            </Link>
          </div>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-pink-100">
            <div className="flex items-center gap-2">
              <Clock className="w-5 h-5" />
              <span className="font-jost">Free consultation included</span>
            </div>
            <div className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              <span className="font-jost">Results-driven design</span>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

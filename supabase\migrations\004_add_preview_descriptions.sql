-- Add preview description fields to projects and job_postings tables
-- This migration adds short preview descriptions for better card display

-- Add preview_description column to projects table
ALTER TABLE projects 
ADD COLUMN preview_description VARCHAR(200);

-- Add preview_description column to job_postings table
ALTER TABLE job_postings 
ADD COLUMN preview_description VARCHAR(200);

-- Update existing projects with preview descriptions
UPDATE projects 
SET preview_description = CASE 
  WHEN title = 'AI Chatbot for Local Language Support' THEN 'Build an intelligent chatbot supporting Kinyarwanda, French, and English with modern NLP technologies.'
  WHEN title = 'E-commerce Platform for Local Businesses' THEN 'Create a comprehensive e-commerce solution tailored for Rwandan businesses with mobile-first design.'
  WHEN title = 'Mobile Health Tracking App' THEN 'Develop a React Native app for health monitoring with offline capabilities and data synchronization.'
  WHEN title = 'Educational Content Management System' THEN 'Build a modern CMS for educational institutions with multi-language support and interactive features.'
  WHEN title = 'Real-time Collaboration Tool' THEN 'Create a Slack-like collaboration platform with real-time messaging and file sharing capabilities.'
  WHEN title = 'IoT Dashboard for Smart Agriculture' THEN 'Develop a comprehensive dashboard for monitoring agricultural sensors and automating farm operations.'
  WHEN title = 'Blockchain Voting System' THEN 'Build a secure, transparent voting platform using blockchain technology for enhanced election integrity.'
  WHEN title = 'AI-Powered Content Recommendation Engine' THEN 'Create an intelligent recommendation system using machine learning for personalized content delivery.'
  WHEN title = 'Virtual Reality Training Platform' THEN 'Develop an immersive VR training solution for professional skill development and education.'
  WHEN title = 'Microservices API Gateway' THEN 'Build a scalable API gateway for microservices architecture with advanced routing and security features.'
  ELSE LEFT(description, 150) || '...'
END;

-- Update existing job postings with preview descriptions
UPDATE job_postings 
SET preview_description = CASE 
  WHEN title = 'Junior Frontend Developer' THEN 'Join our frontend team building innovative web applications with React and TypeScript in a supportive environment.'
  WHEN title = 'Senior Backend Developer' THEN 'Lead backend development using Node.js and Python, architect scalable solutions, and mentor junior developers.'
  WHEN title = 'Full Stack Developer' THEN 'Work across the entire technology stack building end-to-end solutions with modern frameworks and tools.'
  WHEN title = 'Mobile App Developer' THEN 'Create cross-platform mobile applications using React Native with focus on performance and user experience.'
  WHEN title = 'DevOps Engineer' THEN 'Manage cloud infrastructure, implement CI/CD pipelines, and ensure system reliability and scalability.'
  WHEN title = 'UI/UX Designer' THEN 'Design intuitive user interfaces and experiences, collaborate with developers, and conduct user research.'
  WHEN title = 'Data Scientist' THEN 'Analyze complex datasets, build predictive models, and derive actionable insights for business decisions.'
  WHEN title = 'Product Manager' THEN 'Drive product strategy, coordinate cross-functional teams, and deliver innovative solutions that delight users.'
  WHEN title = 'Quality Assurance Engineer' THEN 'Ensure software quality through comprehensive testing, automation, and continuous improvement processes.'
  WHEN title = 'Technical Writer' THEN 'Create clear technical documentation, API guides, and user manuals for developers and end users.'
  ELSE LEFT(description, 150) || '...'
END;

-- Add comments for documentation
COMMENT ON COLUMN projects.preview_description IS 'Short description (max 200 chars) for card previews';
COMMENT ON COLUMN job_postings.preview_description IS 'Short description (max 200 chars) for card previews';

-- Create indexes for better search performance on preview descriptions
CREATE INDEX idx_projects_preview_description ON projects USING gin(to_tsvector('english', preview_description));
CREATE INDEX idx_job_postings_preview_description ON job_postings USING gin(to_tsvector('english', preview_description));

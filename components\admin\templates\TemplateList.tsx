'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { AdminCard } from '@/components/admin/ui/AdminCard';
import AdminButton from '@/components/admin/ui/AdminButton';
import { AdminTableSkeleton } from '@/components/admin/ui/AdminLoading';
import { useAdminAuth, ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';
import { AdminPermissionWrapper } from '@/components/admin/auth/AdminProtectedRoute';
import { adminContent } from '@/lib/supabase/admin';
import { AdminTemplate } from '@/lib/types/admin';
import {
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  ExternalLink,
  Download,
  MessageSquare,
  Star,
  MoreHorizontal,
} from 'lucide-react';

interface TemplateListProps {
  onEdit?: (template: AdminTemplate) => void;
  onDelete?: (template: AdminTemplate) => void;
}

export default function TemplateList({ onEdit, onDelete }: TemplateListProps) {
  const { hasPermission } = useAdminAuth();
  const [templates, setTemplates] = useState<AdminTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const itemsPerPage = 12;

  // Fetch templates
  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const filters: any = {};
      
      if (statusFilter !== 'all') filters.status = statusFilter;
      if (categoryFilter !== 'all') filters.category = categoryFilter;
      if (searchQuery) filters.search = searchQuery;

      const response = await adminContent.getTemplates(currentPage, itemsPerPage, filters);
      setTemplates(response.data);
      setTotalPages(response.pagination.totalPages);
    } catch (error) {
      console.error('Error fetching templates:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTemplates();
  }, [currentPage, statusFilter, categoryFilter, searchQuery]);

  const handleDelete = async (template: AdminTemplate) => {
    if (!confirm(`Are you sure you want to delete "${template.name}"?`)) return;

    try {
      await adminContent.deleteTemplate(template.id);
      await fetchTemplates(); // Refresh list
      if (onDelete) onDelete(template);
    } catch (error) {
      console.error('Error deleting template:', error);
      alert('Failed to delete template. Please try again.');
    }
  };

  const getStatusBadge = (status: string) => {
    const badges = {
      published: 'bg-green-100 text-green-800',
      draft: 'bg-yellow-100 text-yellow-800',
      archived: 'bg-gray-100 text-gray-800',
    };
    return badges[status as keyof typeof badges] || badges.draft;
  };

  const categories = ['Web Development', 'Mobile Apps', 'E-commerce', 'Landing Pages', 'Dashboards'];

  if (loading) {
    return <AdminTableSkeleton rows={6} columns={4} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 font-jost">Templates</h1>
          <p className="text-gray-600 font-jost">Manage your project templates and showcase items</p>
        </div>
        
        <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.TEMPLATES_CREATE}>
          <Link href="/admin/templates/new">
            <AdminButton icon={<Plus className="h-4 w-4" />}>
              Add Template
            </AdminButton>
          </Link>
        </AdminPermissionWrapper>
      </div>

      {/* Filters */}
      <AdminCard>
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search templates..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
              />
            </div>
          </div>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
          >
            <option value="all">All Status</option>
            <option value="published">Published</option>
            <option value="draft">Draft</option>
            <option value="archived">Archived</option>
          </select>

          {/* Category Filter */}
          <select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
          >
            <option value="all">All Categories</option>
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
        </div>
      </AdminCard>

      {/* Templates Grid */}
      {templates.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {templates.map((template) => (
            <AdminCard key={template.id} className="group hover:shadow-lg transition-all duration-200">
              {/* Template Image */}
              <div className="relative aspect-video bg-gray-100 rounded-lg mb-4 overflow-hidden">
                {template.hero_image_url ? (
                  <img
                    src={template.hero_image_url}
                    alt={template.name}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-gray-400">
                    <Eye className="h-8 w-8" />
                  </div>
                )}
                
                {/* Status Badge */}
                <div className="absolute top-2 left-2">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusBadge(template.status)}`}>
                    {template.status}
                  </span>
                </div>

                {/* Featured Badge */}
                {template.is_featured && (
                  <div className="absolute top-2 right-2">
                    <Star className="h-5 w-5 text-yellow-500 fill-current" />
                  </div>
                )}
              </div>

              {/* Template Info */}
              <div className="space-y-3">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 font-jost line-clamp-1">
                    {template.name}
                  </h3>
                  <p className="text-sm text-gray-600 font-jost line-clamp-2">
                    {template.short_description}
                  </p>
                </div>

                {/* Category & Technologies */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500 font-jost">{template.category}</span>
                    {template.base_price && (
                      <span className="text-sm font-medium text-gray-900 font-jost">
                        ${template.base_price}
                      </span>
                    )}
                  </div>
                  
                  {template.technologies.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {template.technologies.slice(0, 3).map((tech) => (
                        <span
                          key={tech}
                          className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800"
                        >
                          {tech}
                        </span>
                      ))}
                      {template.technologies.length > 3 && (
                        <span className="text-xs text-gray-500 font-jost">
                          +{template.technologies.length - 3} more
                        </span>
                      )}
                    </div>
                  )}
                </div>

                {/* Stats */}
                <div className="flex items-center justify-between text-sm text-gray-500 font-jost">
                  <div className="flex items-center space-x-4">
                    <span className="flex items-center">
                      <Eye className="h-4 w-4 mr-1" />
                      {template.view_count}
                    </span>
                    <span className="flex items-center">
                      <Download className="h-4 w-4 mr-1" />
                      {template.download_count}
                    </span>
                    <span className="flex items-center">
                      <MessageSquare className="h-4 w-4 mr-1" />
                      {template.inquiry_count}
                    </span>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between pt-3 border-t border-gray-200">
                  <div className="flex items-center space-x-2">
                    {template.demo_url && (
                      <a
                        href={template.demo_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-500"
                      >
                        <ExternalLink className="h-4 w-4" />
                      </a>
                    )}
                  </div>

                  <div className="flex items-center space-x-2">
                    <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.TEMPLATES_EDIT}>
                      <button
                        onClick={() => onEdit && onEdit(template)}
                        className="text-gray-600 hover:text-gray-900"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                    </AdminPermissionWrapper>

                    <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.TEMPLATES_DELETE}>
                      <button
                        onClick={() => handleDelete(template)}
                        className="text-red-600 hover:text-red-500"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </AdminPermissionWrapper>
                  </div>
                </div>
              </div>
            </AdminCard>
          ))}
        </div>
      ) : (
        <AdminCard className="text-center py-12">
          <div className="mx-auto h-16 w-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <Eye className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2 font-jost">No templates found</h3>
          <p className="text-gray-600 mb-6 font-jost">
            {searchQuery || statusFilter !== 'all' || categoryFilter !== 'all'
              ? 'Try adjusting your filters to see more results.'
              : 'Get started by creating your first template.'}
          </p>
          <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.TEMPLATES_CREATE}>
            <Link href="/admin/templates/new">
              <AdminButton icon={<Plus className="h-4 w-4" />}>
                Add Template
              </AdminButton>
            </Link>
          </AdminPermissionWrapper>
        </AdminCard>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-700 font-jost">
            Showing page {currentPage} of {totalPages}
          </p>
          <div className="flex items-center space-x-2">
            <AdminButton
              variant="outline"
              size="sm"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(currentPage - 1)}
            >
              Previous
            </AdminButton>
            <AdminButton
              variant="outline"
              size="sm"
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage(currentPage + 1)}
            >
              Next
            </AdminButton>
          </div>
        </div>
      )}
    </div>
  );
}

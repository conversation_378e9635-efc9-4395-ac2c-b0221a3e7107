'use client';

import React, { useState, useEffect } from 'react';
import { AdminCard } from '@/components/admin/ui/AdminCard';
import AdminButton from '@/components/admin/ui/AdminButton';
import { useAdminAuth, ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';
import { AdminPermissionWrapper } from '@/components/admin/auth/AdminProtectedRoute';
// import { adminSettings } from '@/lib/supabase/admin'; // TODO: Add adminSettings export
// import { AdminSystemSetting } from '@/lib/types/admin'; // Using local SystemSetting interface instead

// Local interface for component use
interface SystemSetting {
  key: string;
  value: string;
  category: string;
  type: 'text' | 'textarea' | 'email' | 'number' | 'boolean' | 'select' | 'url' | 'password' | 'color';
  label: string;
  description: string;
  options?: string[];
  required?: boolean;
}
import {
  Save,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Settings,
  Globe,
  Mail,
  Shield,
  Database,
  Palette,
  Bell,
} from 'lucide-react';

interface SettingsGroup {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  settings: SystemSetting[];
}

export default function SystemSettings() {
  const { hasPermission } = useAdminAuth();
  const [settings, setSettings] = useState<SystemSetting[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [successMessage, setSuccessMessage] = useState('');

  // Fetch system settings
  const fetchSettings = async () => {
    try {
      setLoading(true);
      // const data = await adminSettings.getSettings(); // TODO: Implement adminSettings
      // setSettings(data);
    } catch (error) {
      console.error('Error fetching settings:', error);
      
      // Mock data for development
      const mockSettings: SystemSetting[] = [
        // General Settings
        { key: 'site_name', value: 'iREME Soft Hub', category: 'general', type: 'text', label: 'Site Name', description: 'The name of your website' },
        { key: 'site_description', value: 'Elite App Builders - Professional Software Development', category: 'general', type: 'textarea', label: 'Site Description', description: 'A brief description of your website' },
        { key: 'site_url', value: 'https://iremesofthub.com', category: 'general', type: 'url', label: 'Site URL', description: 'The main URL of your website' },
        { key: 'admin_email', value: '<EMAIL>', category: 'general', type: 'email', label: 'Admin Email', description: 'Primary admin email address' },
        { key: 'timezone', value: 'UTC', category: 'general', type: 'select', label: 'Timezone', description: 'Default timezone for the system', options: ['UTC', 'America/New_York', 'Europe/London', 'Asia/Tokyo'] },
        
        // Email Settings
        { key: 'smtp_host', value: 'smtp.gmail.com', category: 'email', type: 'text', label: 'SMTP Host', description: 'SMTP server hostname' },
        { key: 'smtp_port', value: '587', category: 'email', type: 'number', label: 'SMTP Port', description: 'SMTP server port' },
        { key: 'smtp_username', value: '<EMAIL>', category: 'email', type: 'email', label: 'SMTP Username', description: 'SMTP authentication username' },
        { key: 'smtp_password', value: '••••••••', category: 'email', type: 'password', label: 'SMTP Password', description: 'SMTP authentication password' },
        { key: 'from_email', value: '<EMAIL>', category: 'email', type: 'email', label: 'From Email', description: 'Default sender email address' },
        { key: 'from_name', value: 'iREME Soft Hub', category: 'email', type: 'text', label: 'From Name', description: 'Default sender name' },
        
        // Security Settings
        { key: 'enable_2fa', value: 'true', category: 'security', type: 'boolean', label: 'Enable 2FA', description: 'Require two-factor authentication for admin users' },
        { key: 'session_timeout', value: '1440', category: 'security', type: 'number', label: 'Session Timeout (minutes)', description: 'Auto-logout after inactivity' },
        { key: 'max_login_attempts', value: '5', category: 'security', type: 'number', label: 'Max Login Attempts', description: 'Maximum failed login attempts before lockout' },
        { key: 'lockout_duration', value: '30', category: 'security', type: 'number', label: 'Lockout Duration (minutes)', description: 'Account lockout duration after max attempts' },
        { key: 'password_min_length', value: '8', category: 'security', type: 'number', label: 'Minimum Password Length', description: 'Minimum required password length' },
        
        // Appearance Settings
        { key: 'primary_color', value: '#000000', category: 'appearance', type: 'color', label: 'Primary Color', description: 'Main brand color' },
        { key: 'secondary_color', value: '#6B7280', category: 'appearance', type: 'color', label: 'Secondary Color', description: 'Secondary brand color' },
        { key: 'logo_url', value: '/images/logo.png', category: 'appearance', type: 'url', label: 'Logo URL', description: 'URL to your logo image' },
        { key: 'favicon_url', value: '/favicon.ico', category: 'appearance', type: 'url', label: 'Favicon URL', description: 'URL to your favicon' },
        
        // Notification Settings
        { key: 'email_notifications', value: 'true', category: 'notifications', type: 'boolean', label: 'Email Notifications', description: 'Send email notifications for important events' },
        { key: 'new_lead_notifications', value: 'true', category: 'notifications', type: 'boolean', label: 'New Lead Notifications', description: 'Notify when new leads are received' },
        { key: 'system_alerts', value: 'true', category: 'notifications', type: 'boolean', label: 'System Alerts', description: 'Send alerts for system events' },
        
        // Database Settings
        { key: 'backup_frequency', value: 'daily', category: 'database', type: 'select', label: 'Backup Frequency', description: 'How often to backup the database', options: ['hourly', 'daily', 'weekly', 'monthly'] },
        { key: 'backup_retention', value: '30', category: 'database', type: 'number', label: 'Backup Retention (days)', description: 'How long to keep database backups' },
        { key: 'enable_query_logging', value: 'false', category: 'database', type: 'boolean', label: 'Enable Query Logging', description: 'Log database queries for debugging' },
      ];
      
      setSettings(mockSettings);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  // Group settings by category
  const settingsGroups: SettingsGroup[] = [
    {
      id: 'general',
      title: 'General Settings',
      description: 'Basic site configuration and information',
      icon: <Settings className="h-5 w-5" />,
      settings: settings.filter(s => s.category === 'general'),
    },
    {
      id: 'email',
      title: 'Email Configuration',
      description: 'SMTP settings and email preferences',
      icon: <Mail className="h-5 w-5" />,
      settings: settings.filter(s => s.category === 'email'),
    },
    {
      id: 'security',
      title: 'Security Settings',
      description: 'Authentication and security policies',
      icon: <Shield className="h-5 w-5" />,
      settings: settings.filter(s => s.category === 'security'),
    },
    {
      id: 'appearance',
      title: 'Appearance',
      description: 'Branding and visual customization',
      icon: <Palette className="h-5 w-5" />,
      settings: settings.filter(s => s.category === 'appearance'),
    },
    {
      id: 'notifications',
      title: 'Notifications',
      description: 'Email and system notification preferences',
      icon: <Bell className="h-5 w-5" />,
      settings: settings.filter(s => s.category === 'notifications'),
    },
    {
      id: 'database',
      title: 'Database Settings',
      description: 'Backup and maintenance configuration',
      icon: <Database className="h-5 w-5" />,
      settings: settings.filter(s => s.category === 'database'),
    },
  ];

  const handleSettingChange = (key: string, value: string) => {
    setSettings(prev => prev.map(setting => 
      setting.key === key ? { ...setting, value } : setting
    ));
    
    // Clear any existing error for this setting
    if (errors[key]) {
      setErrors(prev => ({ ...prev, [key]: '' }));
    }
  };

  const validateSettings = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    settings.forEach(setting => {
      if (setting.type === 'email' && setting.value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(setting.value)) {
          newErrors[setting.key] = 'Please enter a valid email address';
        }
      }
      
      if (setting.type === 'url' && setting.value) {
        try {
          new URL(setting.value);
        } catch {
          newErrors[setting.key] = 'Please enter a valid URL';
        }
      }
      
      if (setting.type === 'number' && setting.value) {
        if (isNaN(Number(setting.value))) {
          newErrors[setting.key] = 'Please enter a valid number';
        }
      }
    });
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateSettings()) return;
    
    try {
      setSaving(true);
      // await adminSettings.updateSettings(settings); // TODO: Implement adminSettings
      setSuccessMessage('Settings saved successfully!');
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (error) {
      console.error('Error saving settings:', error);
      setErrors({ general: 'Failed to save settings. Please try again.' });
    } finally {
      setSaving(false);
    }
  };

  const renderSettingInput = (setting: SystemSetting) => {
    const baseClasses = `w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost ${
      errors[setting.key] ? 'border-red-300' : 'border-gray-300'
    }`;

    switch (setting.type) {
      case 'textarea':
        return (
          <textarea
            value={setting.value}
            onChange={(e) => handleSettingChange(setting.key, e.target.value)}
            className={baseClasses}
            rows={3}
            placeholder={setting.description}
          />
        );
      
      case 'boolean':
        return (
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={setting.value === 'true'}
              onChange={(e) => handleSettingChange(setting.key, e.target.checked ? 'true' : 'false')}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span className="ml-2 text-sm text-gray-700 font-jost">{setting.label}</span>
          </label>
        );
      
      case 'select':
        return (
          <select
            value={setting.value}
            onChange={(e) => handleSettingChange(setting.key, e.target.value)}
            className={baseClasses}
          >
            {setting.options?.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
        );
      
      case 'color':
        return (
          <div className="flex items-center space-x-3">
            <input
              type="color"
              value={setting.value}
              onChange={(e) => handleSettingChange(setting.key, e.target.value)}
              className="h-10 w-20 border border-gray-300 rounded cursor-pointer"
            />
            <input
              type="text"
              value={setting.value}
              onChange={(e) => handleSettingChange(setting.key, e.target.value)}
              className={`flex-1 ${baseClasses}`}
              placeholder="#000000"
            />
          </div>
        );
      
      default:
        return (
          <input
            type={setting.type}
            value={setting.value}
            onChange={(e) => handleSettingChange(setting.key, e.target.value)}
            className={baseClasses}
            placeholder={setting.description}
          />
        );
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {Array.from({ length: 3 }).map((_, i) => (
          <AdminCard key={i}>
            <div className="animate-pulse">
              <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-10 bg-gray-200 rounded"></div>
              </div>
            </div>
          </AdminCard>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 font-jost">System Settings</h1>
          <p className="text-gray-600 font-jost">Configure your admin dashboard and system preferences</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <AdminButton 
            variant="outline" 
            onClick={fetchSettings}
            icon={<RefreshCw className="h-4 w-4" />}
          >
            Refresh
          </AdminButton>
          
          <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.SYSTEM_SETTINGS}>
            <AdminButton 
              onClick={handleSave}
              loading={saving}
              icon={<Save className="h-4 w-4" />}
            >
              Save Changes
            </AdminButton>
          </AdminPermissionWrapper>
        </div>
      </div>

      {/* Success Message */}
      {successMessage && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-start space-x-3">
          <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
          <p className="text-sm text-green-700 font-jost">{successMessage}</p>
        </div>
      )}

      {/* General Error */}
      {errors.general && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start space-x-3">
          <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
          <p className="text-sm text-red-700 font-jost">{errors.general}</p>
        </div>
      )}

      {/* Settings Groups */}
      {settingsGroups.map(group => (
        <AdminCard key={group.id}>
          <div className="space-y-6">
            {/* Group Header */}
            <div className="flex items-center space-x-3 pb-4 border-b border-gray-200">
              <div className="h-10 w-10 bg-gray-100 rounded-lg flex items-center justify-center">
                {group.icon}
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900 font-jost">{group.title}</h3>
                <p className="text-sm text-gray-600 font-jost">{group.description}</p>
              </div>
            </div>

            {/* Settings */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {group.settings.map(setting => (
                <div key={setting.key} className={setting.type === 'boolean' ? 'md:col-span-2' : ''}>
                  {setting.type !== 'boolean' && (
                    <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                      {setting.label}
                    </label>
                  )}
                  
                  {renderSettingInput(setting)}
                  
                  {setting.type !== 'boolean' && setting.description && (
                    <p className="mt-1 text-sm text-gray-500 font-jost">{setting.description}</p>
                  )}
                  
                  {errors[setting.key] && (
                    <p className="mt-1 text-sm text-red-600 font-jost">{errors[setting.key]}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        </AdminCard>
      ))}
    </div>
  );
}

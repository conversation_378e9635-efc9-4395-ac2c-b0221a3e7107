'use client';

import React from 'react';
import AdminProtectedRoute from '@/components/admin/auth/AdminProtectedRoute';
import AdminLayout from '@/components/admin/layout/AdminLayout';
import { AdminCard, AdminStatCard, AdminQuickActionCard } from '@/components/admin/ui/AdminCard';
import { useAdminAuth } from '@/lib/auth/admin-auth';
import {
  BarChart3,
  Users,
  FileText,
  MessageSquare,
  Settings,
  Shield,
  Clock,
  TrendingUp,
  Eye,
  UserPlus,
  PlusCircle,
  Briefcase,
  PenTool,
  Image
} from 'lucide-react';

export default function AdminDashboardPage() {
  return (
    <AdminProtectedRoute>
      <AdminLayout title="Dashboard" subtitle="Welcome to your admin dashboard">
        <AdminDashboardContent />
      </AdminLayout>
    </AdminProtectedRoute>
  );
}

function AdminDashboardContent() {
  const { adminUser } = useAdminAuth();

  const stats = [
    {
      title: 'Total Page Views',
      value: '12,345',
      change: { value: '+12%', type: 'positive' as const },
      icon: <Eye className="h-6 w-6 text-gray-600" />,
    },
    {
      title: 'New Leads',
      value: '23',
      change: { value: '+5%', type: 'positive' as const },
      icon: <Users className="h-6 w-6 text-gray-600" />,
    },
    {
      title: 'Blog Posts',
      value: '45',
      change: { value: '+2', type: 'positive' as const },
      icon: <FileText className="h-6 w-6 text-gray-600" />,
    },
    {
      title: 'Active Templates',
      value: '18',
      change: { value: '+3', type: 'positive' as const },
      icon: <Briefcase className="h-6 w-6 text-gray-600" />,
    },
  ];

  const quickActions = [
    {
      title: 'Add New Template',
      description: 'Create a new project template',
      icon: <PlusCircle className="h-5 w-5" />,
      onClick: () => console.log('Navigate to new template'),
    },
    {
      title: 'Manage Services',
      description: 'Update service offerings and pricing',
      icon: <Briefcase className="h-5 w-5" />,
      onClick: () => console.log('Navigate to services'),
    },
    {
      title: 'Write Blog Post',
      description: 'Create new blog content',
      icon: <PenTool className="h-5 w-5" />,
      onClick: () => console.log('Navigate to new blog post'),
    },
    {
      title: 'Upload Media',
      description: 'Add images and assets to library',
      icon: <Image className="h-5 w-5" />,
      onClick: () => console.log('Navigate to media upload'),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 font-jost">
          Welcome back, {adminUser?.first_name}!
        </h2>
        <p className="mt-1 text-gray-600 font-jost">
          Here's what's happening with your admin dashboard today.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <AdminStatCard
            key={stat.title}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            icon={stat.icon}
          />
        ))}
      </div>

      {/* Quick Actions */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4 font-jost">
          Quick Actions
        </h3>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {quickActions.map((action) => (
            <AdminQuickActionCard
              key={action.title}
              title={action.title}
              description={action.description}
              icon={action.icon}
              onClick={action.onClick}
            />
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <AdminCard>
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 font-jost">
            Recent Activity
          </h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <Shield className="h-5 w-5 text-green-500" />
              </div>
              <div className="flex-1">
                <p className="text-sm text-gray-900 font-jost">
                  Successfully signed in to admin dashboard
                </p>
                <p className="text-xs text-gray-500 font-jost flex items-center">
                  <Clock className="h-3 w-3 mr-1" />
                  Just now
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <TrendingUp className="h-5 w-5 text-blue-500" />
              </div>
              <div className="flex-1">
                <p className="text-sm text-gray-900 font-jost">
                  Admin dashboard system initialized
                </p>
                <p className="text-xs text-gray-500 font-jost flex items-center">
                  <Clock className="h-3 w-3 mr-1" />
                  2 minutes ago
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <UserPlus className="h-5 w-5 text-purple-500" />
              </div>
              <div className="flex-1">
                <p className="text-sm text-gray-900 font-jost">
                  Professional admin dashboard deployed
                </p>
                <p className="text-xs text-gray-500 font-jost flex items-center">
                  <Clock className="h-3 w-3 mr-1" />
                  5 minutes ago
                </p>
              </div>
            </div>
          </div>
        </div>
      </AdminCard>
    </div>
  );
}

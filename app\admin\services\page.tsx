'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import AdminProtectedRoute from '@/components/admin/auth/AdminProtectedRoute';
import AdminLayout from '@/components/admin/layout/AdminLayout';
import ServiceList from '@/components/admin/services/ServiceList';
import { ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';
import { AdminService } from '@/lib/types/admin';

export default function AdminServicesPage() {
  const router = useRouter();

  const handleEdit = (service: AdminService) => {
    router.push(`/admin/services/${service.id}/edit`);
  };

  const handleDelete = (service: AdminService) => {
    // Service deletion is handled in the ServiceList component
    console.log('Service deleted:', service.title);
  };

  return (
    <AdminProtectedRoute requiredPermission={ADMIN_PERMISSIONS.SERVICES_VIEW}>
      <AdminLayout 
        title="Services" 
        subtitle="Manage your service offerings and packages"
      >
        <ServiceList onEdit={handleEdit} onDelete={handleDelete} />
      </AdminLayout>
    </AdminProtectedRoute>
  );
}

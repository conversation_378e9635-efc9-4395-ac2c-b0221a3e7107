'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AdminCard } from '@/components/admin/ui/AdminCard';
import AdminButton from '@/components/admin/ui/AdminButton';
import { adminContent } from '@/lib/supabase/admin';
import { AdminService, ServicePackage, ProcessStep } from '@/lib/types/admin';
import {
  Save,
  Plus,
  X,
  AlertCircle,
  DollarSign,
  Clock,
  Package,
} from 'lucide-react';

interface ServiceFormProps {
  service?: AdminService;
  onSave?: (service: AdminService) => void;
  onCancel?: () => void;
}

interface FormData {
  title: string;
  slug: string;
  description: string;
  short_description: string;
  icon_name: string;
  features: string[];
  benefits: string[];
  process_steps: ProcessStep[];
  starting_price: string;
  price_range_min: number | null;
  price_range_max: number | null;
  pricing_model: 'fixed' | 'hourly' | 'project';
  estimated_timeline: string;
  timeline_min_days: number | null;
  timeline_max_days: number | null;
  packages: ServicePackage[];
  meta_title: string;
  meta_description: string;
  keywords: string[];
  status: 'active' | 'inactive' | 'coming_soon';
  is_featured: boolean;
  sort_order: number;
}

interface FormErrors {
  [key: string]: string;
}

export default function ServiceForm({ service, onSave, onCancel }: ServiceFormProps) {
  const router = useRouter();
  const [loading, setSaving] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [newFeature, setNewFeature] = useState('');
  const [newBenefit, setNewBenefit] = useState('');
  const [newKeyword, setNewKeyword] = useState('');

  const [formData, setFormData] = useState<FormData>({
    title: '',
    slug: '',
    description: '',
    short_description: '',
    icon_name: 'briefcase',
    features: [],
    benefits: [],
    process_steps: [],
    starting_price: '',
    price_range_min: null,
    price_range_max: null,
    pricing_model: 'project',
    estimated_timeline: '',
    timeline_min_days: null,
    timeline_max_days: null,
    packages: [],
    meta_title: '',
    meta_description: '',
    keywords: [],
    status: 'active',
    is_featured: false,
    sort_order: 0,
  });

  // Initialize form with service data if editing
  useEffect(() => {
    if (service) {
      setFormData({
        title: service.title,
        slug: service.slug,
        description: service.description,
        short_description: service.short_description || '',
        icon_name: service.icon_name || 'briefcase',
        features: service.features,
        benefits: service.benefits,
        process_steps: service.process_steps,
        starting_price: service.starting_price || '',
        price_range_min: service.price_range_min ?? null,
        price_range_max: service.price_range_max ?? null,
        pricing_model: service.pricing_model,
        estimated_timeline: service.estimated_timeline || '',
        timeline_min_days: service.timeline_min_days ?? null,
        timeline_max_days: service.timeline_max_days ?? null,
        packages: service.packages,
        meta_title: service.meta_title || '',
        meta_description: service.meta_description || '',
        keywords: service.keywords,
        status: service.status,
        is_featured: service.is_featured,
        sort_order: service.sort_order,
      });
    }
  }, [service]);

  // Generate slug from title
  useEffect(() => {
    if (formData.title && !service) {
      const slug = formData.title
        .toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, '-')
        .trim();
      setFormData(prev => ({ ...prev, slug }));
    }
  }, [formData.title, service]);

  const pricingModels = [
    { value: 'fixed', label: 'Fixed Price' },
    { value: 'hourly', label: 'Hourly Rate' },
    { value: 'project', label: 'Project-based' },
  ];

  const iconOptions = [
    'briefcase', 'code', 'smartphone', 'shopping-cart', 'layout',
    'database', 'cloud', 'shield', 'zap', 'globe', 'palette', 'settings'
  ];

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.title.trim()) newErrors.title = 'Title is required';
    if (!formData.slug.trim()) newErrors.slug = 'Slug is required';
    if (!formData.description.trim()) newErrors.description = 'Description is required';
    if (!formData.short_description.trim()) newErrors.short_description = 'Short description is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const addArrayItem = (field: 'features' | 'benefits' | 'keywords', value: string) => {
    if (value.trim() && !formData[field].includes(value.trim())) {
      setFormData(prev => ({
        ...prev,
        [field]: [...prev[field], value.trim()],
      }));
    }
  };

  const removeArrayItem = (field: 'features' | 'benefits' | 'keywords', index: number) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index),
    }));
  };

  const addProcessStep = () => {
    const newStep: ProcessStep = {
      step: formData.process_steps.length + 1,
      title: '',
      description: '',
      icon: 'circle',
    };
    setFormData(prev => ({
      ...prev,
      process_steps: [...prev.process_steps, newStep],
    }));
  };

  const updateProcessStep = (index: number, field: keyof ProcessStep, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      process_steps: prev.process_steps.map((step, i) =>
        i === index ? { ...step, [field]: value } : step
      ),
    }));
  };

  const removeProcessStep = (index: number) => {
    setFormData(prev => ({
      ...prev,
      process_steps: prev.process_steps.filter((_, i) => i !== index),
    }));
  };

  const addPackage = () => {
    const newPackage: ServicePackage = {
      id: `pkg-${Date.now()}`,
      name: '',
      description: '',
      price: 0,
      features: [],
      timeline: '',
      is_popular: false,
    };
    setFormData(prev => ({
      ...prev,
      packages: [...prev.packages, newPackage],
    }));
  };

  const updatePackage = (index: number, field: keyof ServicePackage, value: any) => {
    setFormData(prev => ({
      ...prev,
      packages: prev.packages.map((pkg, i) =>
        i === index ? { ...pkg, [field]: value } : pkg
      ),
    }));
  };

  const removePackage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      packages: prev.packages.filter((_, i) => i !== index),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      setSaving(true);
      
      let savedService: AdminService;
      
      if (service) {
        // Update existing service
        savedService = await adminContent.updateService(service.id, formData);
      } else {
        // Create new service
        savedService = await adminContent.createService(formData);
      }

      if (onSave) {
        onSave(savedService);
      } else {
        router.push('/admin/services');
      }
    } catch (error) {
      console.error('Error saving service:', error);
      setErrors({ general: 'Failed to save service. Please try again.' });
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      router.push('/admin/services');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* General Error */}
      {errors.general && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start space-x-3">
          <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
          <p className="text-sm text-red-700 font-jost">{errors.general}</p>
        </div>
      )}

      {/* Basic Information */}
      <AdminCard>
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 font-jost">Basic Information</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                Service Title *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost ${
                  errors.title ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Enter service title"
              />
              {errors.title && <p className="mt-1 text-sm text-red-600 font-jost">{errors.title}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                Slug *
              </label>
              <input
                type="text"
                value={formData.slug}
                onChange={(e) => handleInputChange('slug', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost ${
                  errors.slug ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="service-slug"
              />
              {errors.slug && <p className="mt-1 text-sm text-red-600 font-jost">{errors.slug}</p>}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
              Short Description *
            </label>
            <textarea
              value={formData.short_description}
              onChange={(e) => handleInputChange('short_description', e.target.value)}
              rows={2}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost ${
                errors.short_description ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Brief description of the service"
            />
            {errors.short_description && <p className="mt-1 text-sm text-red-600 font-jost">{errors.short_description}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
              Full Description *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={6}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost ${
                errors.description ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Detailed description of the service"
            />
            {errors.description && <p className="mt-1 text-sm text-red-600 font-jost">{errors.description}</p>}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                Icon
              </label>
              <select
                value={formData.icon_name}
                onChange={(e) => handleInputChange('icon_name', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
              >
                {iconOptions.map(icon => (
                  <option key={icon} value={icon}>{icon}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                Pricing Model
              </label>
              <select
                value={formData.pricing_model}
                onChange={(e) => handleInputChange('pricing_model', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
              >
                {pricingModels.map(model => (
                  <option key={model.value} value={model.value}>{model.label}</option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </AdminCard>

      {/* Features */}
      <AdminCard>
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 font-jost">Features</h3>
          
          <div className="flex space-x-2">
            <input
              type="text"
              value={newFeature}
              onChange={(e) => setNewFeature(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
              placeholder="Add feature"
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  addArrayItem('features', newFeature);
                  setNewFeature('');
                }
              }}
            />
            <AdminButton
              type="button"
              variant="outline"
              onClick={() => {
                addArrayItem('features', newFeature);
                setNewFeature('');
              }}
              icon={<Plus className="h-4 w-4" />}
            >
              Add
            </AdminButton>
          </div>

          <div className="flex flex-wrap gap-2">
            {formData.features.map((feature, index) => (
              <span
                key={index}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
              >
                {feature}
                <button
                  type="button"
                  onClick={() => removeArrayItem('features', index)}
                  className="ml-2 text-blue-600 hover:text-blue-500"
                >
                  <X className="h-3 w-3" />
                </button>
              </span>
            ))}
          </div>
        </div>
      </AdminCard>

      {/* Actions */}
      <div className="flex items-center justify-between pt-6 border-t border-gray-200">
        <div className="flex items-center space-x-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.is_featured}
              onChange={(e) => handleInputChange('is_featured', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span className="ml-2 text-sm text-gray-700 font-jost">Featured Service</span>
          </label>

          <select
            value={formData.status}
            onChange={(e) => handleInputChange('status', e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
          >
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="coming_soon">Coming Soon</option>
          </select>
        </div>

        <div className="flex items-center space-x-3">
          <AdminButton
            type="button"
            variant="outline"
            onClick={handleCancel}
          >
            Cancel
          </AdminButton>
          
          <AdminButton
            type="submit"
            loading={loading}
            icon={<Save className="h-4 w-4" />}
          >
            {service ? 'Update Service' : 'Create Service'}
          </AdminButton>
        </div>
      </div>
    </form>
  );
}

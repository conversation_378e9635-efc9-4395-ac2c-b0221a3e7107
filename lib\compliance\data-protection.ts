/**
 * Data Protection and Privacy Compliance System
 * Implements GDPR, CCPA, and other privacy regulations compliance
 */

interface PersonalDataField {
  field: string;
  type: 'pii' | 'sensitive' | 'financial' | 'health' | 'biometric';
  required: boolean;
  retention: number; // days
  purpose: string[];
  lawfulBasis: 'consent' | 'contract' | 'legal_obligation' | 'vital_interests' | 'public_task' | 'legitimate_interests';
}

interface DataSubject {
  id: string;
  email: string;
  consentStatus: ConsentStatus;
  dataCategories: string[];
  retentionDate: number;
  lastAccessed: number;
  requests: DataSubjectRequest[];
}

interface ConsentStatus {
  marketing: boolean;
  analytics: boolean;
  functional: boolean;
  necessary: boolean;
  timestamp: number;
  ipAddress: string;
  userAgent: string;
}

interface DataSubjectRequest {
  id: string;
  type: 'access' | 'rectification' | 'erasure' | 'portability' | 'restriction' | 'objection';
  status: 'pending' | 'in_progress' | 'completed' | 'rejected';
  requestDate: number;
  completionDate?: number;
  requestedBy: string;
  processedBy?: string;
  details: string;
  response?: string;
  documents?: string[];
}

interface DataProcessingActivity {
  id: string;
  name: string;
  description: string;
  controller: string;
  processor?: string;
  dataCategories: string[];
  dataSubjects: string[];
  purposes: string[];
  lawfulBasis: string;
  retention: number;
  transfers: DataTransfer[];
  securityMeasures: string[];
  lastReviewed: number;
}

interface DataTransfer {
  recipient: string;
  country: string;
  adequacyDecision: boolean;
  safeguards: string[];
  purpose: string;
}

interface PrivacyImpactAssessment {
  id: string;
  name: string;
  description: string;
  riskLevel: 'low' | 'medium' | 'high';
  dataTypes: string[];
  risks: PrivacyRisk[];
  mitigations: string[];
  status: 'draft' | 'review' | 'approved' | 'rejected';
  assessor: string;
  reviewDate: number;
  approvalDate?: number;
}

interface PrivacyRisk {
  id: string;
  description: string;
  likelihood: 'low' | 'medium' | 'high';
  impact: 'low' | 'medium' | 'high';
  riskScore: number;
  mitigation: string;
  residualRisk: 'low' | 'medium' | 'high';
}

class DataProtectionManager {
  private dataSubjects: Map<string, DataSubject> = new Map();
  private processingActivities: DataProcessingActivity[] = [];
  private privacyImpactAssessments: PrivacyImpactAssessment[] = [];
  private personalDataFields: PersonalDataField[] = [];
  private retentionPolicies: Map<string, number> = new Map();

  constructor() {
    this.initializePersonalDataFields();
    this.initializeRetentionPolicies();
    this.startRetentionCleanup();
  }

  /**
   * Initialize personal data field definitions
   */
  private initializePersonalDataFields() {
    this.personalDataFields = [
      {
        field: 'email',
        type: 'pii',
        required: true,
        retention: 2555, // 7 years
        purpose: ['communication', 'account_management'],
        lawfulBasis: 'contract',
      },
      {
        field: 'full_name',
        type: 'pii',
        required: true,
        retention: 2555,
        purpose: ['identification', 'communication'],
        lawfulBasis: 'contract',
      },
      {
        field: 'phone',
        type: 'pii',
        required: false,
        retention: 1095, // 3 years
        purpose: ['communication', 'support'],
        lawfulBasis: 'consent',
      },
      {
        field: 'ip_address',
        type: 'pii',
        required: false,
        retention: 365, // 1 year
        purpose: ['security', 'analytics'],
        lawfulBasis: 'legitimate_interests',
      },
      {
        field: 'payment_info',
        type: 'financial',
        required: false,
        retention: 2555, // 7 years (legal requirement)
        purpose: ['payment_processing', 'accounting'],
        lawfulBasis: 'legal_obligation',
      },
    ];
  }

  /**
   * Initialize retention policies
   */
  private initializeRetentionPolicies() {
    this.retentionPolicies.set('user_data', 2555); // 7 years
    this.retentionPolicies.set('audit_logs', 2555); // 7 years
    this.retentionPolicies.set('security_logs', 1095); // 3 years
    this.retentionPolicies.set('analytics_data', 730); // 2 years
    this.retentionPolicies.set('marketing_data', 1095); // 3 years
  }

  /**
   * Record consent
   */
  recordConsent(
    userId: string,
    email: string,
    consent: ConsentStatus
  ): void {
    const dataSubject: DataSubject = {
      id: userId,
      email,
      consentStatus: consent,
      dataCategories: this.determineDataCategories(consent),
      retentionDate: Date.now() + (this.retentionPolicies.get('user_data')! * 24 * 60 * 60 * 1000),
      lastAccessed: Date.now(),
      requests: [],
    };

    this.dataSubjects.set(userId, dataSubject);
    this.auditConsentChange(userId, consent);
  }

  /**
   * Update consent
   */
  updateConsent(
    userId: string,
    consent: Partial<ConsentStatus>
  ): boolean {
    const dataSubject = this.dataSubjects.get(userId);
    if (!dataSubject) return false;

    const oldConsent = { ...dataSubject.consentStatus };
    dataSubject.consentStatus = { ...dataSubject.consentStatus, ...consent };
    dataSubject.dataCategories = this.determineDataCategories(dataSubject.consentStatus);
    dataSubject.lastAccessed = Date.now();

    this.auditConsentChange(userId, dataSubject.consentStatus, oldConsent);
    return true;
  }

  /**
   * Check if processing is lawful
   */
  isProcessingLawful(
    userId: string,
    purpose: string,
    dataType: string
  ): boolean {
    const dataSubject = this.dataSubjects.get(userId);
    if (!dataSubject) return false;

    const field = this.personalDataFields.find(f => f.field === dataType);
    if (!field) return false;

    // Check lawful basis
    switch (field.lawfulBasis) {
      case 'consent':
        return this.hasValidConsent(dataSubject, purpose);
      case 'contract':
        return true; // Assume contract exists for registered users
      case 'legal_obligation':
        return true; // Legal requirements
      case 'legitimate_interests':
        return this.hasLegitimateInterest(purpose);
      default:
        return false;
    }
  }

  /**
   * Submit data subject request
   */
  submitDataSubjectRequest(
    userId: string,
    type: DataSubjectRequest['type'],
    details: string,
    requestedBy: string
  ): string {
    const dataSubject = this.dataSubjects.get(userId);
    if (!dataSubject) {
      throw new Error('Data subject not found');
    }

    const request: DataSubjectRequest = {
      id: this.generateRequestId(),
      type,
      status: 'pending',
      requestDate: Date.now(),
      requestedBy,
      details,
    };

    dataSubject.requests.push(request);
    this.auditDataSubjectRequest(userId, request);

    return request.id;
  }

  /**
   * Process data subject request
   */
  async processDataSubjectRequest(
    requestId: string,
    processedBy: string,
    response?: string
  ): Promise<boolean> {
    for (const [userId, dataSubject] of this.dataSubjects.entries()) {
      const request = dataSubject.requests.find(r => r.id === requestId);
      if (request) {
        request.status = 'in_progress';
        request.processedBy = processedBy;

        try {
          await this.executeDataSubjectRequest(userId, request);
          request.status = 'completed';
          request.completionDate = Date.now();
          request.response = response;
          
          this.auditDataSubjectRequest(userId, request);
          return true;
        } catch (error) {
          request.status = 'rejected';
          request.response = `Error: ${error}`;
          return false;
        }
      }
    }

    return false;
  }

  /**
   * Execute data subject request
   */
  private async executeDataSubjectRequest(
    userId: string,
    request: DataSubjectRequest
  ): Promise<void> {
    switch (request.type) {
      case 'access':
        await this.generateDataExport(userId);
        break;
      case 'erasure':
        await this.eraseUserData(userId);
        break;
      case 'portability':
        await this.generatePortableData(userId);
        break;
      case 'rectification':
        // Manual process - requires admin intervention
        break;
      case 'restriction':
        await this.restrictProcessing(userId);
        break;
      case 'objection':
        await this.handleObjection(userId, request.details);
        break;
    }
  }

  /**
   * Generate data export for access request
   */
  private async generateDataExport(userId: string): Promise<string> {
    const dataSubject = this.dataSubjects.get(userId);
    if (!dataSubject) throw new Error('Data subject not found');

    // In a real implementation, this would collect all user data
    const exportData = {
      personalData: {
        id: dataSubject.id,
        email: dataSubject.email,
        consentStatus: dataSubject.consentStatus,
        dataCategories: dataSubject.dataCategories,
      },
      // Add other data categories as needed
      auditTrail: dataSubject.requests,
      exportDate: new Date().toISOString(),
    };

    // Generate export file
    const exportJson = JSON.stringify(exportData, null, 2);
    
    // In a real implementation, this would be saved to secure storage
    console.log('Data export generated for user:', userId);
    
    return exportJson;
  }

  /**
   * Erase user data (Right to be forgotten)
   */
  private async eraseUserData(userId: string): Promise<void> {
    // Check if erasure is possible (legal obligations, etc.)
    if (!this.canEraseData(userId)) {
      throw new Error('Data cannot be erased due to legal obligations');
    }

    // In a real implementation, this would:
    // 1. Delete user data from all systems
    // 2. Anonymize audit logs
    // 3. Remove from backups (where possible)
    // 4. Notify third parties

    this.dataSubjects.delete(userId);
    console.log('User data erased for:', userId);
  }

  /**
   * Check if data can be erased
   */
  private canEraseData(userId: string): boolean {
    // Check for legal obligations that prevent erasure
    // e.g., financial records, legal proceedings, etc.
    return true; // Simplified for demo
  }

  /**
   * Generate portable data
   */
  private async generatePortableData(userId: string): Promise<string> {
    // Similar to data export but in a structured, machine-readable format
    return this.generateDataExport(userId);
  }

  /**
   * Restrict processing
   */
  private async restrictProcessing(userId: string): Promise<void> {
    const dataSubject = this.dataSubjects.get(userId);
    if (dataSubject) {
      // Mark data as restricted - only store, don't process
      dataSubject.consentStatus.functional = false;
      dataSubject.consentStatus.marketing = false;
      dataSubject.consentStatus.analytics = false;
    }
  }

  /**
   * Handle objection to processing
   */
  private async handleObjection(userId: string, details: string): Promise<void> {
    // Process objection based on details
    // May require manual review
    console.log('Processing objection for user:', userId, details);
  }

  /**
   * Check data retention and cleanup
   */
  private startRetentionCleanup(): void {
    setInterval(() => {
      this.cleanupExpiredData();
    }, 24 * 60 * 60 * 1000); // Daily cleanup
  }

  /**
   * Cleanup expired data
   */
  private cleanupExpiredData(): void {
    const now = Date.now();
    
    for (const [userId, dataSubject] of this.dataSubjects.entries()) {
      if (now > dataSubject.retentionDate) {
        // Check if data can be deleted
        if (this.canEraseData(userId)) {
          this.dataSubjects.delete(userId);
          console.log('Expired data cleaned up for user:', userId);
        }
      }
    }
  }

  /**
   * Determine data categories based on consent
   */
  private determineDataCategories(consent: ConsentStatus): string[] {
    const categories: string[] = ['necessary']; // Always included
    
    if (consent.functional) categories.push('functional');
    if (consent.analytics) categories.push('analytics');
    if (consent.marketing) categories.push('marketing');
    
    return categories;
  }

  /**
   * Check if user has valid consent for purpose
   */
  private hasValidConsent(dataSubject: DataSubject, purpose: string): boolean {
    const consent = dataSubject.consentStatus;
    
    switch (purpose) {
      case 'marketing':
        return consent.marketing;
      case 'analytics':
        return consent.analytics;
      case 'functional':
        return consent.functional;
      default:
        return consent.necessary; // Necessary cookies don't require consent
    }
  }

  /**
   * Check legitimate interest
   */
  private hasLegitimateInterest(purpose: string): boolean {
    const legitimateInterests = ['security', 'fraud_prevention', 'system_administration'];
    return legitimateInterests.includes(purpose);
  }

  /**
   * Audit consent changes
   */
  private auditConsentChange(
    userId: string,
    newConsent: ConsentStatus,
    oldConsent?: ConsentStatus
  ): void {
    // In a real implementation, this would use the audit logger
    console.log('Consent change audited:', { userId, newConsent, oldConsent });
  }

  /**
   * Audit data subject requests
   */
  private auditDataSubjectRequest(userId: string, request: DataSubjectRequest): void {
    // In a real implementation, this would use the audit logger
    console.log('Data subject request audited:', { userId, request });
  }

  /**
   * Generate request ID
   */
  private generateRequestId(): string {
    return `dsr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get compliance report
   */
  getComplianceReport(): {
    totalDataSubjects: number;
    consentRates: Record<string, number>;
    pendingRequests: number;
    completedRequests: number;
    dataRetentionStatus: Record<string, number>;
    riskAssessments: number;
  } {
    const totalDataSubjects = this.dataSubjects.size;
    const consentRates: Record<string, number> = {
      marketing: 0,
      analytics: 0,
      functional: 0,
    };
    
    let pendingRequests = 0;
    let completedRequests = 0;

    for (const dataSubject of this.dataSubjects.values()) {
      // Count consent rates
      if (dataSubject.consentStatus.marketing) consentRates.marketing++;
      if (dataSubject.consentStatus.analytics) consentRates.analytics++;
      if (dataSubject.consentStatus.functional) consentRates.functional++;

      // Count requests
      dataSubject.requests.forEach(request => {
        if (request.status === 'pending' || request.status === 'in_progress') {
          pendingRequests++;
        } else if (request.status === 'completed') {
          completedRequests++;
        }
      });
    }

    // Convert to percentages
    Object.keys(consentRates).forEach(key => {
      consentRates[key] = totalDataSubjects > 0 
        ? (consentRates[key] / totalDataSubjects) * 100 
        : 0;
    });

    return {
      totalDataSubjects,
      consentRates,
      pendingRequests,
      completedRequests,
      dataRetentionStatus: Object.fromEntries(this.retentionPolicies),
      riskAssessments: this.privacyImpactAssessments.length,
    };
  }
}

// Create singleton instance
export const dataProtectionManager = new DataProtectionManager();

// React hook for data protection
export function useDataProtection() {
  return {
    recordConsent: dataProtectionManager.recordConsent.bind(dataProtectionManager),
    updateConsent: dataProtectionManager.updateConsent.bind(dataProtectionManager),
    isProcessingLawful: dataProtectionManager.isProcessingLawful.bind(dataProtectionManager),
    submitDataSubjectRequest: dataProtectionManager.submitDataSubjectRequest.bind(dataProtectionManager),
    getComplianceReport: dataProtectionManager.getComplianceReport.bind(dataProtectionManager),
  };
}

export default DataProtectionManager;

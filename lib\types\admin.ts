// =====================================================
// ADMIN DASHBOARD TYPESCRIPT TYPES
// Professional-grade types for iREME Soft Hub Admin Dashboard
// =====================================================

// Note: Using generic types instead of generated Supabase types for now
// TODO: Generate Supabase types with `supabase gen types typescript`

// =====================================================
// ADMIN USER TYPES
// =====================================================

export type AdminUserRole = 'super_admin' | 'admin' | 'editor' | 'viewer';

export interface AdminUser {
  id: string;
  created_at: string;
  updated_at: string;
  email: string;
  supabase_user_id?: string;
  first_name: string;
  last_name: string;
  avatar_url?: string;
  phone?: string;
  role: AdminUserRole;
  permissions: Record<string, boolean>;
  is_active: boolean;
  is_verified: boolean;
  last_login_at?: string;
  login_attempts: number;
  locked_until?: string;
  two_factor_enabled: boolean;
  created_by?: string;
  updated_by?: string;
}

export interface AdminSession {
  id: string;
  admin_user_id: string;
  session_token: string;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
  expires_at: string;
  is_active: boolean;
}

// =====================================================
// CONTENT MANAGEMENT TYPES
// =====================================================

export type ContentStatus = 'draft' | 'published' | 'archived';
export type ServiceStatus = 'active' | 'inactive' | 'coming_soon';
export type BlogPostStatus = 'draft' | 'published' | 'scheduled' | 'archived';

export interface AdminTemplate {
  id: string;
  created_at: string;
  updated_at: string;
  name: string;
  slug: string;
  short_description?: string;
  full_description?: string;
  hero_image_url?: string;
  gallery_images: string[];
  demo_url?: string;
  source_code_url?: string;
  category: string;
  tags: string[];
  technologies: string[];
  features: string[];
  base_price?: number | null;
  custom_price?: number | null;
  license_type: string;
  meta_title?: string;
  meta_description?: string;
  keywords: string[];
  status: ContentStatus;
  is_featured: boolean;
  sort_order: number;
  view_count: number;
  download_count: number;
  inquiry_count: number;
  created_by?: string;
  updated_by?: string;
}

export interface AdminService {
  id: string;
  created_at: string;
  updated_at: string;
  title: string;
  slug: string;
  description: string;
  short_description?: string;
  icon_name?: string;
  features: string[];
  benefits: string[];
  process_steps: ProcessStep[];
  starting_price?: string;
  price_range_min?: number | null;
  price_range_max?: number | null;
  pricing_model: 'fixed' | 'hourly' | 'project';
  estimated_timeline?: string;
  timeline_min_days?: number | null;
  timeline_max_days?: number | null;
  packages: ServicePackage[];
  meta_title?: string;
  meta_description?: string;
  keywords: string[];
  status: ServiceStatus;
  is_featured: boolean;
  sort_order: number;
  view_count: number;
  inquiry_count: number;
  created_by?: string;
  updated_by?: string;
}

export interface ProcessStep {
  step: number;
  title: string;
  description: string;
  icon?: string;
}

export interface ServicePackage {
  id: string;
  name: string;
  description: string;
  price: number;
  features: string[];
  timeline: string;
  is_popular?: boolean;
}

export interface AdminBlogPost {
  id: string;
  created_at: string;
  updated_at: string;
  title: string;
  slug: string;
  excerpt?: string;
  content: string;
  content_type: 'markdown' | 'html' | 'rich_text';
  featured_image_url?: string;
  gallery_images: string[];
  category?: string;
  tags: string[];
  meta_title?: string;
  meta_description?: string;
  keywords: string[];
  canonical_url?: string;
  status: BlogPostStatus;
  published_at?: string;
  scheduled_for?: string;
  reading_time_minutes?: number;
  view_count: number;
  like_count: number;
  share_count: number;
  author_id?: string;
  author_name?: string;
  author_bio?: string;
  author_avatar_url?: string;
  created_by?: string;
  updated_by?: string;
}

export interface AdminMediaFile {
  id: string;
  created_at: string;
  updated_at: string;
  filename: string;
  original_filename: string;
  file_path: string;
  file_url: string;
  file_size: number;
  mime_type: string;
  file_extension?: string;
  width?: number;
  height?: number;
  alt_text?: string;
  caption?: string;
  folder: string;
  tags: string[];
  usage_count: number;
  last_used_at?: string;
  seo_filename?: string;
  uploaded_by?: string;
}

// =====================================================
// LEAD & CUSTOMER MANAGEMENT TYPES
// =====================================================

export type LeadStatus = 'new' | 'contacted' | 'qualified' | 'proposal_sent' | 'negotiating' | 'won' | 'lost' | 'on_hold';
export type LeadPriority = 'low' | 'medium' | 'high' | 'urgent';
export type ContactStatus = 'new' | 'read' | 'replied' | 'resolved' | 'spam';
export type CustomerType = 'prospect' | 'active' | 'inactive' | 'former';

export interface AdminQuoteRequest {
  id: string;
  created_at: string;
  updated_at: string;
  full_name: string;
  email: string;
  phone?: string;
  company_name?: string;
  website?: string;
  project_type: string;
  project_description: string;
  budget_range?: string;
  budget_min?: number;
  budget_max?: number;
  timeline?: string;
  timeline_urgency: string;
  required_features: string[];
  technical_requirements?: string;
  design_preferences?: string;
  target_audience?: string;
  additional_services: string[];
  special_requirements?: string;
  how_did_you_hear?: string;
  status: LeadStatus;
  priority: LeadPriority;
  lead_score: number;
  estimated_value?: number;
  assigned_to?: string;
  next_follow_up_date?: string;
  last_contact_date?: string;
  contact_attempts: number;
  source: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string | null;
  referrer_url?: string | null;
  ip_address?: string | null;
  user_agent?: string | null;
  created_by?: string | null;
  updated_by?: string | null;
}

export interface AdminContactSubmission {
  id: string;
  created_at: string;
  updated_at: string;
  name: string;
  email: string;
  phone?: string;
  company?: string;
  subject?: string;
  message: string;
  inquiry_type: string;
  status: ContactStatus;
  priority: LeadPriority;
  assigned_to?: string;
  response_sent: boolean;
  response_date?: string;
  response_by?: string;
  form_source: string;
  page_url?: string;
  ip_address?: string;
  user_agent?: string;
  created_by?: string;
  updated_by?: string;
}

export interface AdminCustomer {
  id: string;
  created_at: string;
  updated_at: string;
  company_name?: string;
  contact_person: string;
  email: string;
  phone?: string;
  website?: string;
  address_line_1?: string;
  address_line_2?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  industry?: string;
  company_size?: string;
  annual_revenue_range?: string;
  customer_type: CustomerType;
  acquisition_date?: string;
  acquisition_source?: string;
  account_manager_id?: string;
  total_project_value: number;
  total_paid: number;
  outstanding_balance: number;
  preferred_contact_method: string;
  communication_frequency: string;
  notes?: string;
  tags: string[];
  created_by?: string;
  updated_by?: string;
}

// =====================================================
// ANALYTICS & REPORTING TYPES
// =====================================================

export interface AdminAnalyticsEvent {
  id: string;
  created_at: string;
  event_type: string;
  event_name: string;
  event_category?: string;
  page_url: string;
  page_title?: string;
  referrer_url?: string;
  session_id?: string;
  user_id?: string;
  ip_address?: string;
  user_agent?: string;
  country?: string;
  region?: string;
  city?: string;
  device_type?: string;
  browser?: string;
  operating_system?: string;
  screen_resolution?: string;
  properties: Record<string, any>;
  page_load_time?: number;
  time_on_page?: number;
}

export interface AdminBusinessMetrics {
  id: string;
  date: string;
  created_at: string;
  total_page_views: number;
  unique_visitors: number;
  bounce_rate: number;
  avg_session_duration: number;
  new_quote_requests: number;
  new_contact_submissions: number;
  total_leads: number;
  qualified_leads: number;
  conversion_rate: number;
  blog_views: number;
  template_views: number;
  service_page_views: number;
  new_customers: number;
  total_revenue: number;
  avg_project_value: number;
  avg_page_load_time: number;
  server_uptime_percentage: number;
}

// =====================================================
// SYSTEM ADMINISTRATION TYPES
// =====================================================

export interface AdminSystemSetting {
  id: string;
  created_at: string;
  updated_at: string;
  setting_key: string;
  setting_value?: string;
  setting_type: 'string' | 'number' | 'boolean' | 'json';
  category: string;
  subcategory?: string;
  description?: string;
  is_public: boolean;
  is_encrypted: boolean;
  validation_rules: Record<string, any>;
  updated_by?: string;
}

export interface AdminAuditLog {
  id: string;
  created_at: string;
  admin_user_id?: string;
  user_email?: string;
  user_name?: string;
  action: string;
  resource_type: string;
  resource_id?: string;
  resource_name?: string;
  description?: string;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  session_id?: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
}

export interface AdminEmailTemplate {
  id: string;
  created_at: string;
  updated_at: string;
  name: string;
  slug: string;
  description?: string;
  subject: string;
  html_content: string;
  text_content?: string;
  variables: string[];
  template_type: string;
  is_active: boolean;
  created_by?: string;
  updated_by?: string;
}

// =====================================================
// DASHBOARD & UI TYPES
// =====================================================

export interface DashboardStats {
  totalPageViews: number;
  uniqueVisitors: number;
  avgSessionDuration: number;
  bounceRate: number;
  newLeads: number;
  conversionRate: number;
  totalRevenue: number;
  avgProjectValue: number;
}

export interface TopPerformingContent {
  id: string;
  title: string;
  type: string;
  views: number;
  conversions: number;
  conversionRate: number;
  revenue: number;
  // Legacy properties for backward compatibility
  contentType?: string;
  contentId?: string;
  contentName?: string;
  viewCount?: number;
  engagementScore?: number;
}

export interface LeadFollowUp {
  id: string;
  fullName: string;
  email: string;
  companyName?: string;
  projectType: string;
  status: LeadStatus;
  priority: LeadPriority;
  leadScore: number;
  nextFollowUpDate?: string;
  daysOverdue: number;
  assignedToName: string;
}

// =====================================================
// FORM & API TYPES
// =====================================================

export interface AdminLoginForm {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface AdminUserForm {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  role: AdminUserRole;
  isActive: boolean;
}

export interface TemplateForm {
  name: string;
  shortDescription?: string;
  fullDescription?: string;
  category: string;
  technologies: string[];
  features: string[];
  demoUrl?: string;
  basePrice?: number;
  status: ContentStatus;
  isFeatured: boolean;
}

export interface ServiceForm {
  title: string;
  description: string;
  shortDescription?: string;
  iconName?: string;
  features: string[];
  benefits: string[];
  startingPrice?: string;
  estimatedTimeline?: string;
  status: ServiceStatus;
  isFeatured: boolean;
}

export interface BlogPostForm {
  title: string;
  excerpt?: string;
  content: string;
  category?: string;
  tags: string[];
  status: BlogPostStatus;
  publishedAt?: string;
  scheduledFor?: string;
}

// =====================================================
// API RESPONSE TYPES
// =====================================================

export interface AdminApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface AdminTableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
  render?: (value: any, row: any) => React.ReactNode;
}

export interface AdminTableProps<T> {
  data: T[];
  columns: AdminTableColumn[];
  loading?: boolean;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    onPageChange: (page: number) => void;
    onLimitChange: (limit: number) => void;
  };
  sorting?: {
    column: string;
    direction: 'asc' | 'desc';
    onSort: (column: string, direction: 'asc' | 'desc') => void;
  };
  selection?: {
    selectedRows: string[];
    onSelectionChange: (selectedRows: string[]) => void;
  };
  actions?: {
    onEdit?: (row: T) => void;
    onDelete?: (row: T) => void;
    onView?: (row: T) => void;
    customActions?: Array<{
      label: string;
      icon?: React.ReactNode;
      onClick: (row: T) => void;
      variant?: 'primary' | 'secondary' | 'danger';
    }>;
  };
}

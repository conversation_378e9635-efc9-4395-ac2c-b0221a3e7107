'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import AdminProtectedRoute from '@/components/admin/auth/AdminProtectedRoute';
import AdminLayout from '@/components/admin/layout/AdminLayout';
import ServiceForm from '@/components/admin/services/ServiceForm';
import { ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';
import { AdminService } from '@/lib/types/admin';

export default function NewServicePage() {
  const router = useRouter();

  const handleSave = (service: AdminService) => {
    router.push('/admin/services');
  };

  const handleCancel = () => {
    router.push('/admin/services');
  };

  return (
    <AdminProtectedRoute requiredPermission={ADMIN_PERMISSIONS.SERVICES_CREATE}>
      <AdminLayout 
        title="New Service" 
        subtitle="Create a new service offering"
      >
        <ServiceForm onSave={handleSave} onCancel={handleCancel} />
      </AdminLayout>
    </AdminProtectedRoute>
  );
}

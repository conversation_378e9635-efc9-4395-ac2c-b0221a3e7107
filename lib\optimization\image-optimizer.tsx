/**
 * Image Optimization Utilities
 * Handles image compression, resizing, and format conversion
 */

import React from 'react';

interface ImageOptimizationOptions {
  quality?: number; // 0-1, default 0.8
  maxWidth?: number;
  maxHeight?: number;
  format?: 'webp' | 'jpeg' | 'png';
  progressive?: boolean;
  blur?: number; // For placeholder generation
}

interface OptimizedImage {
  blob: Blob;
  url: string;
  width: number;
  height: number;
  size: number;
  format: string;
}

interface ImageDimensions {
  width: number;
  height: number;
}

class ImageOptimizer {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;

  constructor() {
    if (typeof window !== 'undefined') {
      this.canvas = document.createElement('canvas');
      this.ctx = this.canvas.getContext('2d')!;
    }
  }

  /**
   * Optimize image file
   */
  async optimizeImage(
    file: File,
    options: ImageOptimizationOptions = {}
  ): Promise<OptimizedImage> {
    const {
      quality = 0.8,
      maxWidth = 1920,
      maxHeight = 1080,
      format = 'webp',
      progressive = true,
    } = options;

    return new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        try {
          const dimensions = this.calculateOptimalDimensions(
            img.width,
            img.height,
            maxWidth,
            maxHeight
          );

          this.canvas.width = dimensions.width;
          this.canvas.height = dimensions.height;

          // Clear canvas
          this.ctx.clearRect(0, 0, dimensions.width, dimensions.height);

          // Draw image with new dimensions
          this.ctx.drawImage(img, 0, 0, dimensions.width, dimensions.height);

          // Convert to blob
          this.canvas.toBlob(
            (blob) => {
              if (!blob) {
                reject(new Error('Failed to optimize image'));
                return;
              }

              const optimized: OptimizedImage = {
                blob,
                url: URL.createObjectURL(blob),
                width: dimensions.width,
                height: dimensions.height,
                size: blob.size,
                format: blob.type,
              };

              resolve(optimized);
            },
            this.getMimeType(format),
            quality
          );
        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => {
        reject(new Error('Failed to load image'));
      };

      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Generate image placeholder (blurred, low quality)
   */
  async generatePlaceholder(
    file: File,
    options: { width?: number; height?: number; blur?: number } = {}
  ): Promise<string> {
    const { width = 40, height = 40, blur = 4 } = options;

    const optimized = await this.optimizeImage(file, {
      maxWidth: width,
      maxHeight: height,
      quality: 0.1,
      format: 'jpeg',
    });

    // Apply blur effect
    this.canvas.width = optimized.width;
    this.canvas.height = optimized.height;
    
    const img = new Image();
    img.src = optimized.url;
    
    return new Promise((resolve) => {
      img.onload = () => {
        this.ctx.filter = `blur(${blur}px)`;
        this.ctx.drawImage(img, 0, 0);
        
        const placeholderUrl = this.canvas.toDataURL('image/jpeg', 0.1);
        resolve(placeholderUrl);
      };
    });
  }

  /**
   * Calculate optimal dimensions while maintaining aspect ratio
   */
  private calculateOptimalDimensions(
    originalWidth: number,
    originalHeight: number,
    maxWidth: number,
    maxHeight: number
  ): ImageDimensions {
    let { width, height } = { width: originalWidth, height: originalHeight };

    // Calculate scaling factor
    const widthRatio = maxWidth / width;
    const heightRatio = maxHeight / height;
    const scalingFactor = Math.min(widthRatio, heightRatio, 1);

    width = Math.floor(width * scalingFactor);
    height = Math.floor(height * scalingFactor);

    return { width, height };
  }

  /**
   * Get MIME type for format
   */
  private getMimeType(format: string): string {
    const mimeTypes: Record<string, string> = {
      webp: 'image/webp',
      jpeg: 'image/jpeg',
      png: 'image/png',
    };
    return mimeTypes[format] || 'image/jpeg';
  }

  /**
   * Check if WebP is supported
   */
  static isWebPSupported(): boolean {
    if (typeof window === 'undefined') return false;
    
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    
    return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
  }

  /**
   * Get optimal format based on browser support
   */
  static getOptimalFormat(): 'webp' | 'jpeg' {
    return this.isWebPSupported() ? 'webp' : 'jpeg';
  }

  /**
   * Batch optimize multiple images
   */
  async optimizeBatch(
    files: File[],
    options: ImageOptimizationOptions = {}
  ): Promise<OptimizedImage[]> {
    const promises = files.map(file => this.optimizeImage(file, options));
    return Promise.all(promises);
  }

  /**
   * Get image metadata
   */
  async getImageMetadata(file: File): Promise<{
    width: number;
    height: number;
    size: number;
    type: string;
    aspectRatio: number;
  }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        resolve({
          width: img.width,
          height: img.height,
          size: file.size,
          type: file.type,
          aspectRatio: img.width / img.height,
        });
      };

      img.onerror = () => {
        reject(new Error('Failed to load image metadata'));
      };

      img.src = URL.createObjectURL(file);
    });
  }
}

// Utility functions
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

export function calculateCompressionRatio(originalSize: number, compressedSize: number): number {
  return ((originalSize - compressedSize) / originalSize) * 100;
}

export function isImageFile(file: File): boolean {
  return file.type.startsWith('image/');
}

export function getSupportedImageFormats(): string[] {
  const formats = ['image/jpeg', 'image/png', 'image/gif'];
  
  if (ImageOptimizer.isWebPSupported()) {
    formats.push('image/webp');
  }
  
  return formats;
}

// React hook for image optimization
export function useImageOptimizer() {
  const [optimizer] = React.useState(() => new ImageOptimizer());
  const [isOptimizing, setIsOptimizing] = React.useState(false);

  const optimizeImage = React.useCallback(
    async (file: File, options?: ImageOptimizationOptions) => {
      setIsOptimizing(true);
      try {
        const result = await optimizer.optimizeImage(file, options);
        return result;
      } finally {
        setIsOptimizing(false);
      }
    },
    [optimizer]
  );

  const generatePlaceholder = React.useCallback(
    async (file: File, options?: { width?: number; height?: number; blur?: number }) => {
      return optimizer.generatePlaceholder(file, options);
    },
    [optimizer]
  );

  const getMetadata = React.useCallback(
    async (file: File) => {
      return optimizer.getImageMetadata(file);
    },
    [optimizer]
  );

  return {
    optimizeImage,
    generatePlaceholder,
    getMetadata,
    isOptimizing,
    isWebPSupported: ImageOptimizer.isWebPSupported(),
    optimalFormat: ImageOptimizer.getOptimalFormat(),
  };
}

// Progressive image loading component
interface ProgressiveImageProps {
  src: string;
  placeholder?: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  onLoad?: () => void;
  onError?: () => void;
}

export function ProgressiveImage({
  src,
  placeholder,
  alt,
  className = '',
  width,
  height,
  onLoad,
  onError,
}: ProgressiveImageProps) {
  const [isLoaded, setIsLoaded] = React.useState(false);
  const [hasError, setHasError] = React.useState(false);

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* Placeholder */}
      {placeholder && !isLoaded && !hasError && (
        <img
          src={placeholder}
          alt=""
          className="absolute inset-0 w-full h-full object-cover filter blur-sm scale-110"
          aria-hidden="true"
        />
      )}
      
      {/* Main image */}
      <img
        src={src}
        alt={alt}
        width={width}
        height={height}
        className={`w-full h-full object-cover transition-opacity duration-300 ${
          isLoaded ? 'opacity-100' : 'opacity-0'
        }`}
        onLoad={handleLoad}
        onError={handleError}
        loading="lazy"
      />
      
      {/* Error state */}
      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="text-center text-gray-500">
            <svg className="w-12 h-12 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
            </svg>
            <p className="text-sm">Failed to load image</p>
          </div>
        </div>
      )}
    </div>
  );
}

export default ImageOptimizer;

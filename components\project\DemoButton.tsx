'use client';

import React, { useState } from 'react';
import { ExternalLink, Loader2, AlertCircle } from 'lucide-react';
import { DemoButtonProps } from '@/lib/types';

const DemoButton: React.FC<DemoButtonProps> = ({
  demoUrl,
  templateName,
  variant = 'primary',
  size = 'md',
  className = ''
}) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [hasError, setHasError] = useState<boolean>(false);

  const handleDemoClick = async () => {
    if (isLoading) return;

    setIsLoading(true);
    setHasError(false);

    try {
      // Simulate a brief loading state for better UX
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Check if URL is valid
      if (!demoUrl || !isValidUrl(demoUrl)) {
        throw new Error('Invalid demo URL');
      }

      // Open demo in new tab
      const newWindow = window.open(demoUrl, '_blank', 'noopener,noreferrer');
      
      if (!newWindow) {
        throw new Error('Popup blocked');
      }

    } catch (error) {
      console.error('Error opening demo:', error);
      setHasError(true);
      
      // Reset error state after 3 seconds
      setTimeout(() => {
        setHasError(false);
      }, 3000);
    } finally {
      setIsLoading(false);
    }
  };

  const isValidUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  // Size variants
  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg'
  };

  // Icon sizes
  const iconSizes = {
    sm: 14,
    md: 16,
    lg: 18
  };

  // Variant styles
  const variantClasses = {
    primary: `
      bg-black text-white 
      hover:bg-gray-800 
      disabled:bg-gray-400 disabled:cursor-not-allowed
      shadow-lg hover:shadow-xl
    `,
    secondary: `
      border-2 border-gray-300 text-gray-700 bg-white
      hover:border-gray-400 hover:bg-gray-50
      disabled:border-gray-200 disabled:text-gray-400 disabled:cursor-not-allowed
      shadow-md hover:shadow-lg
    `
  };

  const baseClasses = `
    inline-flex items-center justify-center gap-2 
    rounded-lg font-medium font-jost
    transition-all duration-300 
    transform hover:-translate-y-1
    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500
    relative overflow-hidden
    ${sizeClasses[size]}
    ${variantClasses[variant]}
    ${className}
  `;

  return (
    <button
      onClick={handleDemoClick}
      disabled={isLoading || hasError}
      className={baseClasses}
      aria-label={`View ${templateName} demo`}
      title={hasError ? 'Failed to open demo. Click to retry.' : `Open ${templateName} demo in new tab`}
    >
      {/* Loading State */}
      {isLoading && (
        <>
          <Loader2 size={iconSizes[size]} className="animate-spin" />
          <span>Opening...</span>
        </>
      )}

      {/* Error State */}
      {hasError && !isLoading && (
        <>
          <AlertCircle size={iconSizes[size]} className="text-red-500" />
          <span>Retry</span>
        </>
      )}

      {/* Normal State */}
      {!isLoading && !hasError && (
        <>
          <ExternalLink 
            size={iconSizes[size]} 
            className="transition-transform duration-300 group-hover:scale-110" 
          />
          <span>View Demo</span>
        </>
      )}

      {/* Hover Effect Overlay for Primary Variant */}
      {variant === 'primary' && (
        <div className="absolute inset-0 bg-gray-800 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-500 ease-in-out -z-10" />
      )}
    </button>
  );
};

export default DemoButton;

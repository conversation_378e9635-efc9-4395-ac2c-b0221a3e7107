'use client';

import React, { useState } from 'react';
import { MapPin, Clock, DollarSign, ArrowR<PERSON>, Briefcase, <PERSON>, Eye } from 'lucide-react';

interface Job {
  id: string;
  title: string;
  description: string;
  previewDescription?: string;
  employmentType: 'Full-time' | 'Part-time' | 'Contract';
  experienceLevel: 'Junior' | 'Mid' | 'Senior';
  locationType: 'Remote' | 'On-site' | 'Hybrid';
  compensationRange: string;
  requiredSkills: string[];
  isActive: boolean;
  applicationDeadline?: string;
}

interface JobListItemProps {
  job: Job;
  className?: string;
  onViewDetails?: (job: Job) => void;
}

const JobListItem: React.FC<JobListItemProps> = ({ 
  job, 
  className = '',
  onViewDetails 
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const getExperienceLevelColor = (level: string) => {
    switch (level) {
      case 'Junior':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Mid':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Senior':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getEmploymentTypeColor = (type: string) => {
    switch (type) {
      case 'Full-time':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Part-time':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Contract':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getLocationTypeColor = (type: string) => {
    switch (type) {
      case 'Remote':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'On-site':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Hybrid':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleApplyClick = () => {
    window.location.href = `/collaborate/apply/job?id=${job.id}`;
  };

  const handleViewDetails = () => {
    if (onViewDetails) {
      onViewDetails(job);
    }
  };

  const isClosingSoon = job.applicationDeadline && 
    new Date(job.applicationDeadline) < new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

  return (
    <div 
      className={`group relative bg-white rounded-xl shadow-md border border-gray-100 hover:shadow-lg transition-all duration-300 overflow-hidden hover:-translate-y-1 ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="p-6">
        <div className="flex items-start justify-between gap-6">
          {/* Left Section - Main Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <h3 className="text-lg font-bold text-black mb-2 font-jost group-hover:text-gray-800 transition-colors line-clamp-1">
                  {job.title}
                </h3>
                <div className="flex items-center gap-3 text-sm text-gray-600 mb-3 flex-wrap">
                  <div className={`px-2 py-1 rounded-full text-xs font-medium border ${getEmploymentTypeColor(job.employmentType)} font-jost`}>
                    {job.employmentType}
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium border ${getExperienceLevelColor(job.experienceLevel)} font-jost`}>
                    {job.experienceLevel}
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium border ${getLocationTypeColor(job.locationType)} font-jost`}>
                    <MapPin size={12} className="inline mr-1" />
                    {job.locationType}
                  </div>
                  {job.isActive && (
                    <div className="flex items-center gap-1 text-green-600">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-xs font-jost">Active</span>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-1 text-yellow-500">
                <Star size={14} fill="currentColor" />
                <span className="text-xs text-gray-600 font-jost">Featured</span>
              </div>
            </div>

            <p className="text-gray-600 text-sm mb-4 line-clamp-2 font-jost">
              {job.previewDescription || job.description}
            </p>

            {/* Skills Tags */}
            <div className="flex flex-wrap gap-2 mb-4">
              {job.requiredSkills.slice(0, 4).map((skill, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-blue-50 text-blue-700 rounded text-xs font-medium border border-blue-200 font-jost"
                >
                  {skill}
                </span>
              ))}
              {job.requiredSkills.length > 4 && (
                <span className="text-xs text-gray-500 font-jost">
                  +{job.requiredSkills.length - 4} more
                </span>
              )}
            </div>
          </div>

          {/* Right Section - Compensation and Actions */}
          <div className="flex flex-col items-end gap-4 min-w-0">
            {/* Compensation and Deadline */}
            <div className="text-right">
              <div className="flex items-center gap-1 text-green-600 mb-1">
                <DollarSign size={16} />
                <span className="text-lg font-bold font-jost">{job.compensationRange}</span>
              </div>
              <div className="text-xs text-gray-600 font-jost">
                {job.applicationDeadline ? 
                  `Deadline: ${new Date(job.applicationDeadline).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}` : 
                  'Open Application'
                }
              </div>
              {isClosingSoon && (
                <div className="text-xs text-red-600 font-medium font-jost mt-1">
                  Closing Soon!
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-2">
              <button
                onClick={handleViewDetails}
                className="flex items-center gap-1 px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:border-gray-400 hover:bg-gray-50 transition-all duration-300 text-sm font-medium font-jost"
              >
                <Eye size={14} />
                <span>Details</span>
              </button>
              <button
                onClick={handleApplyClick}
                className="flex items-center gap-2 bg-black text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-all duration-300 text-sm font-medium font-jost hover:shadow-md transform hover:-translate-y-0.5"
              >
                <span>Apply</span>
                <ArrowRight 
                  size={14} 
                  className={`transition-all duration-300 ${
                    isHovered ? 'translate-x-0.5 scale-110' : ''
                  }`}
                />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Hover Overlay Effect */}
      <div className={`absolute inset-0 bg-gradient-to-r from-green-500/5 to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none rounded-xl`} />
      
      {/* Urgency Badge */}
      {isClosingSoon && (
        <div className="absolute top-4 left-4">
          <div className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium font-jost">
            Closing Soon
          </div>
        </div>
      )}
    </div>
  );
};

export default JobListItem;

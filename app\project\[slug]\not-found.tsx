import Link from 'next/link';
import { ArrowLeft, Search } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function TemplateNotFound() {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1 flex items-center justify-center py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full text-center">
          {/* 404 Icon */}
          <div className="mb-8">
            <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center">
              <Search size={32} className="text-gray-400" />
            </div>
          </div>

          {/* Error Message */}
          <h1 className="text-4xl font-bold text-gray-900 mb-4 font-jost">
            Template Not Found
          </h1>
          
          <p className="text-lg text-gray-600 mb-8 font-jost">
            Sorry, we couldn't find the template you're looking for. It may have been moved or doesn't exist.
          </p>

          {/* Action Buttons */}
          <div className="space-y-4">
            <Link
              href="/project"
              className="inline-flex items-center justify-center gap-2 w-full px-6 py-3 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors duration-200 font-medium font-jost"
            >
              <ArrowLeft size={16} />
              Back to Templates
            </Link>
            
            <Link
              href="/"
              className="inline-flex items-center justify-center w-full px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-lg hover:border-gray-400 hover:bg-gray-50 transition-colors duration-200 font-medium font-jost"
            >
              Go to Homepage
            </Link>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}

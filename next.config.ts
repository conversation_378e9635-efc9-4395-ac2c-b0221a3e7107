import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Disable ESLint during build for now
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Disable TypeScript checking during build for now
  typescript: {
    ignoreBuildErrors: true,
  },

  // Image optimization
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'logos-world.net',
        port: '',
        pathname: '/wp-content/uploads/**',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
        port: '',
        pathname: '/**',
      },
    ],
  },

  // Disable x-powered-by header
  poweredByHeader: false,
};

export default nextConfig;

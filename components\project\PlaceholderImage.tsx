'use client';

import React from 'react';

interface PlaceholderImageProps {
  width: number;
  height: number;
  text?: string;
  className?: string;
  bgColor?: string;
  textColor?: string;
}

const PlaceholderImage: React.FC<PlaceholderImageProps> = ({
  width,
  height,
  text,
  className = '',
  bgColor = '#f3f4f6',
  textColor = '#6b7280'
}) => {
  const displayText = text || `${width}×${height}`;
  
  // Calculate font size based on image dimensions
  const fontSize = Math.min(width, height) / 10;
  
  const svgContent = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="${bgColor}"/>
      <text x="50%" y="50%" font-family="system-ui, sans-serif" font-size="${fontSize}" 
            fill="${textColor}" text-anchor="middle" dominant-baseline="middle">
        ${displayText}
      </text>
    </svg>
  `;
  
  const dataUrl = `data:image/svg+xml;base64,${btoa(svgContent)}`;
  
  return (
    <img
      src={dataUrl}
      alt={text || 'Placeholder image'}
      width={width}
      height={height}
      className={className}
      style={{ objectFit: 'cover' }}
    />
  );
};

export default PlaceholderImage;

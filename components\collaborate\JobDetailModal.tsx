'use client';

import React from 'react';
import { X, MapPin, DollarSign, Clock, Briefcase, Star, Calendar, Users, CheckCircle, ArrowRight } from 'lucide-react';

interface Job {
  id: string;
  title: string;
  description: string;
  employmentType: 'Full-time' | 'Part-time' | 'Contract';
  experienceLevel: 'Junior' | 'Mid' | 'Senior';
  locationType: 'Remote' | 'On-site' | 'Hybrid';
  compensationRange: string;
  requiredSkills: string[];
  isActive: boolean;
  applicationDeadline?: string;
}

interface JobDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  job: Job | null;
}

const JobDetailModal: React.FC<JobDetailModalProps> = ({ isOpen, onClose, job }) => {
  if (!isOpen || !job) return null;

  const getExperienceLevelColor = (level: string) => {
    switch (level) {
      case 'Junior':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Mid':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Senior':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getEmploymentTypeColor = (type: string) => {
    switch (type) {
      case 'Full-time':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Part-time':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Contract':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getLocationTypeColor = (type: string) => {
    switch (type) {
      case 'Remote':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'On-site':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Hybrid':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleApplyClick = () => {
    window.location.href = `/collaborate/apply/job?id=${job.id}`;
  };

  const isClosingSoon = job.applicationDeadline && 
    new Date(job.applicationDeadline) < new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-gray-100 p-6 rounded-t-2xl">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h2 className="text-2xl font-bold text-black font-jost">{job.title}</h2>
                {job.isActive && (
                  <div className="flex items-center gap-1 text-green-600">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-sm font-jost">Active</span>
                  </div>
                )}
                {isClosingSoon && (
                  <div className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium font-jost">
                    Closing Soon
                  </div>
                )}
              </div>
              <div className="flex items-center gap-4 text-sm text-gray-600 flex-wrap">
                <div className={`px-3 py-1 rounded-full text-xs font-medium border ${getEmploymentTypeColor(job.employmentType)} font-jost`}>
                  {job.employmentType}
                </div>
                <div className={`px-3 py-1 rounded-full text-xs font-medium border ${getExperienceLevelColor(job.experienceLevel)} font-jost`}>
                  {job.experienceLevel}
                </div>
                <div className={`px-3 py-1 rounded-full text-xs font-medium border ${getLocationTypeColor(job.locationType)} font-jost`}>
                  <MapPin size={12} className="inline mr-1" />
                  {job.locationType}
                </div>
                <div className="flex items-center gap-1 text-yellow-500">
                  <Star size={16} fill="currentColor" />
                  <span className="text-sm text-gray-600 font-jost">Featured Position</span>
                </div>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X size={24} className="text-gray-500" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-8">
          {/* Job Overview */}
          <div>
            <h3 className="text-lg font-semibold text-black mb-4 font-jost">Job Description</h3>
            <p className="text-gray-700 leading-relaxed font-jost">{job.description}</p>
          </div>

          {/* Compensation and Details */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-green-50 rounded-lg p-6 text-center">
              <DollarSign size={24} className="text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-black font-jost">
                {job.compensationRange}
              </div>
              <div className="text-sm text-gray-600 font-jost">Compensation Range</div>
            </div>

            <div className="bg-blue-50 rounded-lg p-6 text-center">
              <Calendar size={24} className="text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-black font-jost">
                {job.applicationDeadline ? 
                  new Date(job.applicationDeadline).toLocaleDateString() : 
                  'Open'
                }
              </div>
              <div className="text-sm text-gray-600 font-jost">Application Deadline</div>
            </div>

            <div className="bg-purple-50 rounded-lg p-6 text-center">
              <Briefcase size={24} className="text-purple-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-black font-jost">{job.employmentType}</div>
              <div className="text-sm text-gray-600 font-jost">Employment Type</div>
            </div>
          </div>

          {/* Required Skills */}
          <div>
            <h3 className="text-lg font-semibold text-black mb-4 font-jost">Required Skills & Qualifications</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {job.requiredSkills.map((skill, index) => (
                <div key={index} className="flex items-center gap-2">
                  <CheckCircle size={16} className="text-green-500" />
                  <span className="text-gray-700 font-jost">{skill}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Responsibilities */}
          <div>
            <h3 className="text-lg font-semibold text-black mb-4 font-jost">Key Responsibilities</h3>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <CheckCircle size={20} className="text-blue-500 mt-0.5" />
                <div>
                  <h4 className="font-medium text-black font-jost">Develop and maintain applications</h4>
                  <p className="text-gray-600 text-sm font-jost">Build scalable and efficient software solutions using modern technologies.</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle size={20} className="text-blue-500 mt-0.5" />
                <div>
                  <h4 className="font-medium text-black font-jost">Collaborate with cross-functional teams</h4>
                  <p className="text-gray-600 text-sm font-jost">Work closely with designers, product managers, and other developers.</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle size={20} className="text-blue-500 mt-0.5" />
                <div>
                  <h4 className="font-medium text-black font-jost">Code review and mentoring</h4>
                  <p className="text-gray-600 text-sm font-jost">Review code, provide feedback, and mentor junior team members.</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle size={20} className="text-blue-500 mt-0.5" />
                <div>
                  <h4 className="font-medium text-black font-jost">Participate in technical discussions</h4>
                  <p className="text-gray-600 text-sm font-jost">Contribute to architectural decisions and technical planning.</p>
                </div>
              </div>
            </div>
          </div>

          {/* Benefits */}
          <div>
            <h3 className="text-lg font-semibold text-black mb-4 font-jost">What We Offer</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3 p-4 bg-blue-50 rounded-lg">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <DollarSign size={16} className="text-white" />
                </div>
                <div>
                  <h4 className="font-medium text-black font-jost">Competitive Salary</h4>
                  <p className="text-sm text-gray-600 font-jost">Market-competitive compensation package</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-4 bg-green-50 rounded-lg">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <Clock size={16} className="text-white" />
                </div>
                <div>
                  <h4 className="font-medium text-black font-jost">Flexible Hours</h4>
                  <p className="text-sm text-gray-600 font-jost">Work-life balance with flexible scheduling</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-4 bg-purple-50 rounded-lg">
                <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                  <Users size={16} className="text-white" />
                </div>
                <div>
                  <h4 className="font-medium text-black font-jost">Great Team</h4>
                  <p className="text-sm text-gray-600 font-jost">Work with talented and supportive colleagues</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-4 bg-yellow-50 rounded-lg">
                <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                  <Star size={16} className="text-white" />
                </div>
                <div>
                  <h4 className="font-medium text-black font-jost">Growth Opportunities</h4>
                  <p className="text-sm text-gray-600 font-jost">Career development and learning opportunities</p>
                </div>
              </div>
            </div>
          </div>

          {/* Company Culture */}
          <div>
            <h3 className="text-lg font-semibold text-black mb-4 font-jost">About iREME Soft Hub</h3>
            <p className="text-gray-700 leading-relaxed font-jost">
              At iREME Soft Hub, we're building the future of technology in Rwanda and beyond. We believe in creating 
              innovative solutions that make a real impact on people's lives. Our team is passionate, diverse, and 
              committed to excellence. We foster a culture of continuous learning, collaboration, and innovation.
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="sticky bottom-0 bg-white border-t border-gray-100 p-6 rounded-b-2xl">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600 font-jost">
              Ready to join our team?
            </div>
            <button
              onClick={handleApplyClick}
              className="flex items-center gap-2 bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-all duration-300 font-medium font-jost hover:shadow-lg transform hover:-translate-y-0.5"
            >
              <span>Apply Now</span>
              <ArrowRight size={18} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default JobDetailModal;

'use client';

import React, { useState, useEffect } from 'react';
import { AdminCard } from '@/components/admin/ui/AdminCard';
import AdminButton from '@/components/admin/ui/AdminButton';
import { AdminTableSkeleton } from '@/components/admin/ui/AdminLoading';
import { useAdminAuth, ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';
import { AdminPermissionWrapper } from '@/components/admin/auth/AdminProtectedRoute';
import { AdminCustomer, CustomerType } from '@/lib/types/admin';
import {
  Search,
  Plus,
  Eye,
  Edit,
  Building,
  User,
  Mail,
  Phone,
  Globe,
  MapPin,
  DollarSign,
  Calendar,
  Users,
  Star,
  AlertTriangle,
  CheckCircle,
  Clock,
} from 'lucide-react';

interface CustomerListProps {
  onView?: (customer: AdminCustomer) => void;
  onEdit?: (customer: AdminCustomer) => void;
}

export default function CustomerList({ onView, onEdit }: CustomerListProps) {
  const { hasPermission } = useAdminAuth();
  const [customers, setCustomers] = useState<AdminCustomer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [managerFilter, setManagerFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const itemsPerPage = 10;

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockCustomers: AdminCustomer[] = [
      {
        id: '1',
        created_at: '2024-01-15T10:00:00Z',
        updated_at: '2024-01-20T15:30:00Z',
        company_name: 'Tech Innovations Inc.',
        contact_person: 'John Smith',
        email: '<EMAIL>',
        phone: '+****************',
        website: 'https://techinnovations.com',
        address_line_1: '123 Business Ave',
        address_line_2: 'Suite 100',
        city: 'San Francisco',
        state: 'CA',
        postal_code: '94105',
        country: 'United States',
        industry: 'Technology',
        company_size: '50-100 employees',
        annual_revenue_range: '$5M - $10M',
        customer_type: 'active',
        acquisition_date: '2024-01-15',
        acquisition_source: 'Website',
        account_manager_id: 'admin-1',
        total_project_value: 45000,
        total_paid: 35000,
        outstanding_balance: 10000,
        preferred_contact_method: 'email',
        communication_frequency: 'weekly',
        notes: 'Great client, always pays on time. Interested in expanding their platform.',
        tags: ['high-value', 'tech', 'recurring'],
        created_by: 'admin-1',
        updated_by: 'admin-1',
      },
      {
        id: '2',
        created_at: '2024-01-10T14:20:00Z',
        updated_at: '2024-01-18T11:45:00Z',
        company_name: 'Creative Studio LLC',
        contact_person: 'Sarah Johnson',
        email: '<EMAIL>',
        phone: '+****************',
        website: 'https://creativestudio.com',
        address_line_1: '456 Design Street',
        city: 'New York',
        state: 'NY',
        postal_code: '10001',
        country: 'United States',
        industry: 'Design & Marketing',
        company_size: '10-25 employees',
        annual_revenue_range: '$1M - $5M',
        customer_type: 'prospect',
        acquisition_date: '2024-01-10',
        acquisition_source: 'Referral',
        account_manager_id: 'admin-2',
        total_project_value: 15000,
        total_paid: 15000,
        outstanding_balance: 0,
        preferred_contact_method: 'phone',
        communication_frequency: 'bi-weekly',
        notes: 'Small but growing agency. Potential for long-term partnership.',
        tags: ['design', 'small-business', 'referral'],
        created_by: 'admin-2',
        updated_by: 'admin-2',
      },
    ];

    setTimeout(() => {
      setCustomers(mockCustomers);
      setLoading(false);
      setTotalPages(1);
    }, 1000);
  }, [searchQuery, typeFilter, managerFilter, currentPage]);

  const getCustomerTypeBadge = (type: CustomerType) => {
    const badges = {
      prospect: { bg: 'bg-blue-100', text: 'text-blue-800', icon: <Clock className="h-3 w-3" /> },
      active: { bg: 'bg-green-100', text: 'text-green-800', icon: <CheckCircle className="h-3 w-3" /> },
      inactive: { bg: 'bg-gray-100', text: 'text-gray-800', icon: <AlertTriangle className="h-3 w-3" /> },
      former: { bg: 'bg-red-100', text: 'text-red-800', icon: <AlertTriangle className="h-3 w-3" /> },
    };
    return badges[type] || badges.prospect;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getHealthScore = (customer: AdminCustomer) => {
    // Simple health score calculation based on payment history and engagement
    let score = 100;
    
    if (customer.outstanding_balance > 0) {
      score -= 20;
    }
    
    if (customer.customer_type === 'inactive') {
      score -= 30;
    } else if (customer.customer_type === 'former') {
      score -= 50;
    }
    
    // Add points for high value customers
    if (customer.total_project_value > 30000) {
      score += 10;
    }
    
    return Math.max(0, Math.min(100, score));
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    if (score >= 40) return 'text-orange-600';
    return 'text-red-600';
  };

  if (loading) {
    return <AdminTableSkeleton rows={5} columns={6} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 font-jost">Customers</h1>
          <p className="text-gray-600 font-jost">Manage your customer relationships and accounts</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.CUSTOMERS_CREATE}>
            <AdminButton icon={<Plus className="h-4 w-4" />}>
              Add Customer
            </AdminButton>
          </AdminPermissionWrapper>
        </div>
      </div>

      {/* Filters */}
      <AdminCard>
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search by company, contact person, or email..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
              />
            </div>
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-3">
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
            >
              <option value="all">All Types</option>
              <option value="prospect">Prospects</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="former">Former</option>
            </select>

            <select
              value={managerFilter}
              onChange={(e) => setManagerFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
            >
              <option value="all">All Managers</option>
              <option value="me">My Customers</option>
              <option value="unassigned">Unassigned</option>
            </select>
          </div>
        </div>
      </AdminCard>

      {/* Customers List */}
      {customers.length > 0 ? (
        <div className="space-y-4">
          {customers.map((customer) => {
            const typeBadge = getCustomerTypeBadge(customer.customer_type);
            const healthScore = getHealthScore(customer);
            
            return (
              <AdminCard key={customer.id} className="hover:shadow-md transition-shadow duration-200">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    {/* Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-start space-x-4">
                        <div className="h-12 w-12 bg-gray-100 rounded-full flex items-center justify-center">
                          <Building className="h-6 w-6 text-gray-600" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 font-jost">
                            {customer.company_name || customer.contact_person}
                          </h3>
                          <div className="flex items-center space-x-4 text-sm text-gray-600 font-jost">
                            <span className="flex items-center">
                              <User className="h-4 w-4 mr-1" />
                              {customer.contact_person}
                            </span>
                            <span className="flex items-center">
                              <Mail className="h-4 w-4 mr-1" />
                              {customer.email}
                            </span>
                            {customer.phone && (
                              <span className="flex items-center">
                                <Phone className="h-4 w-4 mr-1" />
                                {customer.phone}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-3">
                        {/* Health Score */}
                        <div className="text-center">
                          <div className={`text-lg font-bold ${getHealthScoreColor(healthScore)}`}>
                            {healthScore}
                          </div>
                          <div className="text-xs text-gray-500 font-jost">Health</div>
                        </div>

                        {/* Customer Type Badge */}
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${typeBadge.bg} ${typeBadge.text}`}>
                          {typeBadge.icon}
                          <span className="ml-1">{customer.customer_type}</span>
                        </span>
                      </div>
                    </div>

                    {/* Company Details */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <div className="flex items-center text-sm text-gray-600 font-jost mb-2">
                          <MapPin className="h-4 w-4 mr-1" />
                          <span>
                            {customer.city}, {customer.state} {customer.country}
                          </span>
                        </div>
                        {customer.website && (
                          <div className="flex items-center text-sm text-gray-600 font-jost mb-2">
                            <Globe className="h-4 w-4 mr-1" />
                            <a 
                              href={customer.website} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-500"
                            >
                              {customer.website}
                            </a>
                          </div>
                        )}
                        <div className="flex items-center text-sm text-gray-600 font-jost">
                          <Users className="h-4 w-4 mr-1" />
                          <span>{customer.industry} • {customer.company_size}</span>
                        </div>
                      </div>

                      <div>
                        <div className="flex items-center text-sm text-gray-600 font-jost mb-2">
                          <DollarSign className="h-4 w-4 mr-1" />
                          <span>Total Value: {formatCurrency(customer.total_project_value)}</span>
                        </div>
                        <div className="flex items-center text-sm text-gray-600 font-jost mb-2">
                          <CheckCircle className="h-4 w-4 mr-1" />
                          <span>Paid: {formatCurrency(customer.total_paid)}</span>
                        </div>
                        {customer.outstanding_balance > 0 && (
                          <div className="flex items-center text-sm text-red-600 font-jost">
                            <AlertTriangle className="h-4 w-4 mr-1" />
                            <span>Outstanding: {formatCurrency(customer.outstanding_balance)}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Tags */}
                    {customer.tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-4">
                        {customer.tags.map((tag, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    )}

                    {/* Notes */}
                    {customer.notes && (
                      <div className="mb-4">
                        <p className="text-sm text-gray-600 font-jost line-clamp-2">
                          {customer.notes}
                        </p>
                      </div>
                    )}

                    {/* Footer Info */}
                    <div className="flex items-center justify-between pt-3 border-t border-gray-200">
                      <div className="flex items-center text-sm text-gray-500 font-jost">
                        <Calendar className="h-4 w-4 mr-1" />
                        <span>Customer since {formatDate(customer.acquisition_date || customer.created_at)}</span>
                      </div>
                      <div className="text-sm text-gray-500 font-jost">
                        Contact: {customer.communication_frequency}
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2 ml-4">
                    <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.CUSTOMERS_VIEW}>
                      <button
                        onClick={() => onView && onView(customer)}
                        className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                        title="View Details"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                    </AdminPermissionWrapper>

                    <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.CUSTOMERS_EDIT}>
                      <button
                        onClick={() => onEdit && onEdit(customer)}
                        className="p-2 text-blue-600 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors"
                        title="Edit Customer"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                    </AdminPermissionWrapper>
                  </div>
                </div>
              </AdminCard>
            );
          })}
        </div>
      ) : (
        <AdminCard className="text-center py-12">
          <div className="mx-auto h-16 w-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <Building className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2 font-jost">No customers found</h3>
          <p className="text-gray-600 mb-6 font-jost">
            {searchQuery || typeFilter !== 'all' || managerFilter !== 'all'
              ? 'Try adjusting your filters to see more results.'
              : 'Add your first customer to get started.'}
          </p>
          <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.CUSTOMERS_CREATE}>
            <AdminButton icon={<Plus className="h-4 w-4" />}>
              Add Customer
            </AdminButton>
          </AdminPermissionWrapper>
        </AdminCard>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-700 font-jost">
            Showing page {currentPage} of {totalPages}
          </p>
          <div className="flex items-center space-x-2">
            <AdminButton
              variant="outline"
              size="sm"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(currentPage - 1)}
            >
              Previous
            </AdminButton>
            <AdminButton
              variant="outline"
              size="sm"
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage(currentPage + 1)}
            >
              Next
            </AdminButton>
          </div>
        </div>
      )}
    </div>
  );
}

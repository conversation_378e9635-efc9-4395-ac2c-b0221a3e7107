-- =====================================================
-- ADMIN DASHBOARD DATABASE SCHEMA
-- Professional-grade schema for iREME Soft Hub Admin Dashboard
-- =====================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm"; -- For full-text search
CREATE EXTENSION IF NOT EXISTS "unaccent"; -- For accent-insensitive search

-- =====================================================
-- ADMIN USERS & AUTHENTICATION
-- =====================================================

-- Admin users table with role-based access control
CREATE TABLE admin_users (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Authentication
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255), -- For custom auth if needed
  supabase_user_id UUID UNIQUE, -- Link to Supabase auth.users
  
  -- Profile Information
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  avatar_url VARCHAR(500),
  phone VARCHAR(50),
  
  -- Role & Permissions
  role VARCHAR(50) DEFAULT 'admin' CHECK (role IN ('super_admin', 'admin', 'editor', 'viewer')),
  permissions JSONB DEFAULT '{}', -- Granular permissions
  
  -- Status & Security
  is_active BOOLEAN DEFAULT TRUE,
  is_verified BOOLEAN DEFAULT FALSE,
  last_login_at TIMESTAMP WITH TIME ZONE,
  login_attempts INTEGER DEFAULT 0,
  locked_until TIMESTAMP WITH TIME ZONE,
  
  -- Two-Factor Authentication
  two_factor_enabled BOOLEAN DEFAULT FALSE,
  two_factor_secret VARCHAR(255),
  backup_codes TEXT[],
  
  -- Metadata
  created_by UUID REFERENCES admin_users(id),
  updated_by UUID REFERENCES admin_users(id)
);

-- Admin sessions for enhanced security tracking
CREATE TABLE admin_sessions (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  admin_user_id UUID NOT NULL REFERENCES admin_users(id) ON DELETE CASCADE,
  session_token VARCHAR(255) UNIQUE NOT NULL,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  is_active BOOLEAN DEFAULT TRUE
);

-- =====================================================
-- CONTENT MANAGEMENT SYSTEM
-- =====================================================

-- Templates management (enhanced from existing)
CREATE TABLE admin_templates (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Basic Information
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  short_description TEXT,
  full_description TEXT,
  
  -- Media
  hero_image_url VARCHAR(500),
  gallery_images JSONB DEFAULT '[]', -- Array of image URLs
  demo_url VARCHAR(500),
  source_code_url VARCHAR(500),
  
  -- Categorization
  category VARCHAR(100) NOT NULL,
  tags TEXT[],
  technologies TEXT[],
  features TEXT[],
  
  -- Pricing & Business
  base_price DECIMAL(10,2),
  custom_price DECIMAL(10,2),
  license_type VARCHAR(50) DEFAULT 'standard',
  
  -- SEO & Marketing
  meta_title VARCHAR(255),
  meta_description TEXT,
  keywords TEXT[],
  
  -- Status & Visibility
  status VARCHAR(50) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
  is_featured BOOLEAN DEFAULT FALSE,
  sort_order INTEGER DEFAULT 0,
  
  -- Analytics
  view_count INTEGER DEFAULT 0,
  download_count INTEGER DEFAULT 0,
  inquiry_count INTEGER DEFAULT 0,
  
  -- Metadata
  created_by UUID REFERENCES admin_users(id),
  updated_by UUID REFERENCES admin_users(id)
);

-- Services management
CREATE TABLE admin_services (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Basic Information
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  description TEXT NOT NULL,
  short_description VARCHAR(500),
  
  -- Service Details
  icon_name VARCHAR(100), -- Lucide icon name
  features TEXT[] NOT NULL,
  benefits TEXT[],
  process_steps JSONB DEFAULT '[]',
  
  -- Pricing
  starting_price VARCHAR(100),
  price_range_min DECIMAL(10,2),
  price_range_max DECIMAL(10,2),
  pricing_model VARCHAR(50) DEFAULT 'fixed', -- fixed, hourly, project
  
  -- Timeline & Delivery
  estimated_timeline VARCHAR(100),
  timeline_min_days INTEGER,
  timeline_max_days INTEGER,
  
  -- Packages/Tiers
  packages JSONB DEFAULT '[]', -- Array of service packages
  
  -- SEO & Marketing
  meta_title VARCHAR(255),
  meta_description TEXT,
  keywords TEXT[],
  
  -- Status & Visibility
  status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'coming_soon')),
  is_featured BOOLEAN DEFAULT FALSE,
  sort_order INTEGER DEFAULT 0,
  
  -- Analytics
  view_count INTEGER DEFAULT 0,
  inquiry_count INTEGER DEFAULT 0,
  
  -- Metadata
  created_by UUID REFERENCES admin_users(id),
  updated_by UUID REFERENCES admin_users(id)
);

-- Blog posts management
CREATE TABLE admin_blog_posts (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Content
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  excerpt TEXT,
  content TEXT NOT NULL,
  content_type VARCHAR(50) DEFAULT 'markdown', -- markdown, html, rich_text
  
  -- Media
  featured_image_url VARCHAR(500),
  gallery_images JSONB DEFAULT '[]',
  
  -- Categorization
  category VARCHAR(100),
  tags TEXT[],
  
  -- SEO
  meta_title VARCHAR(255),
  meta_description TEXT,
  keywords TEXT[],
  canonical_url VARCHAR(500),
  
  -- Publishing
  status VARCHAR(50) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'scheduled', 'archived')),
  published_at TIMESTAMP WITH TIME ZONE,
  scheduled_for TIMESTAMP WITH TIME ZONE,
  
  -- Reading & Engagement
  reading_time_minutes INTEGER,
  view_count INTEGER DEFAULT 0,
  like_count INTEGER DEFAULT 0,
  share_count INTEGER DEFAULT 0,
  
  -- Author
  author_id UUID REFERENCES admin_users(id),
  author_name VARCHAR(255),
  author_bio TEXT,
  author_avatar_url VARCHAR(500),
  
  -- Metadata
  created_by UUID REFERENCES admin_users(id),
  updated_by UUID REFERENCES admin_users(id)
);

-- Media library for centralized asset management
CREATE TABLE admin_media_library (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- File Information
  filename VARCHAR(255) NOT NULL,
  original_filename VARCHAR(255) NOT NULL,
  file_path VARCHAR(500) NOT NULL,
  file_url VARCHAR(500) NOT NULL,
  file_size BIGINT NOT NULL, -- in bytes
  mime_type VARCHAR(100) NOT NULL,
  file_extension VARCHAR(10),
  
  -- Image-specific metadata
  width INTEGER,
  height INTEGER,
  alt_text TEXT,
  caption TEXT,
  
  -- Organization
  folder VARCHAR(255) DEFAULT 'uploads',
  tags TEXT[],
  
  -- Usage tracking
  usage_count INTEGER DEFAULT 0,
  last_used_at TIMESTAMP WITH TIME ZONE,
  
  -- SEO
  seo_filename VARCHAR(255),
  
  -- Metadata
  uploaded_by UUID REFERENCES admin_users(id)
);

-- =====================================================
-- LEAD & CUSTOMER MANAGEMENT
-- =====================================================

-- Quote requests (enhanced from existing contact forms)
CREATE TABLE admin_quote_requests (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Contact Information
  full_name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  phone VARCHAR(50),
  company_name VARCHAR(255),
  website VARCHAR(500),

  -- Project Details
  project_type VARCHAR(100) NOT NULL,
  project_description TEXT NOT NULL,
  budget_range VARCHAR(100),
  budget_min DECIMAL(10,2),
  budget_max DECIMAL(10,2),
  timeline VARCHAR(100),
  timeline_urgency VARCHAR(50) DEFAULT 'normal',

  -- Requirements
  required_features TEXT[],
  technical_requirements TEXT,
  design_preferences TEXT,
  target_audience TEXT,

  -- Additional Information
  additional_services TEXT[],
  special_requirements TEXT,
  how_did_you_hear VARCHAR(100),

  -- Lead Management
  status VARCHAR(50) DEFAULT 'new' CHECK (status IN ('new', 'contacted', 'qualified', 'proposal_sent', 'negotiating', 'won', 'lost', 'on_hold')),
  priority VARCHAR(50) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  lead_score INTEGER DEFAULT 0,
  estimated_value DECIMAL(10,2),

  -- Follow-up & Communication
  assigned_to UUID REFERENCES admin_users(id),
  next_follow_up_date DATE,
  last_contact_date DATE,
  contact_attempts INTEGER DEFAULT 0,

  -- Source Tracking
  source VARCHAR(100) DEFAULT 'website',
  utm_source VARCHAR(100),
  utm_medium VARCHAR(100),
  utm_campaign VARCHAR(100),
  referrer_url VARCHAR(500),

  -- Metadata
  ip_address INET,
  user_agent TEXT,
  created_by UUID REFERENCES admin_users(id),
  updated_by UUID REFERENCES admin_users(id)
);

-- Contact form submissions
CREATE TABLE admin_contact_submissions (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Contact Information
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  phone VARCHAR(50),
  company VARCHAR(255),

  -- Message
  subject VARCHAR(255),
  message TEXT NOT NULL,
  inquiry_type VARCHAR(100) DEFAULT 'general',

  -- Status & Management
  status VARCHAR(50) DEFAULT 'new' CHECK (status IN ('new', 'read', 'replied', 'resolved', 'spam')),
  priority VARCHAR(50) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
  assigned_to UUID REFERENCES admin_users(id),

  -- Response
  response_sent BOOLEAN DEFAULT FALSE,
  response_date TIMESTAMP WITH TIME ZONE,
  response_by UUID REFERENCES admin_users(id),

  -- Source Tracking
  form_source VARCHAR(100) DEFAULT 'contact_page',
  page_url VARCHAR(500),

  -- Metadata
  ip_address INET,
  user_agent TEXT,
  created_by UUID REFERENCES admin_users(id),
  updated_by UUID REFERENCES admin_users(id)
);

-- Customer/Client management
CREATE TABLE admin_customers (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Basic Information
  company_name VARCHAR(255),
  contact_person VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  phone VARCHAR(50),
  website VARCHAR(500),

  -- Address
  address_line_1 VARCHAR(255),
  address_line_2 VARCHAR(255),
  city VARCHAR(100),
  state VARCHAR(100),
  postal_code VARCHAR(20),
  country VARCHAR(100),

  -- Business Information
  industry VARCHAR(100),
  company_size VARCHAR(50),
  annual_revenue_range VARCHAR(100),

  -- Relationship
  customer_type VARCHAR(50) DEFAULT 'prospect' CHECK (customer_type IN ('prospect', 'active', 'inactive', 'former')),
  acquisition_date DATE,
  acquisition_source VARCHAR(100),
  account_manager_id UUID REFERENCES admin_users(id),

  -- Financial
  total_project_value DECIMAL(12,2) DEFAULT 0,
  total_paid DECIMAL(12,2) DEFAULT 0,
  outstanding_balance DECIMAL(12,2) DEFAULT 0,

  -- Communication Preferences
  preferred_contact_method VARCHAR(50) DEFAULT 'email',
  communication_frequency VARCHAR(50) DEFAULT 'normal',

  -- Notes & Tags
  notes TEXT,
  tags TEXT[],

  -- Metadata
  created_by UUID REFERENCES admin_users(id),
  updated_by UUID REFERENCES admin_users(id)
);

-- =====================================================
-- ANALYTICS & REPORTING
-- =====================================================

-- Website analytics tracking
CREATE TABLE admin_analytics_events (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Event Information
  event_type VARCHAR(100) NOT NULL, -- page_view, button_click, form_submit, etc.
  event_name VARCHAR(255) NOT NULL,
  event_category VARCHAR(100),

  -- Page/Source Information
  page_url VARCHAR(500) NOT NULL,
  page_title VARCHAR(255),
  referrer_url VARCHAR(500),

  -- User Information
  session_id VARCHAR(255),
  user_id UUID, -- If logged in
  ip_address INET,
  user_agent TEXT,

  -- Geographic Data
  country VARCHAR(100),
  region VARCHAR(100),
  city VARCHAR(100),

  -- Device Information
  device_type VARCHAR(50), -- desktop, mobile, tablet
  browser VARCHAR(100),
  operating_system VARCHAR(100),
  screen_resolution VARCHAR(50),

  -- Custom Properties
  properties JSONB DEFAULT '{}',

  -- Performance Metrics
  page_load_time INTEGER, -- milliseconds
  time_on_page INTEGER -- seconds
);

-- Business metrics summary (daily aggregations)
CREATE TABLE admin_business_metrics (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  date DATE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Website Metrics
  total_page_views INTEGER DEFAULT 0,
  unique_visitors INTEGER DEFAULT 0,
  bounce_rate DECIMAL(5,2) DEFAULT 0,
  avg_session_duration INTEGER DEFAULT 0, -- seconds

  -- Lead Metrics
  new_quote_requests INTEGER DEFAULT 0,
  new_contact_submissions INTEGER DEFAULT 0,
  total_leads INTEGER DEFAULT 0,
  qualified_leads INTEGER DEFAULT 0,
  conversion_rate DECIMAL(5,2) DEFAULT 0,

  -- Content Metrics
  blog_views INTEGER DEFAULT 0,
  template_views INTEGER DEFAULT 0,
  service_page_views INTEGER DEFAULT 0,

  -- Business Metrics
  new_customers INTEGER DEFAULT 0,
  total_revenue DECIMAL(12,2) DEFAULT 0,
  avg_project_value DECIMAL(10,2) DEFAULT 0,

  -- Performance Metrics
  avg_page_load_time INTEGER DEFAULT 0, -- milliseconds
  server_uptime_percentage DECIMAL(5,2) DEFAULT 100,

  UNIQUE(date)
);

-- =====================================================
-- SYSTEM ADMINISTRATION
-- =====================================================

-- System settings and configuration
CREATE TABLE admin_system_settings (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Setting Information
  setting_key VARCHAR(255) UNIQUE NOT NULL,
  setting_value TEXT,
  setting_type VARCHAR(50) DEFAULT 'string', -- string, number, boolean, json

  -- Organization
  category VARCHAR(100) NOT NULL, -- general, email, seo, analytics, etc.
  subcategory VARCHAR(100),

  -- Metadata
  description TEXT,
  is_public BOOLEAN DEFAULT FALSE, -- Can be accessed by frontend
  is_encrypted BOOLEAN DEFAULT FALSE,

  -- Validation
  validation_rules JSONB DEFAULT '{}',

  -- Metadata
  updated_by UUID REFERENCES admin_users(id)
);

-- Audit log for all admin actions
CREATE TABLE admin_audit_logs (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- User Information
  admin_user_id UUID REFERENCES admin_users(id),
  user_email VARCHAR(255),
  user_name VARCHAR(255),

  -- Action Information
  action VARCHAR(100) NOT NULL, -- create, update, delete, login, logout, etc.
  resource_type VARCHAR(100) NOT NULL, -- template, service, blog_post, user, etc.
  resource_id UUID,
  resource_name VARCHAR(255),

  -- Details
  description TEXT,
  old_values JSONB,
  new_values JSONB,

  -- Context
  ip_address INET,
  user_agent TEXT,
  session_id VARCHAR(255),

  -- Metadata
  severity VARCHAR(50) DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'error', 'critical'))
);

-- Email templates for automated communications
CREATE TABLE admin_email_templates (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Template Information
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  description TEXT,

  -- Email Content
  subject VARCHAR(255) NOT NULL,
  html_content TEXT NOT NULL,
  text_content TEXT,

  -- Template Variables
  variables JSONB DEFAULT '[]', -- Array of variable names

  -- Usage
  template_type VARCHAR(100) NOT NULL, -- quote_response, welcome, follow_up, etc.
  is_active BOOLEAN DEFAULT TRUE,

  -- Metadata
  created_by UUID REFERENCES admin_users(id),
  updated_by UUID REFERENCES admin_users(id)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE OPTIMIZATION
-- =====================================================

-- Admin Users Indexes
CREATE INDEX idx_admin_users_email ON admin_users(email);
CREATE INDEX idx_admin_users_role ON admin_users(role);
CREATE INDEX idx_admin_users_active ON admin_users(is_active) WHERE is_active = true;
CREATE INDEX idx_admin_users_supabase_id ON admin_users(supabase_user_id);

-- Admin Sessions Indexes
CREATE INDEX idx_admin_sessions_user_id ON admin_sessions(admin_user_id);
CREATE INDEX idx_admin_sessions_token ON admin_sessions(session_token);
CREATE INDEX idx_admin_sessions_active ON admin_sessions(is_active, expires_at);

-- Templates Indexes
CREATE INDEX idx_admin_templates_slug ON admin_templates(slug);
CREATE INDEX idx_admin_templates_status ON admin_templates(status);
CREATE INDEX idx_admin_templates_category ON admin_templates(category);
CREATE INDEX idx_admin_templates_featured ON admin_templates(is_featured) WHERE is_featured = true;
CREATE INDEX idx_admin_templates_search ON admin_templates USING gin(to_tsvector('english', name || ' ' || short_description));

-- Services Indexes
CREATE INDEX idx_admin_services_slug ON admin_services(slug);
CREATE INDEX idx_admin_services_status ON admin_services(status);
CREATE INDEX idx_admin_services_featured ON admin_services(is_featured) WHERE is_featured = true;

-- Blog Posts Indexes
CREATE INDEX idx_admin_blog_posts_slug ON admin_blog_posts(slug);
CREATE INDEX idx_admin_blog_posts_status ON admin_blog_posts(status);
CREATE INDEX idx_admin_blog_posts_published ON admin_blog_posts(published_at) WHERE status = 'published';
CREATE INDEX idx_admin_blog_posts_author ON admin_blog_posts(author_id);
CREATE INDEX idx_admin_blog_posts_search ON admin_blog_posts USING gin(to_tsvector('english', title || ' ' || excerpt || ' ' || content));

-- Media Library Indexes
CREATE INDEX idx_admin_media_library_folder ON admin_media_library(folder);
CREATE INDEX idx_admin_media_library_type ON admin_media_library(mime_type);
CREATE INDEX idx_admin_media_library_uploaded_by ON admin_media_library(uploaded_by);

-- Quote Requests Indexes
CREATE INDEX idx_admin_quote_requests_status ON admin_quote_requests(status);
CREATE INDEX idx_admin_quote_requests_priority ON admin_quote_requests(priority);
CREATE INDEX idx_admin_quote_requests_assigned ON admin_quote_requests(assigned_to);
CREATE INDEX idx_admin_quote_requests_email ON admin_quote_requests(email);
CREATE INDEX idx_admin_quote_requests_created ON admin_quote_requests(created_at);
CREATE INDEX idx_admin_quote_requests_follow_up ON admin_quote_requests(next_follow_up_date) WHERE next_follow_up_date IS NOT NULL;

-- Contact Submissions Indexes
CREATE INDEX idx_admin_contact_submissions_status ON admin_contact_submissions(status);
CREATE INDEX idx_admin_contact_submissions_assigned ON admin_contact_submissions(assigned_to);
CREATE INDEX idx_admin_contact_submissions_created ON admin_contact_submissions(created_at);

-- Customers Indexes
CREATE INDEX idx_admin_customers_email ON admin_customers(email);
CREATE INDEX idx_admin_customers_type ON admin_customers(customer_type);
CREATE INDEX idx_admin_customers_manager ON admin_customers(account_manager_id);

-- Analytics Indexes
CREATE INDEX idx_admin_analytics_events_type ON admin_analytics_events(event_type);
CREATE INDEX idx_admin_analytics_events_created ON admin_analytics_events(created_at);
CREATE INDEX idx_admin_analytics_events_page ON admin_analytics_events(page_url);
CREATE INDEX idx_admin_analytics_events_session ON admin_analytics_events(session_id);

-- Business Metrics Indexes
CREATE INDEX idx_admin_business_metrics_date ON admin_business_metrics(date);

-- System Settings Indexes
CREATE INDEX idx_admin_system_settings_key ON admin_system_settings(setting_key);
CREATE INDEX idx_admin_system_settings_category ON admin_system_settings(category);

-- Audit Logs Indexes
CREATE INDEX idx_admin_audit_logs_user ON admin_audit_logs(admin_user_id);
CREATE INDEX idx_admin_audit_logs_action ON admin_audit_logs(action);
CREATE INDEX idx_admin_audit_logs_resource ON admin_audit_logs(resource_type, resource_id);
CREATE INDEX idx_admin_audit_logs_created ON admin_audit_logs(created_at);

-- Email Templates Indexes
CREATE INDEX idx_admin_email_templates_slug ON admin_email_templates(slug);
CREATE INDEX idx_admin_email_templates_type ON admin_email_templates(template_type);
CREATE INDEX idx_admin_email_templates_active ON admin_email_templates(is_active) WHERE is_active = true;

-- =====================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Create or update the updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to all relevant tables
CREATE TRIGGER update_admin_users_updated_at BEFORE UPDATE ON admin_users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_admin_templates_updated_at BEFORE UPDATE ON admin_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_admin_services_updated_at BEFORE UPDATE ON admin_services
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_admin_blog_posts_updated_at BEFORE UPDATE ON admin_blog_posts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_admin_media_library_updated_at BEFORE UPDATE ON admin_media_library
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_admin_quote_requests_updated_at BEFORE UPDATE ON admin_quote_requests
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_admin_contact_submissions_updated_at BEFORE UPDATE ON admin_contact_submissions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_admin_customers_updated_at BEFORE UPDATE ON admin_customers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_admin_system_settings_updated_at BEFORE UPDATE ON admin_system_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_admin_email_templates_updated_at BEFORE UPDATE ON admin_email_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on all admin tables
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_services ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_blog_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_media_library ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_quote_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_contact_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_analytics_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_business_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_system_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_email_templates ENABLE ROW LEVEL SECURITY;

-- Admin Users Policies
CREATE POLICY "Admin users can view all admin users" ON admin_users
    FOR SELECT USING (auth.jwt() ->> 'role' IN ('super_admin', 'admin'));

CREATE POLICY "Super admins can manage all admin users" ON admin_users
    FOR ALL USING (auth.jwt() ->> 'role' = 'super_admin');

CREATE POLICY "Admins can view their own profile" ON admin_users
    FOR SELECT USING (auth.uid()::text = supabase_user_id::text);

CREATE POLICY "Admins can update their own profile" ON admin_users
    FOR UPDATE USING (auth.uid()::text = supabase_user_id::text);

-- Admin Sessions Policies
CREATE POLICY "Users can manage their own sessions" ON admin_sessions
    FOR ALL USING (
        admin_user_id IN (
            SELECT id FROM admin_users WHERE supabase_user_id = auth.uid()
        )
    );

-- Content Management Policies (Templates, Services, Blog Posts)
CREATE POLICY "Authenticated admins can view all content" ON admin_templates
    FOR SELECT USING (auth.jwt() ->> 'role' IN ('super_admin', 'admin', 'editor', 'viewer'));

CREATE POLICY "Editors and above can manage templates" ON admin_templates
    FOR ALL USING (auth.jwt() ->> 'role' IN ('super_admin', 'admin', 'editor'));

CREATE POLICY "Authenticated admins can view all services" ON admin_services
    FOR SELECT USING (auth.jwt() ->> 'role' IN ('super_admin', 'admin', 'editor', 'viewer'));

CREATE POLICY "Editors and above can manage services" ON admin_services
    FOR ALL USING (auth.jwt() ->> 'role' IN ('super_admin', 'admin', 'editor'));

CREATE POLICY "Authenticated admins can view all blog posts" ON admin_blog_posts
    FOR SELECT USING (auth.jwt() ->> 'role' IN ('super_admin', 'admin', 'editor', 'viewer'));

CREATE POLICY "Editors and above can manage blog posts" ON admin_blog_posts
    FOR ALL USING (auth.jwt() ->> 'role' IN ('super_admin', 'admin', 'editor'));

-- Media Library Policies
CREATE POLICY "Authenticated admins can view media" ON admin_media_library
    FOR SELECT USING (auth.jwt() ->> 'role' IN ('super_admin', 'admin', 'editor', 'viewer'));

CREATE POLICY "Editors and above can manage media" ON admin_media_library
    FOR ALL USING (auth.jwt() ->> 'role' IN ('super_admin', 'admin', 'editor'));

-- Lead Management Policies
CREATE POLICY "Authenticated admins can view leads" ON admin_quote_requests
    FOR SELECT USING (auth.jwt() ->> 'role' IN ('super_admin', 'admin', 'editor', 'viewer'));

CREATE POLICY "Admins and above can manage leads" ON admin_quote_requests
    FOR ALL USING (auth.jwt() ->> 'role' IN ('super_admin', 'admin'));

CREATE POLICY "Authenticated admins can view contact submissions" ON admin_contact_submissions
    FOR SELECT USING (auth.jwt() ->> 'role' IN ('super_admin', 'admin', 'editor', 'viewer'));

CREATE POLICY "Admins and above can manage contact submissions" ON admin_contact_submissions
    FOR ALL USING (auth.jwt() ->> 'role' IN ('super_admin', 'admin'));

-- Customer Management Policies
CREATE POLICY "Authenticated admins can view customers" ON admin_customers
    FOR SELECT USING (auth.jwt() ->> 'role' IN ('super_admin', 'admin', 'editor', 'viewer'));

CREATE POLICY "Admins and above can manage customers" ON admin_customers
    FOR ALL USING (auth.jwt() ->> 'role' IN ('super_admin', 'admin'));

-- Analytics Policies
CREATE POLICY "Authenticated admins can view analytics" ON admin_analytics_events
    FOR SELECT USING (auth.jwt() ->> 'role' IN ('super_admin', 'admin', 'editor', 'viewer'));

CREATE POLICY "System can insert analytics events" ON admin_analytics_events
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Authenticated admins can view business metrics" ON admin_business_metrics
    FOR SELECT USING (auth.jwt() ->> 'role' IN ('super_admin', 'admin', 'editor', 'viewer'));

-- System Administration Policies
CREATE POLICY "Super admins can manage system settings" ON admin_system_settings
    FOR ALL USING (auth.jwt() ->> 'role' = 'super_admin');

CREATE POLICY "Admins can view system settings" ON admin_system_settings
    FOR SELECT USING (auth.jwt() ->> 'role' IN ('super_admin', 'admin'));

CREATE POLICY "Authenticated admins can view audit logs" ON admin_audit_logs
    FOR SELECT USING (auth.jwt() ->> 'role' IN ('super_admin', 'admin'));

CREATE POLICY "System can insert audit logs" ON admin_audit_logs
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Authenticated admins can view email templates" ON admin_email_templates
    FOR SELECT USING (auth.jwt() ->> 'role' IN ('super_admin', 'admin', 'editor', 'viewer'));

CREATE POLICY "Admins and above can manage email templates" ON admin_email_templates
    FOR ALL USING (auth.jwt() ->> 'role' IN ('super_admin', 'admin'));

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant necessary permissions to authenticated users
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Grant limited permissions to anonymous users (for analytics)
GRANT USAGE ON SCHEMA public TO anon;
GRANT INSERT ON admin_analytics_events TO anon;

-- =====================================================
-- INITIAL DATA SETUP
-- =====================================================

-- Insert default system settings
INSERT INTO admin_system_settings (setting_key, setting_value, setting_type, category, description) VALUES
('site_name', 'iREME Soft Hub', 'string', 'general', 'Website name'),
('site_description', 'Elite App Builders - Dominate the Market with Bold Tech', 'string', 'general', 'Website description'),
('contact_email', '<EMAIL>', 'string', 'general', 'Primary contact email'),
('contact_phone', '+****************', 'string', 'general', 'Primary contact phone'),
('business_hours', '9:00 AM - 6:00 PM EST', 'string', 'general', 'Business operating hours'),
('analytics_enabled', 'true', 'boolean', 'analytics', 'Enable analytics tracking'),
('email_notifications', 'true', 'boolean', 'email', 'Enable email notifications'),
('maintenance_mode', 'false', 'boolean', 'system', 'Enable maintenance mode');

-- Create initial super admin user (update with actual details)
-- Note: This should be updated with real admin details during deployment
INSERT INTO admin_users (
    email,
    first_name,
    last_name,
    role,
    is_active,
    is_verified
) VALUES (
    '<EMAIL>',
    'System',
    'Administrator',
    'super_admin',
    true,
    true
) ON CONFLICT (email) DO NOTHING;

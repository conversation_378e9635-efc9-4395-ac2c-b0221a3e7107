'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { AdminCard } from '@/components/admin/ui/AdminCard';
import AdminButton from '@/components/admin/ui/AdminButton';
import { AdminTableSkeleton } from '@/components/admin/ui/AdminLoading';
import { useAdminAuth, ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';
import { AdminPermissionWrapper } from '@/components/admin/auth/AdminProtectedRoute';
import { adminContent } from '@/lib/supabase/admin';
import { AdminService } from '@/lib/types/admin';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  MessageSquare,
  Star,
  DollarSign,
  Clock,
  Briefcase,
} from 'lucide-react';

interface ServiceListProps {
  onEdit?: (service: AdminService) => void;
  onDelete?: (service: AdminService) => void;
}

export default function ServiceList({ onEdit, onDelete }: ServiceListProps) {
  const { hasPermission } = useAdminAuth();
  const [services, setServices] = useState<AdminService[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const itemsPerPage = 10;

  // Fetch services
  const fetchServices = async () => {
    try {
      setLoading(true);
      const filters: any = {};
      
      if (statusFilter !== 'all') filters.status = statusFilter;
      if (searchQuery) filters.search = searchQuery;

      const response = await adminContent.getServices(currentPage, itemsPerPage, filters);
      setServices(response.data);
      setTotalPages(response.pagination.totalPages);
    } catch (error) {
      console.error('Error fetching services:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchServices();
  }, [currentPage, statusFilter, searchQuery]);

  const handleDelete = async (service: AdminService) => {
    if (!confirm(`Are you sure you want to delete "${service.title}"?`)) return;

    try {
      // await adminContent.deleteService(service.id);
      await fetchServices(); // Refresh list
      if (onDelete) onDelete(service);
    } catch (error) {
      console.error('Error deleting service:', error);
      alert('Failed to delete service. Please try again.');
    }
  };

  const getStatusBadge = (status: string) => {
    const badges = {
      active: 'bg-green-100 text-green-800',
      inactive: 'bg-gray-100 text-gray-800',
      coming_soon: 'bg-blue-100 text-blue-800',
    };
    return badges[status as keyof typeof badges] || badges.active;
  };

  if (loading) {
    return <AdminTableSkeleton rows={5} columns={5} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 font-jost">Services</h1>
          <p className="text-gray-600 font-jost">Manage your service offerings and packages</p>
        </div>
        
        <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.SERVICES_CREATE}>
          <Link href="/admin/services/new">
            <AdminButton icon={<Plus className="h-4 w-4" />}>
              Add Service
            </AdminButton>
          </Link>
        </AdminPermissionWrapper>
      </div>

      {/* Filters */}
      <AdminCard>
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search services..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
              />
            </div>
          </div>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="coming_soon">Coming Soon</option>
          </select>
        </div>
      </AdminCard>

      {/* Services List */}
      {services.length > 0 ? (
        <div className="space-y-4">
          {services.map((service) => (
            <AdminCard key={service.id} className="hover:shadow-md transition-shadow duration-200">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="h-12 w-12 bg-gray-100 rounded-lg flex items-center justify-center">
                        <Briefcase className="h-6 w-6 text-gray-600" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 font-jost">
                          {service.title}
                        </h3>
                        <p className="text-sm text-gray-600 font-jost">
                          {service.short_description}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {/* Status Badge */}
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusBadge(service.status)}`}>
                        {service.status.replace('_', ' ')}
                      </span>

                      {/* Featured Badge */}
                      {service.is_featured && (
                        <Star className="h-5 w-5 text-yellow-500 fill-current" />
                      )}
                    </div>
                  </div>

                  {/* Features */}
                  {service.features.length > 0 && (
                    <div className="mb-3">
                      <div className="flex flex-wrap gap-2">
                        {service.features.slice(0, 4).map((feature, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            {feature}
                          </span>
                        ))}
                        {service.features.length > 4 && (
                          <span className="text-xs text-gray-500 font-jost">
                            +{service.features.length - 4} more features
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Service Details */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600 font-jost">
                    <div className="flex items-center">
                      <DollarSign className="h-4 w-4 mr-1" />
                      <span>
                        {service.starting_price || 'Custom pricing'}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      <span>
                        {service.estimated_timeline || 'Varies'}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <Eye className="h-4 w-4 mr-1" />
                      <span>{service.view_count} views</span>
                    </div>
                    <div className="flex items-center">
                      <MessageSquare className="h-4 w-4 mr-1" />
                      <span>{service.inquiry_count} inquiries</span>
                    </div>
                  </div>

                  {/* Packages */}
                  {service.packages.length > 0 && (
                    <div className="mt-3 pt-3 border-t border-gray-200">
                      <p className="text-sm text-gray-600 font-jost mb-2">
                        Available packages: {service.packages.length}
                      </p>
                      <div className="flex flex-wrap gap-2">
                        {service.packages.slice(0, 3).map((pkg, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800"
                          >
                            {pkg.name} - ${pkg.price}
                          </span>
                        ))}
                        {service.packages.length > 3 && (
                          <span className="text-xs text-gray-500 font-jost">
                            +{service.packages.length - 3} more packages
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-2 ml-4">
                  <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.SERVICES_EDIT}>
                    <button
                      onClick={() => onEdit && onEdit(service)}
                      className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                  </AdminPermissionWrapper>

                  <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.SERVICES_DELETE}>
                    <button
                      onClick={() => handleDelete(service)}
                      className="p-2 text-red-600 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </AdminPermissionWrapper>
                </div>
              </div>
            </AdminCard>
          ))}
        </div>
      ) : (
        <AdminCard className="text-center py-12">
          <div className="mx-auto h-16 w-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <Briefcase className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2 font-jost">No services found</h3>
          <p className="text-gray-600 mb-6 font-jost">
            {searchQuery || statusFilter !== 'all'
              ? 'Try adjusting your filters to see more results.'
              : 'Get started by creating your first service.'}
          </p>
          <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.SERVICES_CREATE}>
            <Link href="/admin/services/new">
              <AdminButton icon={<Plus className="h-4 w-4" />}>
                Add Service
              </AdminButton>
            </Link>
          </AdminPermissionWrapper>
        </AdminCard>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-700 font-jost">
            Showing page {currentPage} of {totalPages}
          </p>
          <div className="flex items-center space-x-2">
            <AdminButton
              variant="outline"
              size="sm"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(currentPage - 1)}
            >
              Previous
            </AdminButton>
            <AdminButton
              variant="outline"
              size="sm"
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage(currentPage + 1)}
            >
              Next
            </AdminButton>
          </div>
        </div>
      )}
    </div>
  );
}

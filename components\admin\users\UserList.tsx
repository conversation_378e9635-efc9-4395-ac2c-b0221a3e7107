'use client';

import React, { useState, useEffect } from 'react';
import { AdminCard } from '@/components/admin/ui/AdminCard';
import AdminButton from '@/components/admin/ui/AdminButton';
import { AdminTableSkeleton } from '@/components/admin/ui/AdminLoading';
import { useAdminAuth, ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';
import { AdminPermissionWrapper } from '@/components/admin/auth/AdminProtectedRoute';
import { adminUsers } from '@/lib/supabase/admin';
import { AdminUser, AdminUserRole } from '@/lib/types/admin';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Shield,
  User,
  Mail,
  Calendar,
  CheckCircle,
  XCircle,
  AlertTriangle,
  MoreHorizontal,
  Key,
  Settings,
} from 'lucide-react';

interface UserListProps {
  onEdit?: (user: AdminUser) => void;
  onDelete?: (user: AdminUser) => void;
}

export default function UserList({ onEdit, onDelete }: UserListProps) {
  const { hasPermission, adminUser: currentUser } = useAdminAuth();
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const itemsPerPage = 10;

  // Fetch admin users
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const filters: any = {};
      
      if (roleFilter !== 'all') filters.role = roleFilter;
      if (statusFilter !== 'all') filters.is_active = statusFilter === 'active';
      if (searchQuery) filters.search = searchQuery;

      const response = await adminUsers.getUsers(currentPage, itemsPerPage, filters);
      setUsers(response.data);
      setTotalPages(response.pagination.totalPages);
    } catch (error) {
      console.error('Error fetching users:', error);
      
      // Mock data for development
      const mockUsers: AdminUser[] = [
        {
          id: 'admin-1',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-20T15:30:00Z',
          email: '<EMAIL>',
          supabase_user_id: 'supabase-1',
          first_name: 'System',
          last_name: 'Administrator',
          role: 'super_admin',
          is_active: true,
          is_verified: true,
          last_login_at: '2024-01-20T15:30:00Z',
          login_count: 145,
          failed_login_attempts: 0,
          locked_until: null,
          password_changed_at: '2024-01-01T00:00:00Z',
          two_factor_enabled: true,
          profile_image_url: null,
          phone: null,
          timezone: 'UTC',
          language: 'en',
          created_by: null,
          updated_by: null,
        },
        {
          id: 'admin-2',
          created_at: '2024-01-10T10:00:00Z',
          updated_at: '2024-01-18T12:00:00Z',
          email: '<EMAIL>',
          supabase_user_id: 'supabase-2',
          first_name: 'Content',
          last_name: 'Editor',
          role: 'editor',
          is_active: true,
          is_verified: true,
          last_login_at: '2024-01-18T12:00:00Z',
          login_count: 23,
          failed_login_attempts: 0,
          locked_until: null,
          password_changed_at: '2024-01-10T10:00:00Z',
          two_factor_enabled: false,
          profile_image_url: null,
          phone: '+****************',
          timezone: 'America/New_York',
          language: 'en',
          created_by: 'admin-1',
          updated_by: 'admin-1',
        },
      ];
      
      setUsers(mockUsers);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [currentPage, roleFilter, statusFilter, searchQuery]);

  const handleDelete = async (user: AdminUser) => {
    if (user.id === currentUser?.id) {
      alert('You cannot delete your own account.');
      return;
    }

    if (!confirm(`Are you sure you want to delete ${user.first_name} ${user.last_name}?`)) return;

    try {
      await adminUsers.deleteUser(user.id);
      await fetchUsers(); // Refresh list
      if (onDelete) onDelete(user);
    } catch (error) {
      console.error('Error deleting user:', error);
      alert('Failed to delete user. Please try again.');
    }
  };

  const handleToggleStatus = async (user: AdminUser) => {
    if (user.id === currentUser?.id) {
      alert('You cannot deactivate your own account.');
      return;
    }

    try {
      await adminUsers.updateUser(user.id, { is_active: !user.is_active });
      await fetchUsers(); // Refresh list
    } catch (error) {
      console.error('Error updating user status:', error);
      alert('Failed to update user status. Please try again.');
    }
  };

  const getRoleBadge = (role: AdminUserRole) => {
    const badges = {
      super_admin: { bg: 'bg-purple-100', text: 'text-purple-800', icon: <Shield className="h-3 w-3" /> },
      admin: { bg: 'bg-blue-100', text: 'text-blue-800', icon: <Shield className="h-3 w-3" /> },
      editor: { bg: 'bg-green-100', text: 'text-green-800', icon: <User className="h-3 w-3" /> },
      viewer: { bg: 'bg-gray-100', text: 'text-gray-800', icon: <User className="h-3 w-3" /> },
    };
    return badges[role] || badges.viewer;
  };

  const getStatusBadge = (isActive: boolean, isVerified: boolean) => {
    if (!isActive) {
      return { bg: 'bg-red-100', text: 'text-red-800', icon: <XCircle className="h-3 w-3" />, label: 'Inactive' };
    }
    if (!isVerified) {
      return { bg: 'bg-yellow-100', text: 'text-yellow-800', icon: <AlertTriangle className="h-3 w-3" />, label: 'Unverified' };
    }
    return { bg: 'bg-green-100', text: 'text-green-800', icon: <CheckCircle className="h-3 w-3" />, label: 'Active' };
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const roles: AdminUserRole[] = ['super_admin', 'admin', 'editor', 'viewer'];

  if (loading) {
    return <AdminTableSkeleton rows={5} columns={6} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 font-jost">User Management</h1>
          <p className="text-gray-600 font-jost">Manage admin users and their permissions</p>
        </div>
        
        <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.USERS_CREATE}>
          <AdminButton icon={<Plus className="h-4 w-4" />}>
            Add User
          </AdminButton>
        </AdminPermissionWrapper>
      </div>

      {/* Filters */}
      <AdminCard>
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search by name or email..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
              />
            </div>
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-3">
            <select
              value={roleFilter}
              onChange={(e) => setRoleFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
            >
              <option value="all">All Roles</option>
              {roles.map(role => (
                <option key={role} value={role}>
                  {role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </option>
              ))}
            </select>

            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>
      </AdminCard>

      {/* Users List */}
      {users.length > 0 ? (
        <div className="space-y-4">
          {users.map((user) => {
            const roleBadge = getRoleBadge(user.role);
            const statusBadge = getStatusBadge(user.is_active, user.is_verified);
            const isCurrentUser = user.id === currentUser?.id;
            
            return (
              <AdminCard key={user.id} className="hover:shadow-md transition-shadow duration-200">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    {/* Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-start space-x-4">
                        <div className="h-12 w-12 bg-gray-100 rounded-full flex items-center justify-center">
                          {user.profile_image_url ? (
                            <img
                              src={user.profile_image_url}
                              alt={`${user.first_name} ${user.last_name}`}
                              className="h-12 w-12 rounded-full object-cover"
                            />
                          ) : (
                            <span className="text-lg font-medium text-gray-600">
                              {user.first_name[0]}{user.last_name[0]}
                            </span>
                          )}
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 font-jost">
                            {user.first_name} {user.last_name}
                            {isCurrentUser && (
                              <span className="ml-2 text-sm text-blue-600 font-jost">(You)</span>
                            )}
                          </h3>
                          <div className="flex items-center space-x-4 text-sm text-gray-600 font-jost">
                            <span className="flex items-center">
                              <Mail className="h-4 w-4 mr-1" />
                              {user.email}
                            </span>
                            {user.phone && (
                              <span>{user.phone}</span>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        {/* Role Badge */}
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${roleBadge.bg} ${roleBadge.text}`}>
                          {roleBadge.icon}
                          <span className="ml-1">{user.role.replace('_', ' ')}</span>
                        </span>

                        {/* Status Badge */}
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusBadge.bg} ${statusBadge.text}`}>
                          {statusBadge.icon}
                          <span className="ml-1">{statusBadge.label}</span>
                        </span>
                      </div>
                    </div>

                    {/* User Details */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <p className="text-sm font-medium text-gray-900 font-jost">Last Login</p>
                        <p className="text-sm text-gray-600 font-jost">
                          {user.last_login_at ? formatDate(user.last_login_at) : 'Never'}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900 font-jost">Login Count</p>
                        <p className="text-sm text-gray-600 font-jost">{user.login_count} times</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900 font-jost">2FA Status</p>
                        <p className="text-sm text-gray-600 font-jost">
                          {user.two_factor_enabled ? (
                            <span className="text-green-600 flex items-center">
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Enabled
                            </span>
                          ) : (
                            <span className="text-red-600 flex items-center">
                              <XCircle className="h-4 w-4 mr-1" />
                              Disabled
                            </span>
                          )}
                        </p>
                      </div>
                    </div>

                    {/* Security Info */}
                    {user.failed_login_attempts > 0 && (
                      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div className="flex items-center">
                          <AlertTriangle className="h-4 w-4 text-yellow-600 mr-2" />
                          <span className="text-sm text-yellow-800 font-jost">
                            {user.failed_login_attempts} failed login attempt{user.failed_login_attempts > 1 ? 's' : ''}
                          </span>
                        </div>
                      </div>
                    )}

                    {/* Footer Info */}
                    <div className="flex items-center justify-between pt-3 border-t border-gray-200">
                      <div className="flex items-center text-sm text-gray-500 font-jost">
                        <Calendar className="h-4 w-4 mr-1" />
                        <span>Created {formatDate(user.created_at)}</span>
                      </div>
                      <div className="text-sm text-gray-500 font-jost">
                        Timezone: {user.timezone}
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2 ml-4">
                    <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.USERS_EDIT}>
                      <button
                        onClick={() => onEdit && onEdit(user)}
                        className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                        title="Edit User"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                    </AdminPermissionWrapper>

                    {!isCurrentUser && (
                      <>
                        <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.USERS_EDIT}>
                          <button
                            onClick={() => handleToggleStatus(user)}
                            className={`p-2 rounded-lg transition-colors ${
                              user.is_active
                                ? 'text-red-600 hover:text-red-500 hover:bg-red-50'
                                : 'text-green-600 hover:text-green-500 hover:bg-green-50'
                            }`}
                            title={user.is_active ? 'Deactivate User' : 'Activate User'}
                          >
                            {user.is_active ? <XCircle className="h-4 w-4" /> : <CheckCircle className="h-4 w-4" />}
                          </button>
                        </AdminPermissionWrapper>

                        <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.USERS_DELETE}>
                          <button
                            onClick={() => handleDelete(user)}
                            className="p-2 text-red-600 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                            title="Delete User"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </AdminPermissionWrapper>
                      </>
                    )}
                  </div>
                </div>
              </AdminCard>
            );
          })}
        </div>
      ) : (
        <AdminCard className="text-center py-12">
          <div className="mx-auto h-16 w-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <User className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2 font-jost">No users found</h3>
          <p className="text-gray-600 mb-6 font-jost">
            {searchQuery || roleFilter !== 'all' || statusFilter !== 'all'
              ? 'Try adjusting your filters to see more results.'
              : 'Add your first admin user to get started.'}
          </p>
          <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.USERS_CREATE}>
            <AdminButton icon={<Plus className="h-4 w-4" />}>
              Add User
            </AdminButton>
          </AdminPermissionWrapper>
        </AdminCard>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-700 font-jost">
            Showing page {currentPage} of {totalPages}
          </p>
          <div className="flex items-center space-x-2">
            <AdminButton
              variant="outline"
              size="sm"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(currentPage - 1)}
            >
              Previous
            </AdminButton>
            <AdminButton
              variant="outline"
              size="sm"
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage(currentPage + 1)}
            >
              Next
            </AdminButton>
          </div>
        </div>
      )}
    </div>
  );
}

import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function Blog() {
  const blogPosts = [
    {
      title: "The Future of Web Development",
      excerpt: "Exploring the latest trends and technologies shaping the future of web development.",
      date: "December 15, 2024",
      readTime: "5 min read"
    },
    {
      title: "Building Scalable Applications",
      excerpt: "Best practices for creating applications that can grow with your business.",
      date: "December 10, 2024",
      readTime: "7 min read"
    },
    {
      title: "Mobile-First Development",
      excerpt: "Why mobile-first approach is crucial for modern application development.",
      date: "December 5, 2024",
      readTime: "4 min read"
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1 py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h1 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              iREME Soft Hub Blog
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto font-jost">
              Insights, tutorials, and updates from our development team.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogPosts.map((post, index) => (
              <article key={index} className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <div className="h-48 bg-gradient-to-r from-green-400 via-blue-400 to-cyan-400"></div>
                <div className="p-6">
                  <div className="flex items-center text-sm text-gray-500 mb-3 font-jost">
                    <span>{post.date}</span>
                    <span className="mx-2">•</span>
                    <span>{post.readTime}</span>
                  </div>
                  <h2 className="text-xl font-semibold text-black mb-3 font-jost">
                    {post.title}
                  </h2>
                  <p className="text-gray-600 mb-4 font-jost">
                    {post.excerpt}
                  </p>
                  <button className="text-blue-600 hover:text-blue-800 font-medium transition-colors duration-300 font-jost">
                    Read More →
                  </button>
                </div>
              </article>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <button className="bg-black text-white px-8 py-3 rounded-full hover:bg-gray-800 transition-colors duration-300 font-jost">
              Load More Posts
            </button>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}

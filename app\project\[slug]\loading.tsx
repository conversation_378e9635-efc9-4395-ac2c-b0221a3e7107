import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function TemplateLoading() {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1 py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Breadcrumb Skeleton */}
          <div className="mb-8">
            <div className="h-5 bg-gray-200 animate-pulse rounded-lg w-32"></div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-8">
              {/* Template Header Skeleton */}
              <div>
                <div className="flex items-center gap-3 mb-4">
                  <div className="h-6 bg-gray-200 animate-pulse rounded-full w-20"></div>
                  <div className="h-4 bg-gray-200 animate-pulse rounded-lg w-32"></div>
                </div>
                
                <div className="space-y-4 mb-6">
                  <div className="h-12 bg-gray-200 animate-pulse rounded-lg"></div>
                  <div className="h-12 bg-gray-200 animate-pulse rounded-lg max-w-3/4"></div>
                </div>
                
                <div className="space-y-2">
                  <div className="h-6 bg-gray-200 animate-pulse rounded-lg"></div>
                  <div className="h-6 bg-gray-200 animate-pulse rounded-lg max-w-5/6"></div>
                </div>
              </div>

              {/* Image Gallery Skeleton */}
              <div className="space-y-6">
                {/* Main Image */}
                <div className="h-96 md:h-[500px] bg-gray-200 animate-pulse rounded-2xl"></div>
                
                {/* Thumbnails */}
                <div className="flex gap-3 overflow-x-auto pb-2">
                  {Array.from({ length: 5 }).map((_, index) => (
                    <div
                      key={index}
                      className="flex-shrink-0 w-20 h-20 bg-gray-200 animate-pulse rounded-lg"
                    ></div>
                  ))}
                </div>
              </div>

              {/* Description Skeleton */}
              <div>
                <div className="h-8 bg-gray-200 animate-pulse rounded-lg w-64 mb-4"></div>
                <div className="space-y-3">
                  <div className="h-5 bg-gray-200 animate-pulse rounded-lg"></div>
                  <div className="h-5 bg-gray-200 animate-pulse rounded-lg"></div>
                  <div className="h-5 bg-gray-200 animate-pulse rounded-lg max-w-4/5"></div>
                  <div className="h-5 bg-gray-200 animate-pulse rounded-lg max-w-3/4"></div>
                </div>
              </div>

              {/* Features Skeleton */}
              <div>
                <div className="h-8 bg-gray-200 animate-pulse rounded-lg w-48 mb-6"></div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {Array.from({ length: 8 }).map((_, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg"
                    >
                      <div className="w-2 h-2 bg-gray-300 rounded-full flex-shrink-0"></div>
                      <div className="h-4 bg-gray-200 animate-pulse rounded-lg flex-1"></div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-8">
              {/* Demo Button Skeleton */}
              <div className="bg-white p-6 rounded-2xl shadow-lg border border-gray-100">
                <div className="h-6 bg-gray-200 animate-pulse rounded-lg w-32 mb-4"></div>
                <div className="space-y-2 mb-6">
                  <div className="h-4 bg-gray-200 animate-pulse rounded-lg"></div>
                  <div className="h-4 bg-gray-200 animate-pulse rounded-lg max-w-3/4"></div>
                </div>
                <div className="h-12 bg-gray-200 animate-pulse rounded-lg w-full"></div>
              </div>

              {/* Technologies Skeleton */}
              <div className="bg-white p-6 rounded-2xl shadow-lg border border-gray-100">
                <div className="h-6 bg-gray-200 animate-pulse rounded-lg w-40 mb-4"></div>
                <div className="flex flex-wrap gap-2">
                  {Array.from({ length: 6 }).map((_, index) => (
                    <div
                      key={index}
                      className="h-8 bg-gray-200 animate-pulse rounded-lg w-20"
                    ></div>
                  ))}
                </div>
              </div>

              {/* Template Info Skeleton */}
              <div className="bg-white p-6 rounded-2xl shadow-lg border border-gray-100">
                <div className="h-6 bg-gray-200 animate-pulse rounded-lg w-36 mb-4"></div>
                <div className="space-y-3">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <div key={index} className="flex justify-between">
                      <div className="h-4 bg-gray-200 animate-pulse rounded-lg w-20"></div>
                      <div className="h-4 bg-gray-200 animate-pulse rounded-lg w-24"></div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Contact CTA Skeleton */}
              <div className="bg-gray-50 p-6 rounded-2xl">
                <div className="h-6 bg-gray-200 animate-pulse rounded-lg w-36 mb-3"></div>
                <div className="space-y-2 mb-4">
                  <div className="h-4 bg-gray-200 animate-pulse rounded-lg"></div>
                  <div className="h-4 bg-gray-200 animate-pulse rounded-lg max-w-4/5"></div>
                </div>
                <div className="h-12 bg-gray-200 animate-pulse rounded-lg w-full"></div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}

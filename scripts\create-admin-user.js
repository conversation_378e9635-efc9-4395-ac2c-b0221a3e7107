#!/usr/bin/env node

/**
 * Admin User Creation Script
 * This script creates the initial admin user for the iREME Soft Hub admin dashboard
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Configuration
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = 'Nike14##';

async function createAdminUser() {
  console.log('🚀 Creating admin user for iREME Soft Hub...\n');

  // Check environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('❌ Missing Supabase environment variables');
    console.error('Please ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set in .env.local');
    process.exit(1);
  }

  const supabase = createClient(supabaseUrl, supabaseAnonKey);

  try {
    console.log('📧 Creating admin user in Supabase Auth...');
    
    // Try to sign up the admin user
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
      options: {
        data: {
          first_name: 'System',
          last_name: 'Administrator',
          role: 'super_admin'
        }
      }
    });

    if (authError) {
      if (authError.message.includes('already registered')) {
        console.log('ℹ️  Admin user already exists in Supabase Auth');
      } else {
        throw authError;
      }
    } else {
      console.log('✅ Admin user created in Supabase Auth');
    }

    // Check if admin_users table exists and create admin user record
    console.log('👤 Creating admin user record...');
    
    const { data: adminData, error: adminError } = await supabase
      .from('admin_users')
      .upsert({
        email: ADMIN_EMAIL,
        supabase_user_id: authData?.user?.id,
        first_name: 'System',
        last_name: 'Administrator',
        role: 'super_admin',
        is_active: true,
        is_verified: true
      }, {
        onConflict: 'email'
      });

    if (adminError) {
      if (adminError.message.includes('relation "admin_users" does not exist')) {
        console.log('⚠️  Admin users table does not exist. Please run the database migrations first.');
        console.log('📋 Instructions:');
        console.log('1. Go to your Supabase SQL Editor: https://supabase.com/dashboard/project/ciklmqccwnthipbmmsnl/sql');
        console.log('2. Run the migration file: supabase/migrations/006_create_admin_dashboard_schema.sql');
        console.log('3. Run the functions file: supabase/migrations/007_create_admin_dashboard_functions.sql');
        console.log('4. Then run this script again');
      } else {
        throw adminError;
      }
    } else {
      console.log('✅ Admin user record created/updated');
    }

    console.log('\n🎉 Admin user setup completed!');
    console.log('\n📋 Login Credentials:');
    console.log(`Email: ${ADMIN_EMAIL}`);
    console.log(`Password: ${ADMIN_PASSWORD}`);
    console.log('\n🔗 Admin Login URL: http://localhost:3001/admin/login');

  } catch (error) {
    console.error('❌ Error creating admin user:', error.message);
    process.exit(1);
  }
}

// Run the script
createAdminUser();

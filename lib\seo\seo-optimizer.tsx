/**
 * SEO Optimization Utilities
 * Handles meta tags, structured data, and SEO best practices
 */

import React from 'react';
import Head from 'next/head';

interface SEOData {
  title?: string;
  description?: string;
  keywords?: string[];
  canonical?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogType?: string;
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player';
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
  jsonLd?: object;
  noindex?: boolean;
  nofollow?: boolean;
}

interface BreadcrumbItem {
  name: string;
  url: string;
}

interface ArticleData {
  headline: string;
  description: string;
  author: string;
  datePublished: string;
  dateModified?: string;
  image?: string;
  url: string;
}

interface OrganizationData {
  name: string;
  url: string;
  logo: string;
  description: string;
  contactPoint?: {
    telephone: string;
    contactType: string;
    email?: string;
  };
  sameAs?: string[];
}

class SEOOptimizer {
  private defaultSEO: SEOData = {
    title: 'iREME Soft Hub - Elite App Builders',
    description: 'Professional software development services, custom web applications, mobile apps, and digital solutions for businesses.',
    keywords: ['software development', 'web development', 'mobile apps', 'custom software', 'digital solutions'],
    ogType: 'website',
    twitterCard: 'summary_large_image',
  };

  private siteUrl: string = 'https://iremesofthub.com';
  private siteName: string = 'iREME Soft Hub';

  /**
   * Generate optimized title
   */
  generateTitle(title?: string, includeCompany: boolean = true): string {
    if (!title) return this.defaultSEO.title!;
    
    if (includeCompany && !title.includes(this.siteName)) {
      return `${title} | ${this.siteName}`;
    }
    
    return title;
  }

  /**
   * Generate meta description
   */
  generateDescription(description?: string): string {
    if (!description) return this.defaultSEO.description!;
    
    // Ensure description is within optimal length (150-160 characters)
    if (description.length > 160) {
      return description.substring(0, 157) + '...';
    }
    
    return description;
  }

  /**
   * Generate keywords string
   */
  generateKeywords(keywords?: string[]): string {
    const allKeywords = [...(this.defaultSEO.keywords || []), ...(keywords || [])];
    return [...new Set(allKeywords)].join(', ');
  }

  /**
   * Generate canonical URL
   */
  generateCanonical(path: string): string {
    return `${this.siteUrl}${path.startsWith('/') ? path : `/${path}`}`;
  }

  /**
   * Generate Open Graph data
   */
  generateOpenGraph(data: SEOData, path: string): object {
    return {
      'og:title': data.ogTitle || data.title || this.defaultSEO.title,
      'og:description': data.ogDescription || data.description || this.defaultSEO.description,
      'og:type': data.ogType || this.defaultSEO.ogType,
      'og:url': this.generateCanonical(path),
      'og:site_name': this.siteName,
      'og:image': data.ogImage || `${this.siteUrl}/images/og-default.jpg`,
      'og:image:width': '1200',
      'og:image:height': '630',
      'og:locale': 'en_US',
    };
  }

  /**
   * Generate Twitter Card data
   */
  generateTwitterCard(data: SEOData): object {
    return {
      'twitter:card': data.twitterCard || this.defaultSEO.twitterCard,
      'twitter:title': data.twitterTitle || data.title || this.defaultSEO.title,
      'twitter:description': data.twitterDescription || data.description || this.defaultSEO.description,
      'twitter:image': data.twitterImage || data.ogImage || `${this.siteUrl}/images/twitter-default.jpg`,
      'twitter:site': '@iremesofthub',
      'twitter:creator': '@iremesofthub',
    };
  }

  /**
   * Generate JSON-LD structured data for organization
   */
  generateOrganizationSchema(data: OrganizationData): object {
    return {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: data.name,
      url: data.url,
      logo: {
        '@type': 'ImageObject',
        url: data.logo,
      },
      description: data.description,
      contactPoint: data.contactPoint ? {
        '@type': 'ContactPoint',
        telephone: data.contactPoint.telephone,
        contactType: data.contactPoint.contactType,
        email: data.contactPoint.email,
      } : undefined,
      sameAs: data.sameAs,
    };
  }

  /**
   * Generate JSON-LD structured data for article
   */
  generateArticleSchema(data: ArticleData): object {
    return {
      '@context': 'https://schema.org',
      '@type': 'Article',
      headline: data.headline,
      description: data.description,
      author: {
        '@type': 'Person',
        name: data.author,
      },
      publisher: {
        '@type': 'Organization',
        name: this.siteName,
        logo: {
          '@type': 'ImageObject',
          url: `${this.siteUrl}/images/logo.png`,
        },
      },
      datePublished: data.datePublished,
      dateModified: data.dateModified || data.datePublished,
      image: data.image ? {
        '@type': 'ImageObject',
        url: data.image,
      } : undefined,
      url: data.url,
    };
  }

  /**
   * Generate breadcrumb schema
   */
  generateBreadcrumbSchema(items: BreadcrumbItem[]): object {
    return {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: items.map((item, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        name: item.name,
        item: item.url,
      })),
    };
  }

  /**
   * Generate FAQ schema
   */
  generateFAQSchema(faqs: { question: string; answer: string }[]): object {
    return {
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      mainEntity: faqs.map(faq => ({
        '@type': 'Question',
        name: faq.question,
        acceptedAnswer: {
          '@type': 'Answer',
          text: faq.answer,
        },
      })),
    };
  }

  /**
   * Generate service schema
   */
  generateServiceSchema(service: {
    name: string;
    description: string;
    provider: string;
    areaServed?: string;
    serviceType?: string;
  }): object {
    return {
      '@context': 'https://schema.org',
      '@type': 'Service',
      name: service.name,
      description: service.description,
      provider: {
        '@type': 'Organization',
        name: service.provider,
      },
      areaServed: service.areaServed,
      serviceType: service.serviceType,
    };
  }

  /**
   * Analyze content for SEO
   */
  analyzeContent(content: string, targetKeywords: string[]): {
    wordCount: number;
    keywordDensity: Record<string, number>;
    readabilityScore: number;
    suggestions: string[];
  } {
    const words = content.toLowerCase().split(/\s+/).filter(word => word.length > 2);
    const wordCount = words.length;
    
    // Calculate keyword density
    const keywordDensity: Record<string, number> = {};
    targetKeywords.forEach(keyword => {
      const keywordLower = keyword.toLowerCase();
      const occurrences = content.toLowerCase().split(keywordLower).length - 1;
      keywordDensity[keyword] = (occurrences / wordCount) * 100;
    });

    // Simple readability score (Flesch Reading Ease approximation)
    const sentences = content.split(/[.!?]+/).length;
    const avgWordsPerSentence = wordCount / sentences;
    const avgSyllablesPerWord = this.estimateAverageSyllables(words);
    const readabilityScore = 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord);

    // Generate suggestions
    const suggestions: string[] = [];
    
    if (wordCount < 300) {
      suggestions.push('Content is too short. Aim for at least 300 words.');
    }
    
    if (wordCount > 2000) {
      suggestions.push('Content is very long. Consider breaking it into sections.');
    }
    
    targetKeywords.forEach(keyword => {
      const density = keywordDensity[keyword];
      if (density !== undefined) {
        if (density < 0.5) {
          suggestions.push(`Keyword "${keyword}" density is too low (${density.toFixed(1)}%). Aim for 0.5-2%.`);
        } else if (density > 3) {
          suggestions.push(`Keyword "${keyword}" density is too high (${density.toFixed(1)}%). Reduce to avoid keyword stuffing.`);
        }
      }
    });
    
    if (readabilityScore < 30) {
      suggestions.push('Content is difficult to read. Use shorter sentences and simpler words.');
    }

    return {
      wordCount,
      keywordDensity,
      readabilityScore,
      suggestions,
    };
  }

  /**
   * Estimate average syllables per word
   */
  private estimateAverageSyllables(words: string[]): number {
    const totalSyllables = words.reduce((sum, word) => {
      return sum + this.countSyllables(word);
    }, 0);
    
    return totalSyllables / words.length;
  }

  /**
   * Count syllables in a word (approximation)
   */
  private countSyllables(word: string): number {
    word = word.toLowerCase();
    if (word.length <= 3) return 1;
    
    const vowels = 'aeiouy';
    let syllableCount = 0;
    let previousWasVowel = false;
    
    for (let i = 0; i < word.length; i++) {
      const char = word[i];
      if (char) {
        const isVowel = vowels.includes(char);
        if (isVowel && !previousWasVowel) {
          syllableCount++;
        }
        previousWasVowel = isVowel;
      }
    }
    
    // Handle silent 'e'
    if (word.endsWith('e')) {
      syllableCount--;
    }
    
    return Math.max(1, syllableCount);
  }
}

// React component for SEO head tags
interface SEOHeadProps extends SEOData {
  path: string;
  breadcrumbs?: BreadcrumbItem[];
  article?: ArticleData;
  service?: {
    name: string;
    description: string;
    provider: string;
    areaServed?: string;
    serviceType?: string;
  };
}

export function SEOHead({
  path,
  title,
  description,
  keywords,
  canonical,
  ogTitle,
  ogDescription,
  ogImage,
  ogType,
  twitterCard,
  twitterTitle,
  twitterDescription,
  twitterImage,
  jsonLd,
  noindex,
  nofollow,
  breadcrumbs,
  article,
  service,
}: SEOHeadProps) {
  const seo = new SEOOptimizer();
  
  const optimizedTitle = seo.generateTitle(title);
  const optimizedDescription = seo.generateDescription(description);
  const optimizedKeywords = seo.generateKeywords(keywords);
  const canonicalUrl = canonical || seo.generateCanonical(path);
  
  const ogData = seo.generateOpenGraph({
    ...(title && { title }),
    ...(description && { description }),
    ...(ogTitle && { ogTitle }),
    ...(ogDescription && { ogDescription }),
    ...(ogImage && { ogImage }),
    ...(ogType && { ogType }),
  }, path);
  
  const twitterData = seo.generateTwitterCard({
    ...(twitterCard && { twitterCard }),
    ...(twitterTitle && { twitterTitle }),
    ...(twitterDescription && { twitterDescription }),
    ...(twitterImage && { twitterImage }),
    ...(title && { title }),
    ...(description && { description }),
    ...(ogImage && { ogImage }),
  });

  // Generate structured data
  const structuredData: object[] = [];
  
  if (breadcrumbs) {
    structuredData.push(seo.generateBreadcrumbSchema(breadcrumbs));
  }
  
  if (article) {
    structuredData.push(seo.generateArticleSchema(article));
  }
  
  if (service) {
    structuredData.push(seo.generateServiceSchema(service));
  }
  
  if (jsonLd) {
    structuredData.push(jsonLd);
  }

  const robotsContent = [
    noindex ? 'noindex' : 'index',
    nofollow ? 'nofollow' : 'follow',
  ].join(', ');

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{optimizedTitle}</title>
      <meta name="description" content={optimizedDescription} />
      <meta name="keywords" content={optimizedKeywords} />
      <meta name="robots" content={robotsContent} />
      <link rel="canonical" href={canonicalUrl} />
      
      {/* Open Graph */}
      {Object.entries(ogData).map(([property, content]) => (
        <meta key={property} property={property} content={content as string} />
      ))}
      
      {/* Twitter Card */}
      {Object.entries(twitterData).map(([name, content]) => (
        <meta key={name} name={name} content={content as string} />
      ))}
      
      {/* Structured Data */}
      {structuredData.map((data, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
        />
      ))}
      
      {/* Additional SEO Tags */}
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
      <meta name="language" content="English" />
      <meta name="author" content="iREME Soft Hub" />
    </Head>
  );
}

// React hook for SEO analysis
export function useSEOAnalysis(content: string, keywords: string[]) {
  const [analysis, setAnalysis] = React.useState<ReturnType<SEOOptimizer['analyzeContent']> | null>(null);
  
  React.useEffect(() => {
    if (content && keywords.length > 0) {
      const seo = new SEOOptimizer();
      const result = seo.analyzeContent(content, keywords);
      setAnalysis(result);
    }
  }, [content, keywords]);
  
  return analysis;
}

export default SEOOptimizer;

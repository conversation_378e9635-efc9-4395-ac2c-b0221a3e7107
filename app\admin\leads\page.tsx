'use client';

import React from 'react';
import AdminProtectedRoute from '@/components/admin/auth/AdminProtectedRoute';
import AdminLayout from '@/components/admin/layout/AdminLayout';
import FollowUpDashboard from '@/components/admin/leads/FollowUpDashboard';
import { ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';

export default function AdminLeadsPage() {
  return (
    <AdminProtectedRoute requiredPermission={ADMIN_PERMISSIONS.LEADS_VIEW}>
      <AdminLayout 
        title="Lead Management" 
        subtitle="Track and manage your sales pipeline"
      >
        <FollowUpDashboard />
      </AdminLayout>
    </AdminProtectedRoute>
  );
}

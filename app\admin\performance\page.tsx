'use client';

import React from 'react';
import AdminProtectedRoute from '@/components/admin/auth/AdminProtectedRoute';
import AdminLayout from '@/components/admin/layout/AdminLayout';
import PerformanceDashboard from '@/components/admin/performance/PerformanceDashboard';
import { ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';

export default function AdminPerformancePage() {
  return (
    <AdminProtectedRoute requiredPermission={ADMIN_PERMISSIONS.SYSTEM_SETTINGS}>
      <AdminLayout 
        title="Performance Dashboard" 
        subtitle="Monitor system performance and optimization metrics"
      >
        <PerformanceDashboard />
      </AdminLayout>
    </AdminProtectedRoute>
  );
}

'use client';

import React from 'react';
import AdminProtectedRoute from '@/components/admin/auth/AdminProtectedRoute';
import AdminLayout from '@/components/admin/layout/AdminLayout';
import ContentAnalytics from '@/components/admin/analytics/ContentAnalytics';
import { ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';

export default function AdminContentAnalyticsPage() {
  return (
    <AdminProtectedRoute requiredPermission={ADMIN_PERMISSIONS.ANALYTICS_VIEW}>
      <AdminLayout 
        title="Content Analytics" 
        subtitle="Track performance of your templates, services, and blog posts"
      >
        <ContentAnalytics />
      </AdminLayout>
    </AdminProtectedRoute>
  );
}

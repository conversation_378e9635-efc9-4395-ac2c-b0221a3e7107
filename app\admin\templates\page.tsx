'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import AdminProtectedRoute from '@/components/admin/auth/AdminProtectedRoute';
import AdminLayout from '@/components/admin/layout/AdminLayout';
import TemplateList from '@/components/admin/templates/TemplateList';
import { ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';
import { AdminTemplate } from '@/lib/types/admin';

export default function AdminTemplatesPage() {
  const router = useRouter();

  const handleEdit = (template: AdminTemplate) => {
    router.push(`/admin/templates/${template.id}/edit`);
  };

  const handleDelete = (template: AdminTemplate) => {
    // Template deletion is handled in the TemplateList component
    console.log('Template deleted:', template.name);
  };

  return (
    <AdminProtectedRoute requiredPermission={ADMIN_PERMISSIONS.TEMPLATES_VIEW}>
      <AdminLayout 
        title="Templates" 
        subtitle="Manage your project templates and showcase items"
      >
        <TemplateList onEdit={handleEdit} onDelete={handleDelete} />
      </AdminLayout>
    </AdminProtectedRoute>
  );
}

'use client';

import React, { useState, useEffect } from 'react';
import { ArrowR<PERSON>, CheckCircle, Clock, Users, Star, Github, Linkedin, Globe, Upload, X, FileText, MapPin, DollarSign, Calendar, Briefcase } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { submitJobApplication, uploadResumeFile, getJobPostingById, JobPosting } from '@/lib/supabase/collaborate';

interface JobApplicationData {
  // Personal Information
  fullName: string;
  email: string;
  phone: string;
  location: string;
  currentPosition: string;
  
  // Professional Information
  resumeFile: File | null;
  githubLink: string;
  linkedinLink: string;
  portfolioLink: string;
  yearsExperience: string;
  salaryExpectation: string;
  
  // Application Details
  positionAppliedFor: string;
  coverLetter: string;
  availabilityStartDate: string;
  preferredWorkArrangement: string;
  additionalInformation: string;
}

const JobApplicationPage = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [jobId, setJobId] = useState<string>('');
  const [jobPosting, setJobPosting] = useState<JobPosting | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [formData, setFormData] = useState<JobApplicationData>({
    fullName: '',
    email: '',
    phone: '',
    location: '',
    currentPosition: '',
    resumeFile: null,
    githubLink: '',
    linkedinLink: '',
    portfolioLink: '',
    yearsExperience: '',
    salaryExpectation: '',
    positionAppliedFor: '',
    coverLetter: '',
    availabilityStartDate: '',
    preferredWorkArrangement: '',
    additionalInformation: ''
  });

  const totalSteps = 3;

  const experienceOptions = [
    '0-1 years',
    '1-2 years',
    '2-3 years',
    '3-5 years',
    '5-8 years',
    '8+ years'
  ];

  const workArrangementOptions = [
    'Remote',
    'On-site',
    'Hybrid',
    'Flexible'
  ];

  const salaryRanges = [
    'Below $500/month',
    '$500-800/month',
    '$800-1200/month',
    '$1200-2000/month',
    '$2000-3000/month',
    '$3000+/month',
    'Negotiable'
  ];

  useEffect(() => {
    // Get job ID from URL params
    const urlParams = new URLSearchParams(window.location.search);
    const id = urlParams.get('id');
    if (id) {
      setJobId(id);
      setFormData(prev => ({ ...prev, positionAppliedFor: id }));
      loadJobPosting(id);
    }
  }, []);

  const loadJobPosting = async (id: string) => {
    try {
      const jobData = await getJobPostingById(id);
      if (jobData) {
        setJobPosting(jobData);
        setFormData(prev => ({ ...prev, positionAppliedFor: jobData.title }));
      }
    } catch (error) {
      console.error('Error loading job posting:', error);
    }
  };

  const handleInputChange = (field: keyof JobApplicationData, value: string | File | null) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    if (step === 1) {
      if (!formData.fullName.trim()) newErrors.fullName = 'Full name is required';
      if (!formData.email.trim()) newErrors.email = 'Email is required';
      else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = 'Please enter a valid email';
      if (!formData.phone.trim()) newErrors.phone = 'Phone number is required';
      if (!formData.location.trim()) newErrors.location = 'Location is required';
    }

    if (step === 2) {
      if (!formData.resumeFile) newErrors.resumeFile = 'Resume/CV is required';
      if (!formData.githubLink.trim()) newErrors.githubLink = 'GitHub profile is required';
      else if (!formData.githubLink.includes('github.com')) newErrors.githubLink = 'Please enter a valid GitHub URL';
      if (!formData.yearsExperience) newErrors.yearsExperience = 'Please select your experience level';
    }

    if (step === 3) {
      if (!formData.coverLetter.trim()) newErrors.coverLetter = 'Cover letter is required';
      else if (formData.coverLetter.length < 300) newErrors.coverLetter = 'Please provide at least 300 characters';
      else if (formData.coverLetter.length > 800) newErrors.coverLetter = 'Please keep it under 800 characters';
      if (!formData.availabilityStartDate) newErrors.availabilityStartDate = 'Please select your availability start date';
      if (!formData.preferredWorkArrangement) newErrors.preferredWorkArrangement = 'Please select your preferred work arrangement';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleFileUpload = (file: File) => {
    // Validate file type and size
    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!allowedTypes.includes(file.type)) {
      setErrors(prev => ({ ...prev, resumeFile: 'Please upload a PDF or Word document' }));
      return;
    }

    if (file.size > maxSize) {
      setErrors(prev => ({ ...prev, resumeFile: 'File size must be less than 5MB' }));
      return;
    }

    handleInputChange('resumeFile', file);
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files[0]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateStep(3)) {
      return;
    }

    if (!formData.resumeFile) {
      setErrors({ resumeFile: 'Resume is required' });
      return;
    }

    setIsSubmitting(true);

    try {
      // First, upload the resume file
      const tempId = Date.now().toString(); // Temporary ID for file upload
      const uploadResult = await uploadResumeFile(formData.resumeFile, tempId);

      if (!uploadResult.success) {
        setErrors({ submit: uploadResult.error || 'Failed to upload resume' });
        setIsSubmitting(false);
        return;
      }

      // Prepare data for submission
      const submissionData = {
        full_name: formData.fullName,
        email: formData.email,
        phone: formData.phone,
        location: formData.location,
        current_position: formData.currentPosition,
        resume_file_url: uploadResult.url!,
        github_link: formData.githubLink,
        linkedin_link: formData.linkedinLink,
        portfolio_link: formData.portfolioLink,
        years_experience: formData.yearsExperience,
        salary_expectation: formData.salaryExpectation,
        position_applied_for: formData.positionAppliedFor,
        cover_letter: formData.coverLetter,
        availability_start_date: formData.availabilityStartDate,
        preferred_work_arrangement: formData.preferredWorkArrangement,
        additional_information: formData.additionalInformation,
        job_posting_id: jobId
      };

      const result = await submitJobApplication(submissionData);

      if (result.success) {
        setIsSubmitted(true);
      } else {
        setErrors({ submit: result.error || 'Failed to submit application' });
      }
    } catch (error) {
      console.error('Error submitting application:', error);
      setErrors({ submit: 'An unexpected error occurred. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const nextStep = () => {
    if (validateStep(currentStep) && currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <div className="bg-white p-12 rounded-2xl shadow-lg border border-gray-100">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-6" />
              <h1 className="text-3xl font-bold text-black mb-4 font-jost">
                Application Submitted Successfully!
              </h1>
              <p className="text-lg text-gray-600 mb-8 font-jost">
                Thank you for your interest in joining our team. We'll review your application and get back to you within 5-7 business days.
              </p>
              <div className="bg-gray-50 p-6 rounded-lg mb-8">
                <h3 className="text-lg font-semibold text-black mb-2 font-jost">What happens next?</h3>
                <div className="space-y-2 text-left">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-700 font-jost">HR team reviews your application and resume</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-700 font-jost">Initial screening call if you're a good fit</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-700 font-jost">Technical interview and final decision</span>
                  </div>
                </div>
              </div>
              <div className="flex gap-4 justify-center">
                <button 
                  onClick={() => window.location.href = '/collaborate'}
                  className="bg-black text-white px-8 py-3 rounded-full hover:bg-gray-800 transition-colors duration-300 font-jost"
                >
                  View More Jobs
                </button>
                <button 
                  onClick={() => window.location.href = '/'}
                  className="border-2 border-gray-300 text-gray-700 px-8 py-3 rounded-full hover:border-gray-400 hover:bg-gray-50 transition-colors duration-300 font-jost"
                >
                  Return Home
                </button>
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1 py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center bg-black text-white px-4 py-2 rounded-full text-sm mb-8 hover:scale-105 transition-all duration-300 cursor-pointer animate-fade-in font-jost">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
              Job Application - Join Our Team
            </div>
            
            <h1 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              Apply for Position
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8 font-jost">
              Take the next step in your career with iREME Soft Hub. We're looking for talented individuals who are passionate about technology and innovation.
            </p>
            
            {/* Progress Indicator */}
            <div className="flex items-center justify-center mb-12">
              {[1, 2, 3].map((step) => (
                <div key={step} className="flex items-center">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300 ${
                    step <= currentStep 
                      ? 'bg-black text-white' 
                      : 'bg-gray-200 text-gray-500'
                  }`}>
                    {step}
                  </div>
                  {step < 3 && (
                    <div className={`w-16 h-1 mx-2 transition-all duration-300 ${
                      step < currentStep ? 'bg-black' : 'bg-gray-200'
                    }`}></div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Job Details Section */}
          {jobPosting && (
            <div className="mb-16">
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                <div className="bg-gradient-to-r from-green-50 to-blue-50 p-8 border-b border-gray-100">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-4">
                        <h2 className="text-2xl font-bold text-black font-jost">{jobPosting.title}</h2>
                        {jobPosting.is_active && (
                          <div className="flex items-center gap-1 text-green-600">
                            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                            <span className="text-sm font-jost">Active Position</span>
                          </div>
                        )}
                        {jobPosting.application_deadline &&
                         new Date(jobPosting.application_deadline) < new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) && (
                          <div className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium font-jost">
                            Closing Soon
                          </div>
                        )}
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-600 mb-4 flex-wrap">
                        <div className={`px-3 py-1 rounded-full text-xs font-medium border ${
                          jobPosting.employment_type === 'Full-time' ? 'bg-blue-100 text-blue-800 border-blue-200' :
                          jobPosting.employment_type === 'Part-time' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                          'bg-orange-100 text-orange-800 border-orange-200'
                        } font-jost`}>
                          {jobPosting.employment_type}
                        </div>
                        <div className={`px-3 py-1 rounded-full text-xs font-medium border ${
                          jobPosting.experience_level === 'Junior' ? 'bg-green-100 text-green-800 border-green-200' :
                          jobPosting.experience_level === 'Mid' ? 'bg-blue-100 text-blue-800 border-blue-200' :
                          'bg-purple-100 text-purple-800 border-purple-200'
                        } font-jost`}>
                          {jobPosting.experience_level}
                        </div>
                        <div className={`px-3 py-1 rounded-full text-xs font-medium border ${
                          jobPosting.location_type === 'Remote' ? 'bg-green-100 text-green-800 border-green-200' :
                          jobPosting.location_type === 'On-site' ? 'bg-blue-100 text-blue-800 border-blue-200' :
                          'bg-purple-100 text-purple-800 border-purple-200'
                        } font-jost`}>
                          <MapPin size={12} className="inline mr-1" />
                          {jobPosting.location_type}
                        </div>
                        <div className="flex items-center gap-1 text-yellow-500">
                          <Star size={16} fill="currentColor" />
                          <span className="text-sm text-gray-600 font-jost">Featured Position</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-8">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Job Description */}
                    <div className="lg:col-span-2">
                      <h3 className="text-lg font-semibold text-black mb-4 font-jost">Job Description</h3>
                      <p className="text-gray-700 leading-relaxed mb-6 font-jost">{jobPosting.description}</p>

                      {/* Required Skills */}
                      <div className="mb-6">
                        <h4 className="text-md font-semibold text-black mb-3 font-jost">Required Skills & Qualifications</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          {jobPosting.required_skills.map((skill, index) => (
                            <div key={index} className="flex items-center gap-2">
                              <CheckCircle size={16} className="text-green-500" />
                              <span className="text-gray-700 text-sm font-jost">{skill}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Key Responsibilities */}
                      <div>
                        <h4 className="text-md font-semibold text-black mb-3 font-jost">Key Responsibilities</h4>
                        <div className="space-y-2">
                          <div className="flex items-start gap-2">
                            <CheckCircle size={16} className="text-blue-500 mt-0.5" />
                            <span className="text-gray-700 text-sm font-jost">Develop and maintain high-quality software applications</span>
                          </div>
                          <div className="flex items-start gap-2">
                            <CheckCircle size={16} className="text-blue-500 mt-0.5" />
                            <span className="text-gray-700 text-sm font-jost">Collaborate with cross-functional teams on project delivery</span>
                          </div>
                          <div className="flex items-start gap-2">
                            <CheckCircle size={16} className="text-blue-500 mt-0.5" />
                            <span className="text-gray-700 text-sm font-jost">Participate in code reviews and technical discussions</span>
                          </div>
                          <div className="flex items-start gap-2">
                            <CheckCircle size={16} className="text-blue-500 mt-0.5" />
                            <span className="text-gray-700 text-sm font-jost">Contribute to architectural decisions and best practices</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Job Details */}
                    <div className="lg:col-span-1">
                      <h3 className="text-lg font-semibold text-black mb-4 font-jost">Position Details</h3>
                      <div className="space-y-4">
                        <div className="bg-green-50 rounded-lg p-4 text-center">
                          <DollarSign size={24} className="text-green-600 mx-auto mb-2" />
                          <div className="text-xl font-bold text-black font-jost">
                            {jobPosting.compensation_range}
                          </div>
                          <div className="text-sm text-gray-600 font-jost">Compensation Range</div>
                        </div>

                        <div className="bg-blue-50 rounded-lg p-4 text-center">
                          <Calendar size={24} className="text-blue-600 mx-auto mb-2" />
                          <div className="text-xl font-bold text-black font-jost">
                            {jobPosting.application_deadline ?
                              new Date(jobPosting.application_deadline).toLocaleDateString() :
                              'Open'
                            }
                          </div>
                          <div className="text-sm text-gray-600 font-jost">Application Deadline</div>
                        </div>

                        <div className="bg-purple-50 rounded-lg p-4 text-center">
                          <Briefcase size={24} className="text-purple-600 mx-auto mb-2" />
                          <div className="text-xl font-bold text-black font-jost">{jobPosting.employment_type}</div>
                          <div className="text-sm text-gray-600 font-jost">Employment Type</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Trust Signals Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100 sticky top-8">
                <h3 className="text-xl font-semibold text-black mb-6 font-jost">Why Work with iREME?</h3>
                
                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <Clock className="w-6 h-6 text-blue-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-black font-jost">Flexible Hours</h4>
                      <p className="text-sm text-gray-600 font-jost">Work-life balance is our priority</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <Users className="w-6 h-6 text-green-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-black font-jost">Great Team</h4>
                      <p className="text-sm text-gray-600 font-jost">Collaborative and supportive environment</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <Star className="w-6 h-6 text-yellow-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-black font-jost">Growth Opportunities</h4>
                      <p className="text-sm text-gray-600 font-jost">Continuous learning and career advancement</p>
                    </div>
                  </div>
                </div>

                <div className="mt-8 p-4 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-700 italic font-jost">
                    "iREME has been an amazing place to grow my career. The team is supportive and the projects are challenging and rewarding."
                  </p>
                  <p className="text-xs text-gray-500 mt-2 font-jost">- Sarah K., Senior Developer</p>
                </div>
              </div>
            </div>

            {/* Main Form */}
            <div className="lg:col-span-2">
              <div className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100">
                <form onSubmit={handleSubmit}>
                  {/* Step 1: Personal Information */}
                  {currentStep === 1 && (
                    <div className="space-y-6">
                      <h2 className="text-2xl font-bold text-black mb-6 font-jost">Personal Information</h2>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                            Full Name *
                          </label>
                          <input
                            type="text"
                            required
                            value={formData.fullName}
                            onChange={(e) => handleInputChange('fullName', e.target.value)}
                            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost ${
                              errors.fullName ? 'border-red-500' : 'border-gray-300'
                            }`}
                            placeholder="Your full name"
                          />
                          {errors.fullName && (
                            <p className="text-red-500 text-sm mt-1 font-jost">{errors.fullName}</p>
                          )}
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                            Email Address *
                          </label>
                          <input
                            type="email"
                            required
                            value={formData.email}
                            onChange={(e) => handleInputChange('email', e.target.value)}
                            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost ${
                              errors.email ? 'border-red-500' : 'border-gray-300'
                            }`}
                            placeholder="<EMAIL>"
                          />
                          {errors.email && (
                            <p className="text-red-500 text-sm mt-1 font-jost">{errors.email}</p>
                          )}
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                            Phone Number *
                          </label>
                          <input
                            type="tel"
                            required
                            value={formData.phone}
                            onChange={(e) => handleInputChange('phone', e.target.value)}
                            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost ${
                              errors.phone ? 'border-red-500' : 'border-gray-300'
                            }`}
                            placeholder="+250 XXX XXX XXX"
                          />
                          {errors.phone && (
                            <p className="text-red-500 text-sm mt-1 font-jost">{errors.phone}</p>
                          )}
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                            Location *
                          </label>
                          <input
                            type="text"
                            required
                            value={formData.location}
                            onChange={(e) => handleInputChange('location', e.target.value)}
                            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost ${
                              errors.location ? 'border-red-500' : 'border-gray-300'
                            }`}
                            placeholder="City, Country"
                          />
                          {errors.location && (
                            <p className="text-red-500 text-sm mt-1 font-jost">{errors.location}</p>
                          )}
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                          Current Position
                        </label>
                        <input
                          type="text"
                          value={formData.currentPosition}
                          onChange={(e) => handleInputChange('currentPosition', e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost"
                          placeholder="e.g., Frontend Developer at XYZ Company"
                        />
                      </div>
                    </div>
                  )}

                  {/* Step 2: Professional Information */}
                  {currentStep === 2 && (
                    <div className="space-y-6">
                      <h2 className="text-2xl font-bold text-black mb-6 font-jost">Professional Information</h2>

                      {/* Resume Upload */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-3 font-jost">
                          Resume/CV * (PDF or Word document, max 5MB)
                        </label>
                        <div
                          className={`relative border-2 border-dashed rounded-lg p-6 transition-all duration-300 ${
                            dragActive ? 'border-blue-500 bg-blue-50' :
                            errors.resumeFile ? 'border-red-500 bg-red-50' : 'border-gray-300 hover:border-gray-400'
                          }`}
                          onDragEnter={handleDrag}
                          onDragLeave={handleDrag}
                          onDragOver={handleDrag}
                          onDrop={handleDrop}
                        >
                          {formData.resumeFile ? (
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <FileText className="w-8 h-8 text-blue-500" />
                                <div>
                                  <p className="text-sm font-medium text-black font-jost">{formData.resumeFile.name}</p>
                                  <p className="text-xs text-gray-500 font-jost">
                                    {(formData.resumeFile.size / 1024 / 1024).toFixed(2)} MB
                                  </p>
                                </div>
                              </div>
                              <button
                                type="button"
                                onClick={() => handleInputChange('resumeFile', null)}
                                className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                              >
                                <X size={16} className="text-gray-500" />
                              </button>
                            </div>
                          ) : (
                            <div className="text-center">
                              <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                              <p className="text-sm text-gray-600 mb-2 font-jost">
                                Drag and drop your resume here, or{' '}
                                <label className="text-blue-500 hover:text-blue-600 cursor-pointer font-medium">
                                  browse files
                                  <input
                                    type="file"
                                    className="hidden"
                                    accept=".pdf,.doc,.docx"
                                    onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0])}
                                  />
                                </label>
                              </p>
                              <p className="text-xs text-gray-500 font-jost">PDF, DOC, DOCX up to 5MB</p>
                            </div>
                          )}
                        </div>
                        {errors.resumeFile && (
                          <p className="text-red-500 text-sm mt-1 font-jost">{errors.resumeFile}</p>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                            GitHub Profile *
                          </label>
                          <div className="relative">
                            <Github className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                            <input
                              type="url"
                              required
                              value={formData.githubLink}
                              onChange={(e) => handleInputChange('githubLink', e.target.value)}
                              className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost ${
                                errors.githubLink ? 'border-red-500' : 'border-gray-300'
                              }`}
                              placeholder="https://github.com/yourusername"
                            />
                          </div>
                          {errors.githubLink && (
                            <p className="text-red-500 text-sm mt-1 font-jost">{errors.githubLink}</p>
                          )}
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                            LinkedIn Profile
                          </label>
                          <div className="relative">
                            <Linkedin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                            <input
                              type="url"
                              value={formData.linkedinLink}
                              onChange={(e) => handleInputChange('linkedinLink', e.target.value)}
                              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost"
                              placeholder="https://linkedin.com/in/yourusername"
                            />
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                            Portfolio Website
                          </label>
                          <div className="relative">
                            <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                            <input
                              type="url"
                              value={formData.portfolioLink}
                              onChange={(e) => handleInputChange('portfolioLink', e.target.value)}
                              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost"
                              placeholder="https://yourportfolio.com"
                            />
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                            Years of Experience *
                          </label>
                          <select
                            required
                            value={formData.yearsExperience}
                            onChange={(e) => handleInputChange('yearsExperience', e.target.value)}
                            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost ${
                              errors.yearsExperience ? 'border-red-500' : 'border-gray-300'
                            }`}
                          >
                            <option value="">Select experience level</option>
                            {experienceOptions.map((option) => (
                              <option key={option} value={option}>{option}</option>
                            ))}
                          </select>
                          {errors.yearsExperience && (
                            <p className="text-red-500 text-sm mt-1 font-jost">{errors.yearsExperience}</p>
                          )}
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                          Salary Expectation
                        </label>
                        <select
                          value={formData.salaryExpectation}
                          onChange={(e) => handleInputChange('salaryExpectation', e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost"
                        >
                          <option value="">Select salary range</option>
                          {salaryRanges.map((range) => (
                            <option key={range} value={range}>{range}</option>
                          ))}
                        </select>
                      </div>
                    </div>
                  )}

                  {/* Step 3: Application Details */}
                  {currentStep === 3 && (
                    <div className="space-y-6">
                      <h2 className="text-2xl font-bold text-black mb-6 font-jost">Application Details</h2>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-3 font-jost">
                          Cover Letter * (300-800 characters)
                        </label>
                        <textarea
                          required
                          rows={8}
                          value={formData.coverLetter}
                          onChange={(e) => handleInputChange('coverLetter', e.target.value)}
                          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost ${
                            errors.coverLetter ? 'border-red-500' : 'border-gray-300'
                          }`}
                          placeholder="Tell us why you're interested in this position and what makes you a great fit for our team..."
                        />
                        <div className="flex justify-between items-center mt-1">
                          {errors.coverLetter && (
                            <p className="text-red-500 text-sm font-jost">{errors.coverLetter}</p>
                          )}
                          <p className={`text-sm ml-auto font-jost ${
                            formData.coverLetter.length < 300 ? 'text-red-500' :
                            formData.coverLetter.length > 800 ? 'text-red-500' : 'text-gray-500'
                          }`}>
                            {formData.coverLetter.length}/800 characters
                          </p>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-3 font-jost">
                            Availability to Start *
                          </label>
                          <input
                            type="date"
                            required
                            value={formData.availabilityStartDate}
                            onChange={(e) => handleInputChange('availabilityStartDate', e.target.value)}
                            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost ${
                              errors.availabilityStartDate ? 'border-red-500' : 'border-gray-300'
                            }`}
                            min={new Date().toISOString().split('T')[0]}
                          />
                          {errors.availabilityStartDate && (
                            <p className="text-red-500 text-sm mt-1 font-jost">{errors.availabilityStartDate}</p>
                          )}
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-3 font-jost">
                            Preferred Work Arrangement *
                          </label>
                          <select
                            required
                            value={formData.preferredWorkArrangement}
                            onChange={(e) => handleInputChange('preferredWorkArrangement', e.target.value)}
                            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost ${
                              errors.preferredWorkArrangement ? 'border-red-500' : 'border-gray-300'
                            }`}
                          >
                            <option value="">Select work arrangement</option>
                            {workArrangementOptions.map((option) => (
                              <option key={option} value={option}>{option}</option>
                            ))}
                          </select>
                          {errors.preferredWorkArrangement && (
                            <p className="text-red-500 text-sm mt-1 font-jost">{errors.preferredWorkArrangement}</p>
                          )}
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-3 font-jost">
                          Additional Information
                        </label>
                        <textarea
                          rows={4}
                          value={formData.additionalInformation}
                          onChange={(e) => handleInputChange('additionalInformation', e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost"
                          placeholder="Any additional information you'd like to share about your background, interests, or questions about the role..."
                        />
                      </div>

                      <div className="bg-gray-50 p-6 rounded-lg">
                        <h3 className="text-lg font-semibold text-black mb-3 font-jost">Application Summary</h3>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-600 font-jost">Name:</span>
                            <span className="text-black font-jost">{formData.fullName || 'Not provided'}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600 font-jost">Experience:</span>
                            <span className="text-black font-jost">{formData.yearsExperience || 'Not specified'}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600 font-jost">Resume:</span>
                            <span className="text-black font-jost">{formData.resumeFile ? 'Uploaded' : 'Not uploaded'}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600 font-jost">Work Arrangement:</span>
                            <span className="text-black font-jost">{formData.preferredWorkArrangement || 'Not specified'}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Navigation Buttons */}
                  <div className="flex justify-between mt-8">
                    <button
                      type="button"
                      onClick={prevStep}
                      disabled={currentStep === 1}
                      className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 font-jost ${
                        currentStep === 1
                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                      }`}
                    >
                      Previous
                    </button>
                    
                    {currentStep < totalSteps ? (
                      <button
                        type="button"
                        onClick={nextStep}
                        className="bg-black text-white px-8 py-3 rounded-lg hover:bg-gray-800 transition-all duration-300 flex items-center gap-2 font-jost"
                      >
                        Next Step
                        <ArrowRight size={18} />
                      </button>
                    ) : (
                      <button
                        type="submit"
                        disabled={isSubmitting}
                        className="bg-black text-white px-8 py-3 rounded-lg hover:bg-gray-800 transition-all duration-300 flex items-center gap-2 disabled:opacity-50 font-jost"
                      >
                        {isSubmitting ? 'Submitting...' : 'Submit Application'}
                        {!isSubmitting && <ArrowRight size={18} />}
                      </button>
                    )}
                  </div>

                  {/* Error Display */}
                  {errors.submit && (
                    <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                      <p className="text-red-600 text-sm font-jost">{errors.submit}</p>
                    </div>
                  )}
                </form>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default JobApplicationPage;

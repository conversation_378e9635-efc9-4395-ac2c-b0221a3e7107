'use client';

import React, { useState, useEffect } from 'react';
import { ArrowR<PERSON>, CheckCircle, Clock, Users, Star, Github, Linkedin, Globe, Code2, Calendar, Target } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { submitProjectApplication, getProjectById, Project } from '@/lib/supabase/collaborate';

interface ProjectApplicationData {
  // Personal Information
  fullName: string;
  email: string;
  phone: string;
  location: string;
  professionalTitle: string;
  
  // Technical Information
  githubLink: string;
  linkedinLink: string;
  portfolioLink: string;
  yearsExperience: string;
  primarySkills: string[];
  
  // Application Details
  projectInterest: string;
  motivationStatement: string;
  availabilityHours: string;
  expectedStartDate: string;
  additionalComments: string;
}

const ProjectApplicationPage = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [projectId, setProjectId] = useState<string>('');
  const [project, setProject] = useState<Project | null>(null);
  const [formData, setFormData] = useState<ProjectApplicationData>({
    fullName: '',
    email: '',
    phone: '',
    location: '',
    professionalTitle: '',
    githubLink: '',
    linkedinLink: '',
    portfolioLink: '',
    yearsExperience: '',
    primarySkills: [],
    projectInterest: '',
    motivationStatement: '',
    availabilityHours: '',
    expectedStartDate: '',
    additionalComments: ''
  });

  const totalSteps = 3;

  const experienceOptions = [
    '0-1 years',
    '1-2 years',
    '2-3 years',
    '3-5 years',
    '5+ years'
  ];

  const availabilityOptions = [
    '5-10 hours/week',
    '10-20 hours/week',
    '20-30 hours/week',
    '30+ hours/week'
  ];

  const skillOptions = [
    'React', 'Vue.js', 'Angular', 'JavaScript', 'TypeScript',
    'Node.js', 'Python', 'Java', 'C#', 'PHP',
    'HTML/CSS', 'Tailwind CSS', 'Bootstrap', 'SASS',
    'MongoDB', 'PostgreSQL', 'MySQL', 'Firebase',
    'AWS', 'Azure', 'Google Cloud', 'Docker',
    'Git', 'REST APIs', 'GraphQL', 'Testing',
    'UI/UX Design', 'Figma', 'Adobe Creative Suite'
  ];

  useEffect(() => {
    // Get project ID from URL params
    const urlParams = new URLSearchParams(window.location.search);
    const id = urlParams.get('id');
    if (id) {
      setProjectId(id);
      setFormData(prev => ({ ...prev, projectInterest: id, project_id: id }));
      loadProject(id);
    }
  }, []);

  const loadProject = async (id: string) => {
    try {
      const projectData = await getProjectById(id);
      if (projectData) {
        setProject(projectData);
        setFormData(prev => ({ ...prev, projectInterest: projectData.title }));
      }
    } catch (error) {
      console.error('Error loading project:', error);
    }
  };

  const handleInputChange = (field: keyof ProjectApplicationData, value: string | string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    if (step === 1) {
      if (!formData.fullName.trim()) newErrors.fullName = 'Full name is required';
      if (!formData.email.trim()) newErrors.email = 'Email is required';
      else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = 'Please enter a valid email';
      if (!formData.location.trim()) newErrors.location = 'Location is required';
    }

    if (step === 2) {
      if (!formData.githubLink.trim()) newErrors.githubLink = 'GitHub profile is required';
      else if (!formData.githubLink.includes('github.com')) newErrors.githubLink = 'Please enter a valid GitHub URL';
      if (!formData.yearsExperience) newErrors.yearsExperience = 'Please select your experience level';
      if (formData.primarySkills.length === 0) newErrors.primarySkills = 'Please select at least one skill';
    }

    if (step === 3) {
      if (!formData.motivationStatement.trim()) newErrors.motivationStatement = 'Motivation statement is required';
      else if (formData.motivationStatement.length < 200) newErrors.motivationStatement = 'Please provide at least 200 characters';
      else if (formData.motivationStatement.length > 500) newErrors.motivationStatement = 'Please keep it under 500 characters';
      if (!formData.availabilityHours) newErrors.availabilityHours = 'Please select your availability';
      if (!formData.expectedStartDate) newErrors.expectedStartDate = 'Please select your expected start date';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSkillToggle = (skill: string) => {
    setFormData(prev => ({
      ...prev,
      primarySkills: prev.primarySkills.includes(skill)
        ? prev.primarySkills.filter(s => s !== skill)
        : [...prev.primarySkills, skill]
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateStep(3)) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Validate required fields before submission
      if (!formData.fullName.trim()) {
        setErrors({ submit: 'Full name is required' });
        return;
      }
      if (!formData.email.trim()) {
        setErrors({ submit: 'Email is required' });
        return;
      }
      if (!formData.location.trim()) {
        setErrors({ submit: 'Location is required' });
        return;
      }
      if (!formData.githubLink.trim()) {
        setErrors({ submit: 'GitHub link is required' });
        return;
      }
      if (!formData.motivationStatement.trim()) {
        setErrors({ submit: 'Motivation statement is required' });
        return;
      }

      // Prepare data for submission
      const submissionData = {
        full_name: formData.fullName.trim(),
        email: formData.email.trim(),
        phone: formData.phone.trim() || null,
        location: formData.location.trim(),
        professional_title: formData.professionalTitle.trim() || null,
        github_link: formData.githubLink.trim(),
        linkedin_link: formData.linkedinLink.trim() || null,
        portfolio_link: formData.portfolioLink.trim() || null,
        years_experience: formData.yearsExperience || null,
        primary_skills: formData.primarySkills,
        project_interest: formData.projectInterest.trim(),
        motivation_statement: formData.motivationStatement.trim(),
        availability_hours: formData.availabilityHours || null,
        expected_start_date: formData.expectedStartDate || null,
        additional_comments: formData.additionalComments.trim() || null,
        project_id: projectId || null
      };

      console.log('Form data being submitted:', submissionData);
      const result = await submitProjectApplication(submissionData);

      if (result.success) {
        setIsSubmitted(true);
      } else {
        setErrors({ submit: result.error || 'Failed to submit application' });
      }
    } catch (error) {
      console.error('Error submitting application:', error);
      setErrors({ submit: 'An unexpected error occurred. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const nextStep = () => {
    if (validateStep(currentStep) && currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <div className="bg-white p-12 rounded-2xl shadow-lg border border-gray-100">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-6" />
              <h1 className="text-3xl font-bold text-black mb-4 font-jost">
                Application Submitted Successfully!
              </h1>
              <p className="text-lg text-gray-600 mb-8 font-jost">
                Thank you for your interest in collaborating with us. We'll review your application and get back to you within 3-5 business days.
              </p>
              <div className="bg-gray-50 p-6 rounded-lg mb-8">
                <h3 className="text-lg font-semibold text-black mb-2 font-jost">What happens next?</h3>
                <div className="space-y-2 text-left">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-700 font-jost">Our team reviews your application and portfolio</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-700 font-jost">We may schedule a brief interview call</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-700 font-jost">You'll receive an email with the decision</span>
                  </div>
                </div>
              </div>
              <div className="flex gap-4 justify-center">
                <button 
                  onClick={() => window.location.href = '/collaborate'}
                  className="bg-black text-white px-8 py-3 rounded-full hover:bg-gray-800 transition-colors duration-300 font-jost"
                >
                  View More Projects
                </button>
                <button 
                  onClick={() => window.location.href = '/'}
                  className="border-2 border-gray-300 text-gray-700 px-8 py-3 rounded-full hover:border-gray-400 hover:bg-gray-50 transition-colors duration-300 font-jost"
                >
                  Return Home
                </button>
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1 py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center bg-black text-white px-4 py-2 rounded-full text-sm mb-8 hover:scale-105 transition-all duration-300 cursor-pointer animate-fade-in font-jost">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
              Project Collaboration Application
            </div>
            
            <h1 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              Apply to Join Project
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8 font-jost">
              Tell us about yourself and why you'd like to contribute to this project. We're looking for passionate developers who want to make a real impact.
            </p>
            
            {/* Progress Indicator */}
            <div className="flex items-center justify-center mb-12">
              {[1, 2, 3].map((step) => (
                <div key={step} className="flex items-center">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300 ${
                    step <= currentStep 
                      ? 'bg-black text-white' 
                      : 'bg-gray-200 text-gray-500'
                  }`}>
                    {step}
                  </div>
                  {step < 3 && (
                    <div className={`w-16 h-1 mx-2 transition-all duration-300 ${
                      step < currentStep ? 'bg-black' : 'bg-gray-200'
                    }`}></div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Project Details Section */}
          {project && (
            <div className="mb-16">
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-8 border-b border-gray-100">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-4">
                        <h2 className="text-2xl font-bold text-black font-jost">{project.title}</h2>
                        {project.is_active && (
                          <div className="flex items-center gap-1 text-green-600">
                            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                            <span className="text-sm font-jost">Active Project</span>
                          </div>
                        )}
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
                        <div className="flex items-center gap-1">
                          <Clock size={16} />
                          <span className="font-jost">{project.duration}</span>
                        </div>
                        <div className={`px-3 py-1 rounded-full text-xs font-medium border ${
                          project.difficulty_level === 'Beginner' ? 'bg-green-100 text-green-800 border-green-200' :
                          project.difficulty_level === 'Intermediate' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                          'bg-red-100 text-red-800 border-red-200'
                        } font-jost`}>
                          {project.difficulty_level}
                        </div>
                        <div className="flex items-center gap-1 text-yellow-500">
                          <Star size={16} fill="currentColor" />
                          <span className="text-sm text-gray-600 font-jost">Featured Project</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-8">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Project Description */}
                    <div className="lg:col-span-2">
                      <h3 className="text-lg font-semibold text-black mb-4 font-jost">Project Overview</h3>
                      <p className="text-gray-700 leading-relaxed mb-6 font-jost">{project.description}</p>

                      {/* Technologies */}
                      <div className="mb-6">
                        <h4 className="text-md font-semibold text-black mb-3 font-jost">Technologies Used</h4>
                        <div className="flex flex-wrap gap-2">
                          {project.technologies.map((tech, index) => (
                            <span
                              key={index}
                              className="inline-flex items-center gap-1 px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm font-medium font-jost"
                            >
                              <Code2 size={14} />
                              {tech}
                            </span>
                          ))}
                        </div>
                      </div>

                      {/* Required Skills */}
                      <div>
                        <h4 className="text-md font-semibold text-black mb-3 font-jost">Required Skills</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          {project.required_skills.map((skill, index) => (
                            <div key={index} className="flex items-center gap-2">
                              <CheckCircle size={16} className="text-green-500" />
                              <span className="text-gray-700 text-sm font-jost">{skill}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Project Stats */}
                    <div className="lg:col-span-1">
                      <h3 className="text-lg font-semibold text-black mb-4 font-jost">Project Details</h3>
                      <div className="space-y-4">
                        <div className="bg-gray-50 rounded-lg p-4 text-center">
                          <Users size={24} className="text-black mx-auto mb-2" />
                          <div className="text-xl font-bold text-black font-jost">
                            {project.max_collaborators || '5'}
                          </div>
                          <div className="text-sm text-gray-600 font-jost">Max Team Size</div>
                        </div>

                        <div className="bg-gray-50 rounded-lg p-4 text-center">
                          <Calendar size={24} className="text-black mx-auto mb-2" />
                          <div className="text-xl font-bold text-black font-jost">
                            {project.application_deadline ?
                              new Date(project.application_deadline).toLocaleDateString() :
                              'Open'
                            }
                          </div>
                          <div className="text-sm text-gray-600 font-jost">Application Deadline</div>
                        </div>

                        <div className="bg-gray-50 rounded-lg p-4 text-center">
                          <Target size={24} className="text-black mx-auto mb-2" />
                          <div className="text-xl font-bold text-black font-jost">3</div>
                          <div className="text-sm text-gray-600 font-jost">Current Team Members</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Trust Signals Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100 sticky top-8">
                <h3 className="text-xl font-semibold text-black mb-6 font-jost">Why Collaborate with iREME?</h3>
                
                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <Clock className="w-6 h-6 text-blue-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-black font-jost">Flexible Schedule</h4>
                      <p className="text-sm text-gray-600 font-jost">Work on your own schedule and pace</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <Users className="w-6 h-6 text-green-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-black font-jost">Mentorship</h4>
                      <p className="text-sm text-gray-600 font-jost">Learn from experienced developers</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <Star className="w-6 h-6 text-yellow-500 mt-1" />
                    <div>
                      <h4 className="font-semibold text-black font-jost">Portfolio Building</h4>
                      <p className="text-sm text-gray-600 font-jost">Build real-world projects for your portfolio</p>
                    </div>
                  </div>
                </div>

                <div className="mt-8 p-4 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-700 italic font-jost">
                    "Working on iREME projects helped me land my dream job. The mentorship and real-world experience were invaluable!"
                  </p>
                  <p className="text-xs text-gray-500 mt-2 font-jost">- Alex M., Former Collaborator</p>
                </div>
              </div>
            </div>

            {/* Main Form */}
            <div className="lg:col-span-2">
              <div className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100">
                <form onSubmit={handleSubmit}>
                  {/* Step 1: Personal Information */}
                  {currentStep === 1 && (
                    <div className="space-y-6">
                      <h2 className="text-2xl font-bold text-black mb-6 font-jost">Personal Information</h2>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                            Full Name *
                          </label>
                          <input
                            type="text"
                            required
                            value={formData.fullName}
                            onChange={(e) => handleInputChange('fullName', e.target.value)}
                            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost ${
                              errors.fullName ? 'border-red-500' : 'border-gray-300'
                            }`}
                            placeholder="Your full name"
                          />
                          {errors.fullName && (
                            <p className="text-red-500 text-sm mt-1 font-jost">{errors.fullName}</p>
                          )}
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                            Email Address *
                          </label>
                          <input
                            type="email"
                            required
                            value={formData.email}
                            onChange={(e) => handleInputChange('email', e.target.value)}
                            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost ${
                              errors.email ? 'border-red-500' : 'border-gray-300'
                            }`}
                            placeholder="<EMAIL>"
                          />
                          {errors.email && (
                            <p className="text-red-500 text-sm mt-1 font-jost">{errors.email}</p>
                          )}
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                            Phone Number
                          </label>
                          <input
                            type="tel"
                            value={formData.phone}
                            onChange={(e) => handleInputChange('phone', e.target.value)}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost"
                            placeholder="+250 XXX XXX XXX"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                            Location *
                          </label>
                          <input
                            type="text"
                            required
                            value={formData.location}
                            onChange={(e) => handleInputChange('location', e.target.value)}
                            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost ${
                              errors.location ? 'border-red-500' : 'border-gray-300'
                            }`}
                            placeholder="City, Country"
                          />
                          {errors.location && (
                            <p className="text-red-500 text-sm mt-1 font-jost">{errors.location}</p>
                          )}
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                          Professional Title
                        </label>
                        <input
                          type="text"
                          value={formData.professionalTitle}
                          onChange={(e) => handleInputChange('professionalTitle', e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost"
                          placeholder="e.g., Frontend Developer, Student, etc."
                        />
                      </div>
                    </div>
                  )}

                  {/* Step 2: Technical Information */}
                  {currentStep === 2 && (
                    <div className="space-y-6">
                      <h2 className="text-2xl font-bold text-black mb-6 font-jost">Technical Information</h2>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                            GitHub Profile *
                          </label>
                          <div className="relative">
                            <Github className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                            <input
                              type="url"
                              required
                              value={formData.githubLink}
                              onChange={(e) => handleInputChange('githubLink', e.target.value)}
                              className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost ${
                                errors.githubLink ? 'border-red-500' : 'border-gray-300'
                              }`}
                              placeholder="https://github.com/yourusername"
                            />
                          </div>
                          {errors.githubLink && (
                            <p className="text-red-500 text-sm mt-1 font-jost">{errors.githubLink}</p>
                          )}
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                            LinkedIn Profile
                          </label>
                          <div className="relative">
                            <Linkedin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                            <input
                              type="url"
                              value={formData.linkedinLink}
                              onChange={(e) => handleInputChange('linkedinLink', e.target.value)}
                              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost"
                              placeholder="https://linkedin.com/in/yourusername"
                            />
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                            Portfolio Website
                          </label>
                          <div className="relative">
                            <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                            <input
                              type="url"
                              value={formData.portfolioLink}
                              onChange={(e) => handleInputChange('portfolioLink', e.target.value)}
                              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost"
                              placeholder="https://yourportfolio.com"
                            />
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2 font-jost">
                            Years of Experience *
                          </label>
                          <select
                            required
                            value={formData.yearsExperience}
                            onChange={(e) => handleInputChange('yearsExperience', e.target.value)}
                            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost ${
                              errors.yearsExperience ? 'border-red-500' : 'border-gray-300'
                            }`}
                          >
                            <option value="">Select experience level</option>
                            {experienceOptions.map((option) => (
                              <option key={option} value={option}>{option}</option>
                            ))}
                          </select>
                          {errors.yearsExperience && (
                            <p className="text-red-500 text-sm mt-1 font-jost">{errors.yearsExperience}</p>
                          )}
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-3 font-jost">
                          Primary Skills * (Select all that apply)
                        </label>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 max-h-64 overflow-y-auto border border-gray-200 rounded-lg p-4">
                          {skillOptions.map((skill) => (
                            <button
                              key={skill}
                              type="button"
                              onClick={() => handleSkillToggle(skill)}
                              className={`p-2 text-sm rounded-lg border transition-all duration-300 font-jost ${
                                formData.primarySkills.includes(skill)
                                  ? 'bg-black text-white border-black'
                                  : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'
                              }`}
                            >
                              {skill}
                            </button>
                          ))}
                        </div>
                        {errors.primarySkills && (
                          <p className="text-red-500 text-sm mt-1 font-jost">{errors.primarySkills}</p>
                        )}
                        <p className="text-sm text-gray-500 mt-2 font-jost">
                          Selected: {formData.primarySkills.length} skills
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Step 3: Application Details */}
                  {currentStep === 3 && (
                    <div className="space-y-6">
                      <h2 className="text-2xl font-bold text-black mb-6 font-jost">Application Details</h2>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-3 font-jost">
                          Motivation Statement * (200-500 characters)
                        </label>
                        <textarea
                          required
                          rows={6}
                          value={formData.motivationStatement}
                          onChange={(e) => handleInputChange('motivationStatement', e.target.value)}
                          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost ${
                            errors.motivationStatement ? 'border-red-500' : 'border-gray-300'
                          }`}
                          placeholder="Tell us why you're interested in this project and what you hope to contribute and learn..."
                        />
                        <div className="flex justify-between items-center mt-1">
                          {errors.motivationStatement && (
                            <p className="text-red-500 text-sm font-jost">{errors.motivationStatement}</p>
                          )}
                          <p className={`text-sm ml-auto font-jost ${
                            formData.motivationStatement.length < 200 ? 'text-red-500' :
                            formData.motivationStatement.length > 500 ? 'text-red-500' : 'text-gray-500'
                          }`}>
                            {formData.motivationStatement.length}/500 characters
                          </p>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-3 font-jost">
                            Availability *
                          </label>
                          <select
                            required
                            value={formData.availabilityHours}
                            onChange={(e) => handleInputChange('availabilityHours', e.target.value)}
                            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost ${
                              errors.availabilityHours ? 'border-red-500' : 'border-gray-300'
                            }`}
                          >
                            <option value="">Select availability</option>
                            {availabilityOptions.map((option) => (
                              <option key={option} value={option}>{option}</option>
                            ))}
                          </select>
                          {errors.availabilityHours && (
                            <p className="text-red-500 text-sm mt-1 font-jost">{errors.availabilityHours}</p>
                          )}
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-3 font-jost">
                            Expected Start Date *
                          </label>
                          <input
                            type="date"
                            required
                            value={formData.expectedStartDate}
                            onChange={(e) => handleInputChange('expectedStartDate', e.target.value)}
                            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost ${
                              errors.expectedStartDate ? 'border-red-500' : 'border-gray-300'
                            }`}
                            min={new Date().toISOString().split('T')[0]}
                          />
                          {errors.expectedStartDate && (
                            <p className="text-red-500 text-sm mt-1 font-jost">{errors.expectedStartDate}</p>
                          )}
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-3 font-jost">
                          Additional Comments
                        </label>
                        <textarea
                          rows={4}
                          value={formData.additionalComments}
                          onChange={(e) => handleInputChange('additionalComments', e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost"
                          placeholder="Any additional information you'd like to share..."
                        />
                      </div>

                      <div className="bg-gray-50 p-6 rounded-lg">
                        <h3 className="text-lg font-semibold text-black mb-3 font-jost">Application Summary</h3>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-600 font-jost">Name:</span>
                            <span className="text-black font-jost">{formData.fullName || 'Not provided'}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600 font-jost">Experience:</span>
                            <span className="text-black font-jost">{formData.yearsExperience || 'Not specified'}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600 font-jost">Skills:</span>
                            <span className="text-black font-jost">{formData.primarySkills.length} selected</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600 font-jost">Availability:</span>
                            <span className="text-black font-jost">{formData.availabilityHours || 'Not specified'}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Navigation Buttons */}
                  <div className="flex justify-between mt-8">
                    <button
                      type="button"
                      onClick={prevStep}
                      disabled={currentStep === 1}
                      className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 font-jost ${
                        currentStep === 1
                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                      }`}
                    >
                      Previous
                    </button>
                    
                    {currentStep < totalSteps ? (
                      <button
                        type="button"
                        onClick={nextStep}
                        className="bg-black text-white px-8 py-3 rounded-lg hover:bg-gray-800 transition-all duration-300 flex items-center gap-2 font-jost"
                      >
                        Next Step
                        <ArrowRight size={18} />
                      </button>
                    ) : (
                      <button
                        type="submit"
                        disabled={isSubmitting}
                        className="bg-black text-white px-8 py-3 rounded-lg hover:bg-gray-800 transition-all duration-300 flex items-center gap-2 disabled:opacity-50 font-jost"
                      >
                        {isSubmitting ? 'Submitting...' : 'Submit Application'}
                        {!isSubmitting && <ArrowRight size={18} />}
                      </button>
                    )}
                  </div>

                  {/* Error Display */}
                  {errors.submit && (
                    <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                      <p className="text-red-600 text-sm font-jost">{errors.submit}</p>
                    </div>
                  )}
                </form>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default ProjectApplicationPage;

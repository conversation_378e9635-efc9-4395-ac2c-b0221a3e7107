/**
 * Security Monitoring and Threat Detection System
 * Monitors for security threats, suspicious activities, and compliance violations
 */

interface SecurityEvent {
  id: string;
  type: SecurityEventType;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: number;
  userId?: string;
  userEmail?: string;
  ipAddress: string;
  userAgent: string;
  resource: string;
  action: string;
  details: Record<string, any>;
  resolved: boolean;
  resolvedBy?: string;
  resolvedAt?: number;
}

type SecurityEventType = 
  | 'failed_login'
  | 'suspicious_login'
  | 'brute_force_attempt'
  | 'unauthorized_access'
  | 'privilege_escalation'
  | 'data_breach_attempt'
  | 'sql_injection_attempt'
  | 'xss_attempt'
  | 'csrf_attempt'
  | 'rate_limit_exceeded'
  | 'suspicious_file_upload'
  | 'account_lockout'
  | 'password_policy_violation'
  | 'session_hijack_attempt'
  | 'malicious_request';

interface SecurityRule {
  id: string;
  name: string;
  description: string;
  type: SecurityEventType;
  enabled: boolean;
  threshold: number;
  timeWindow: number; // in minutes
  action: 'log' | 'alert' | 'block' | 'lockout';
  severity: 'low' | 'medium' | 'high' | 'critical';
}

interface ThreatIntelligence {
  ipAddress: string;
  reputation: 'good' | 'suspicious' | 'malicious';
  country: string;
  lastSeen: number;
  threatTypes: string[];
  confidence: number;
}

class SecurityMonitor {
  private events: SecurityEvent[] = [];
  private rules: SecurityRule[] = [];
  private threatIntel: Map<string, ThreatIntelligence> = new Map();
  private rateLimits: Map<string, { count: number; resetTime: number }> = new Map();
  private suspiciousIPs: Set<string> = new Set();
  private blockedIPs: Set<string> = new Set();

  constructor() {
    this.initializeDefaultRules();
    this.startCleanupInterval();
  }

  /**
   * Initialize default security rules
   */
  private initializeDefaultRules() {
    this.rules = [
      {
        id: 'failed-login-rule',
        name: 'Failed Login Attempts',
        description: 'Detect multiple failed login attempts from same IP',
        type: 'failed_login',
        enabled: true,
        threshold: 5,
        timeWindow: 15,
        action: 'lockout',
        severity: 'high',
      },
      {
        id: 'brute-force-rule',
        name: 'Brute Force Detection',
        description: 'Detect brute force attacks across multiple accounts',
        type: 'brute_force_attempt',
        enabled: true,
        threshold: 10,
        timeWindow: 10,
        action: 'block',
        severity: 'critical',
      },
      {
        id: 'rate-limit-rule',
        name: 'Rate Limit Exceeded',
        description: 'Detect excessive API requests from single source',
        type: 'rate_limit_exceeded',
        enabled: true,
        threshold: 100,
        timeWindow: 5,
        action: 'block',
        severity: 'medium',
      },
      {
        id: 'privilege-escalation-rule',
        name: 'Privilege Escalation',
        description: 'Detect attempts to access unauthorized resources',
        type: 'privilege_escalation',
        enabled: true,
        threshold: 3,
        timeWindow: 30,
        action: 'alert',
        severity: 'critical',
      },
      {
        id: 'suspicious-login-rule',
        name: 'Suspicious Login Location',
        description: 'Detect logins from unusual locations',
        type: 'suspicious_login',
        enabled: true,
        threshold: 1,
        timeWindow: 60,
        action: 'alert',
        severity: 'medium',
      },
    ];
  }

  /**
   * Record a security event
   */
  recordEvent(
    type: SecurityEventType,
    details: {
      userId?: string;
      userEmail?: string;
      ipAddress: string;
      userAgent: string;
      resource: string;
      action: string;
      metadata?: Record<string, any>;
    }
  ): SecurityEvent {
    const event: SecurityEvent = {
      id: this.generateEventId(),
      type,
      severity: this.calculateSeverity(type, details),
      timestamp: Date.now(),
      userId: details.userId,
      userEmail: details.userEmail,
      ipAddress: details.ipAddress,
      userAgent: details.userAgent,
      resource: details.resource,
      action: details.action,
      details: details.metadata || {},
      resolved: false,
    };

    this.events.push(event);
    this.processEvent(event);
    
    return event;
  }

  /**
   * Process security event and apply rules
   */
  private processEvent(event: SecurityEvent) {
    const applicableRules = this.rules.filter(rule => 
      rule.enabled && rule.type === event.type
    );

    for (const rule of applicableRules) {
      const recentEvents = this.getRecentEvents(
        event.type,
        event.ipAddress,
        rule.timeWindow
      );

      if (recentEvents.length >= rule.threshold) {
        this.triggerRuleAction(rule, event, recentEvents);
      }
    }

    // Check threat intelligence
    this.checkThreatIntelligence(event);
  }

  /**
   * Get recent events of specific type from IP
   */
  private getRecentEvents(
    type: SecurityEventType,
    ipAddress: string,
    timeWindowMinutes: number
  ): SecurityEvent[] {
    const cutoffTime = Date.now() - (timeWindowMinutes * 60 * 1000);
    
    return this.events.filter(event =>
      event.type === type &&
      event.ipAddress === ipAddress &&
      event.timestamp >= cutoffTime
    );
  }

  /**
   * Trigger rule action
   */
  private triggerRuleAction(
    rule: SecurityRule,
    event: SecurityEvent,
    recentEvents: SecurityEvent[]
  ) {
    switch (rule.action) {
      case 'log':
        console.warn(`Security rule triggered: ${rule.name}`, { rule, event });
        break;
      
      case 'alert':
        this.sendSecurityAlert(rule, event, recentEvents);
        break;
      
      case 'block':
        this.blockIP(event.ipAddress, rule.name);
        this.sendSecurityAlert(rule, event, recentEvents);
        break;
      
      case 'lockout':
        if (event.userId) {
          this.lockoutUser(event.userId, rule.name);
        }
        this.sendSecurityAlert(rule, event, recentEvents);
        break;
    }
  }

  /**
   * Check threat intelligence for IP
   */
  private checkThreatIntelligence(event: SecurityEvent) {
    const intel = this.threatIntel.get(event.ipAddress);
    
    if (intel && intel.reputation === 'malicious') {
      this.blockIP(event.ipAddress, 'Malicious IP detected');
      this.sendThreatAlert(event, intel);
    } else if (intel && intel.reputation === 'suspicious') {
      this.suspiciousIPs.add(event.ipAddress);
      this.sendThreatAlert(event, intel);
    }
  }

  /**
   * Block IP address
   */
  private blockIP(ipAddress: string, reason: string) {
    this.blockedIPs.add(ipAddress);
    
    // In a real implementation, this would update firewall rules
    console.warn(`IP blocked: ${ipAddress} - ${reason}`);
    
    // Record the block event
    this.recordEvent('malicious_request', {
      ipAddress,
      userAgent: 'System',
      resource: 'security',
      action: 'ip_blocked',
      metadata: { reason },
    });
  }

  /**
   * Lockout user account
   */
  private lockoutUser(userId: string, reason: string) {
    // In a real implementation, this would update the user's account status
    console.warn(`User locked out: ${userId} - ${reason}`);
    
    // Record the lockout event
    this.recordEvent('account_lockout', {
      userId,
      ipAddress: '127.0.0.1',
      userAgent: 'System',
      resource: 'auth',
      action: 'account_locked',
      metadata: { reason },
    });
  }

  /**
   * Send security alert
   */
  private sendSecurityAlert(
    rule: SecurityRule,
    event: SecurityEvent,
    recentEvents: SecurityEvent[]
  ) {
    const alert = {
      type: 'security_alert',
      rule: rule.name,
      severity: rule.severity,
      event,
      recentEvents: recentEvents.length,
      timestamp: Date.now(),
    };

    // In a real implementation, this would send to alerting system
    console.error('Security Alert:', alert);
    
    // Could integrate with services like:
    // - Email notifications
    // - Slack/Teams webhooks
    // - PagerDuty
    // - Security Information and Event Management (SIEM) systems
  }

  /**
   * Send threat intelligence alert
   */
  private sendThreatAlert(event: SecurityEvent, intel: ThreatIntelligence) {
    const alert = {
      type: 'threat_alert',
      event,
      intelligence: intel,
      timestamp: Date.now(),
    };

    console.error('Threat Alert:', alert);
  }

  /**
   * Check if IP is blocked
   */
  isIPBlocked(ipAddress: string): boolean {
    return this.blockedIPs.has(ipAddress);
  }

  /**
   * Check if IP is suspicious
   */
  isIPSuspicious(ipAddress: string): boolean {
    return this.suspiciousIPs.has(ipAddress);
  }

  /**
   * Check rate limit for IP
   */
  checkRateLimit(ipAddress: string, limit: number, windowMinutes: number): boolean {
    const key = `${ipAddress}:${windowMinutes}`;
    const now = Date.now();
    const windowMs = windowMinutes * 60 * 1000;
    
    const current = this.rateLimits.get(key);
    
    if (!current || now > current.resetTime) {
      this.rateLimits.set(key, { count: 1, resetTime: now + windowMs });
      return true;
    }
    
    if (current.count >= limit) {
      this.recordEvent('rate_limit_exceeded', {
        ipAddress,
        userAgent: 'Unknown',
        resource: 'api',
        action: 'rate_limit_exceeded',
        metadata: { limit, window: windowMinutes, count: current.count },
      });
      return false;
    }
    
    current.count++;
    return true;
  }

  /**
   * Update threat intelligence
   */
  updateThreatIntelligence(intel: ThreatIntelligence) {
    this.threatIntel.set(intel.ipAddress, intel);
  }

  /**
   * Get security events
   */
  getEvents(filters?: {
    type?: SecurityEventType;
    severity?: string;
    resolved?: boolean;
    limit?: number;
    offset?: number;
  }): SecurityEvent[] {
    let filteredEvents = [...this.events];
    
    if (filters?.type) {
      filteredEvents = filteredEvents.filter(e => e.type === filters.type);
    }
    
    if (filters?.severity) {
      filteredEvents = filteredEvents.filter(e => e.severity === filters.severity);
    }
    
    if (filters?.resolved !== undefined) {
      filteredEvents = filteredEvents.filter(e => e.resolved === filters.resolved);
    }
    
    // Sort by timestamp (newest first)
    filteredEvents.sort((a, b) => b.timestamp - a.timestamp);
    
    // Apply pagination
    const offset = filters?.offset || 0;
    const limit = filters?.limit || 100;
    
    return filteredEvents.slice(offset, offset + limit);
  }

  /**
   * Resolve security event
   */
  resolveEvent(eventId: string, resolvedBy: string): boolean {
    const event = this.events.find(e => e.id === eventId);
    
    if (event && !event.resolved) {
      event.resolved = true;
      event.resolvedBy = resolvedBy;
      event.resolvedAt = Date.now();
      return true;
    }
    
    return false;
  }

  /**
   * Get security statistics
   */
  getSecurityStats(timeRangeHours: number = 24): {
    totalEvents: number;
    eventsByType: Record<string, number>;
    eventsBySeverity: Record<string, number>;
    blockedIPs: number;
    suspiciousIPs: number;
    resolvedEvents: number;
    unresolvedEvents: number;
  } {
    const cutoffTime = Date.now() - (timeRangeHours * 60 * 60 * 1000);
    const recentEvents = this.events.filter(e => e.timestamp >= cutoffTime);
    
    const eventsByType: Record<string, number> = {};
    const eventsBySeverity: Record<string, number> = {};
    let resolvedEvents = 0;
    
    recentEvents.forEach(event => {
      eventsByType[event.type] = (eventsByType[event.type] || 0) + 1;
      eventsBySeverity[event.severity] = (eventsBySeverity[event.severity] || 0) + 1;
      
      if (event.resolved) {
        resolvedEvents++;
      }
    });
    
    return {
      totalEvents: recentEvents.length,
      eventsByType,
      eventsBySeverity,
      blockedIPs: this.blockedIPs.size,
      suspiciousIPs: this.suspiciousIPs.size,
      resolvedEvents,
      unresolvedEvents: recentEvents.length - resolvedEvents,
    };
  }

  /**
   * Generate unique event ID
   */
  private generateEventId(): string {
    return `sec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Calculate event severity
   */
  private calculateSeverity(
    type: SecurityEventType,
    details: any
  ): 'low' | 'medium' | 'high' | 'critical' {
    const severityMap: Record<SecurityEventType, 'low' | 'medium' | 'high' | 'critical'> = {
      failed_login: 'low',
      suspicious_login: 'medium',
      brute_force_attempt: 'critical',
      unauthorized_access: 'high',
      privilege_escalation: 'critical',
      data_breach_attempt: 'critical',
      sql_injection_attempt: 'critical',
      xss_attempt: 'high',
      csrf_attempt: 'high',
      rate_limit_exceeded: 'medium',
      suspicious_file_upload: 'high',
      account_lockout: 'medium',
      password_policy_violation: 'low',
      session_hijack_attempt: 'critical',
      malicious_request: 'high',
    };
    
    return severityMap[type] || 'medium';
  }

  /**
   * Start cleanup interval
   */
  private startCleanupInterval() {
    setInterval(() => {
      this.cleanupOldEvents();
      this.cleanupRateLimits();
    }, 60 * 60 * 1000); // Run every hour
  }

  /**
   * Cleanup old events (keep last 30 days)
   */
  private cleanupOldEvents() {
    const cutoffTime = Date.now() - (30 * 24 * 60 * 60 * 1000);
    this.events = this.events.filter(event => event.timestamp >= cutoffTime);
  }

  /**
   * Cleanup expired rate limits
   */
  private cleanupRateLimits() {
    const now = Date.now();
    for (const [key, limit] of this.rateLimits.entries()) {
      if (now > limit.resetTime) {
        this.rateLimits.delete(key);
      }
    }
  }
}

// Create singleton instance
export const securityMonitor = new SecurityMonitor();

// Middleware function for Express/Next.js
export function securityMiddleware(req: any, res: any, next: any) {
  const ipAddress = req.ip || req.connection.remoteAddress || '127.0.0.1';
  const userAgent = req.headers['user-agent'] || 'Unknown';
  
  // Check if IP is blocked
  if (securityMonitor.isIPBlocked(ipAddress)) {
    return res.status(403).json({ error: 'Access denied' });
  }
  
  // Check rate limit
  if (!securityMonitor.checkRateLimit(ipAddress, 100, 5)) {
    return res.status(429).json({ error: 'Rate limit exceeded' });
  }
  
  next();
}

export default SecurityMonitor;

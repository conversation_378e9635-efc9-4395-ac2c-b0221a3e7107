'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAdminAuth, ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';
import { AdminPermissionWrapper } from '@/components/admin/auth/AdminProtectedRoute';
import {
  LayoutDashboard,
  FileText,
  Briefcase,
  PenTool,
  Image,
  Users,
  MessageSquare,
  UserCheck,
  BarChart3,
  Settings,
  Shield,
  Activity,
  ChevronLeft,
  ChevronRight,
  LogOut,
  Menu,
  X,
} from 'lucide-react';

interface AdminSidebarProps {
  isCollapsed: boolean;
  onToggleCollapse: () => void;
  isMobileOpen: boolean;
  onMobileToggle: () => void;
}

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  permission?: string;
  badge?: string;
  children?: NavItem[];
}

const navigationItems: NavItem[] = [
  {
    name: 'Dashboard',
    href: '/admin/dashboard',
    icon: LayoutDashboard,
  },
  {
    name: 'Content Management',
    href: '/admin/content',
    icon: FileText,
    permission: ADMIN_PERMISSIONS.CONTENT_VIEW,
    children: [
      {
        name: 'Templates',
        href: '/admin/templates',
        icon: FileText,
        permission: ADMIN_PERMISSIONS.TEMPLATES_VIEW,
      },
      {
        name: 'Services',
        href: '/admin/services',
        icon: Briefcase,
        permission: ADMIN_PERMISSIONS.SERVICES_VIEW,
      },
      {
        name: 'Blog Posts',
        href: '/admin/blog',
        icon: PenTool,
        permission: ADMIN_PERMISSIONS.BLOG_VIEW,
      },
      {
        name: 'Media Library',
        href: '/admin/media',
        icon: Image,
        permission: ADMIN_PERMISSIONS.MEDIA_VIEW,
      },
    ],
  },
  {
    name: 'Lead Management',
    href: '/admin/leads',
    icon: Users,
    permission: ADMIN_PERMISSIONS.LEADS_VIEW,
    children: [
      {
        name: 'Quote Requests',
        href: '/admin/leads/quotes',
        icon: MessageSquare,
        permission: ADMIN_PERMISSIONS.LEADS_VIEW,
        badge: '5',
      },
      {
        name: 'Contact Forms',
        href: '/admin/leads/contacts',
        icon: MessageSquare,
        permission: ADMIN_PERMISSIONS.LEADS_VIEW,
        badge: '2',
      },
      {
        name: 'Customers',
        href: '/admin/customers',
        icon: UserCheck,
        permission: ADMIN_PERMISSIONS.CUSTOMERS_VIEW,
      },
    ],
  },
  {
    name: 'Analytics',
    href: '/admin/analytics',
    icon: BarChart3,
    permission: ADMIN_PERMISSIONS.ANALYTICS_VIEW,
  },
  {
    name: 'Administration',
    href: '/admin/admin',
    icon: Settings,
    permission: ADMIN_PERMISSIONS.USERS_VIEW,
    children: [
      {
        name: 'Users',
        href: '/admin/users',
        icon: Shield,
        permission: ADMIN_PERMISSIONS.USERS_VIEW,
      },
      {
        name: 'Performance',
        href: '/admin/performance',
        icon: Activity,
        permission: ADMIN_PERMISSIONS.SYSTEM_SETTINGS,
      },
      {
        name: 'Security',
        href: '/admin/security',
        icon: Shield,
        permission: ADMIN_PERMISSIONS.SYSTEM_SETTINGS,
      },
      {
        name: 'System Settings',
        href: '/admin/settings',
        icon: Settings,
        permission: ADMIN_PERMISSIONS.SYSTEM_SETTINGS,
      },
    ],
  },
];

export default function AdminSidebar({
  isCollapsed,
  onToggleCollapse,
  isMobileOpen,
  onMobileToggle,
}: AdminSidebarProps) {
  const pathname = usePathname();
  const { adminUser, signOut } = useAdminAuth();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev =>
      prev.includes(itemName)
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    );
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const isItemActive = (href: string) => {
    if (href === '/admin/dashboard') {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  const renderNavItem = (item: NavItem, level = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.name);
    const isActive = isItemActive(item.href);

    // If item has permission requirement, wrap in permission check
    const itemContent = (
      <div key={item.name}>
        {hasChildren ? (
          <button
            onClick={() => toggleExpanded(item.name)}
            className={`w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
              isActive
                ? 'bg-black text-white'
                : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
            } ${level > 0 ? 'ml-4' : ''}`}
          >
            <div className="flex items-center">
              <item.icon className={`${isCollapsed ? 'h-5 w-5' : 'h-4 w-4 mr-3'} flex-shrink-0`} />
              {!isCollapsed && (
                <>
                  <span>{item.name}</span>
                  {item.badge && (
                    <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      {item.badge}
                    </span>
                  )}
                </>
              )}
            </div>
            {!isCollapsed && (
              <ChevronRight
                className={`h-4 w-4 transition-transform duration-200 ${
                  isExpanded ? 'rotate-90' : ''
                }`}
              />
            )}
          </button>
        ) : (
          <Link
            href={item.href}
            className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
              isActive
                ? 'bg-black text-white'
                : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
            } ${level > 0 ? 'ml-4' : ''}`}
            onClick={() => {
              if (window.innerWidth < 768) {
                onMobileToggle();
              }
            }}
          >
            <item.icon className={`${isCollapsed ? 'h-5 w-5' : 'h-4 w-4 mr-3'} flex-shrink-0`} />
            {!isCollapsed && (
              <>
                <span>{item.name}</span>
                {item.badge && (
                  <span className="ml-auto inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    {item.badge}
                  </span>
                )}
              </>
            )}
          </Link>
        )}

        {/* Render children if expanded */}
        {hasChildren && isExpanded && !isCollapsed && (
          <div className="mt-1 space-y-1">
            {item.children!.map(child => renderNavItem(child, level + 1))}
          </div>
        )}
      </div>
    );

    // Wrap with permission check if required
    if (item.permission) {
      return (
        <AdminPermissionWrapper key={item.name} permission={item.permission}>
          {itemContent}
        </AdminPermissionWrapper>
      );
    }

    return itemContent;
  };

  const sidebarContent = (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        {!isCollapsed && (
          <div className="flex items-center">
            <div className="h-8 w-8 bg-black rounded-lg flex items-center justify-center mr-3">
              <span className="text-white text-sm font-bold">iR</span>
            </div>
            <div>
              <h1 className="text-lg font-bold text-gray-900 font-jost">Admin</h1>
              <p className="text-xs text-gray-500 font-jost">Dashboard</p>
            </div>
          </div>
        )}
        
        {/* Desktop collapse toggle */}
        <button
          onClick={onToggleCollapse}
          className="hidden md:flex p-1.5 rounded-lg hover:bg-gray-100 transition-colors"
        >
          {isCollapsed ? (
            <ChevronRight className="h-4 w-4 text-gray-600" />
          ) : (
            <ChevronLeft className="h-4 w-4 text-gray-600" />
          )}
        </button>

        {/* Mobile close button */}
        <button
          onClick={onMobileToggle}
          className="md:hidden p-1.5 rounded-lg hover:bg-gray-100 transition-colors"
        >
          <X className="h-5 w-5 text-gray-600" />
        </button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-4 space-y-2 overflow-y-auto">
        {navigationItems.map(item => renderNavItem(item))}
      </nav>

      {/* User Info & Sign Out */}
      <div className="border-t border-gray-200 p-4">
        {!isCollapsed && adminUser && (
          <div className="mb-3">
            <div className="flex items-center">
              <div className="h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                <span className="text-gray-600 text-sm font-medium">
                  {adminUser.first_name?.[0]}{adminUser.last_name?.[0]}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate font-jost">
                  {adminUser.first_name} {adminUser.last_name}
                </p>
                <p className="text-xs text-gray-500 truncate font-jost capitalize">
                  {adminUser.role.replace('_', ' ')}
                </p>
              </div>
            </div>
          </div>
        )}
        
        <button
          onClick={handleSignOut}
          className={`w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100 hover:text-gray-900 transition-all duration-200 font-jost ${
            isCollapsed ? 'justify-center' : ''
          }`}
        >
          <LogOut className={`h-4 w-4 ${isCollapsed ? '' : 'mr-3'} flex-shrink-0`} />
          {!isCollapsed && <span>Sign Out</span>}
        </button>
      </div>
    </div>
  );

  return (
    <>
      {/* Mobile overlay */}
      {isMobileOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={onMobileToggle}
        />
      )}

      {/* Desktop sidebar */}
      <div
        className={`hidden md:flex flex-col bg-white border-r border-gray-200 transition-all duration-300 ${
          isCollapsed ? 'w-16' : 'w-64'
        }`}
      >
        {sidebarContent}
      </div>

      {/* Mobile sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 md:hidden ${
          isMobileOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        {sidebarContent}
      </div>
    </>
  );
}

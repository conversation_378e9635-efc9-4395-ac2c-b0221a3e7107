/**
 * Performance Monitoring and Analytics
 * Tracks page load times, component render times, and user interactions
 */

import React from 'react';

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  url?: string;
  userId?: string;
  metadata?: Record<string, any>;
}

interface PageLoadMetrics {
  url: string;
  loadTime: number;
  domContentLoaded: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  cumulativeLayoutShift: number;
  firstInputDelay: number;
  timeToInteractive: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private isEnabled: boolean = true;
  private batchSize: number = 10;
  private flushInterval: number = 30000; // 30 seconds
  private flushTimer?: NodeJS.Timeout;

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeWebVitals();
      this.startAutoFlush();
    }
  }

  /**
   * Initialize Web Vitals monitoring
   */
  private initializeWebVitals() {
    // Monitor Core Web Vitals
    this.observePerformanceEntries();
    this.monitorNavigationTiming();
    this.monitorResourceTiming();
  }

  /**
   * Observe performance entries
   */
  private observePerformanceEntries() {
    if ('PerformanceObserver' in window) {
      // Largest Contentful Paint
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.recordMetric('largest_contentful_paint', lastEntry.startTime);
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

      // First Input Delay
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          this.recordMetric('first_input_delay', entry.processingStart - entry.startTime);
        });
      });
      fidObserver.observe({ entryTypes: ['first-input'] });

      // Cumulative Layout Shift
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0;
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        this.recordMetric('cumulative_layout_shift', clsValue);
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
    }
  }

  /**
   * Monitor navigation timing
   */
  private monitorNavigationTiming() {
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        if (navigation) {
          const metrics: PageLoadMetrics = {
            url: window.location.href,
            loadTime: navigation.loadEventEnd - navigation.navigationStart,
            domContentLoaded: navigation.domContentLoadedEventEnd - navigation.navigationStart,
            firstContentfulPaint: this.getFirstContentfulPaint(),
            largestContentfulPaint: 0, // Will be updated by observer
            cumulativeLayoutShift: 0, // Will be updated by observer
            firstInputDelay: 0, // Will be updated by observer
            timeToInteractive: this.calculateTimeToInteractive(navigation),
          };

          this.recordPageLoadMetrics(metrics);
        }
      }, 0);
    });
  }

  /**
   * Monitor resource timing
   */
  private monitorResourceTiming() {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        if (entry.duration > 1000) { // Only track slow resources (>1s)
          this.recordMetric('slow_resource', entry.duration, {
            name: entry.name,
            type: (entry as any).initiatorType,
          });
        }
      });
    });
    observer.observe({ entryTypes: ['resource'] });
  }

  /**
   * Get First Contentful Paint
   */
  private getFirstContentfulPaint(): number {
    const paintEntries = performance.getEntriesByType('paint');
    const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint');
    return fcpEntry ? fcpEntry.startTime : 0;
  }

  /**
   * Calculate Time to Interactive
   */
  private calculateTimeToInteractive(navigation: PerformanceNavigationTiming): number {
    // Simplified TTI calculation
    return navigation.domInteractive - navigation.navigationStart;
  }

  /**
   * Record a performance metric
   */
  recordMetric(name: string, value: number, metadata?: Record<string, any>) {
    if (!this.isEnabled) return;

    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      metadata,
    };

    this.metrics.push(metric);

    // Auto-flush if batch size reached
    if (this.metrics.length >= this.batchSize) {
      this.flush();
    }
  }

  /**
   * Record page load metrics
   */
  recordPageLoadMetrics(metrics: PageLoadMetrics) {
    Object.entries(metrics).forEach(([key, value]) => {
      if (typeof value === 'number' && value > 0) {
        this.recordMetric(`page_${key}`, value, { url: metrics.url });
      }
    });
  }

  /**
   * Record component render time
   */
  recordComponentRender(componentName: string, renderTime: number) {
    this.recordMetric('component_render', renderTime, { component: componentName });
  }

  /**
   * Record API call performance
   */
  recordApiCall(endpoint: string, duration: number, status: number) {
    this.recordMetric('api_call', duration, { endpoint, status });
  }

  /**
   * Record user interaction
   */
  recordInteraction(action: string, element: string, duration?: number) {
    this.recordMetric('user_interaction', duration || 0, { action, element });
  }

  /**
   * Start auto-flush timer
   */
  private startAutoFlush() {
    this.flushTimer = setInterval(() => {
      if (this.metrics.length > 0) {
        this.flush();
      }
    }, this.flushInterval);
  }

  /**
   * Flush metrics to analytics service
   */
  async flush() {
    if (this.metrics.length === 0) return;

    const metricsToSend = [...this.metrics];
    this.metrics = [];

    try {
      // Send to analytics service
      await this.sendMetrics(metricsToSend);
    } catch (error) {
      console.error('Failed to send performance metrics:', error);
      // Re-add metrics to queue for retry
      this.metrics.unshift(...metricsToSend);
    }
  }

  /**
   * Send metrics to analytics service
   */
  private async sendMetrics(metrics: PerformanceMetric[]) {
    // In a real implementation, this would send to your analytics service
    // For now, we'll just log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.group('Performance Metrics');
      metrics.forEach(metric => {
        console.log(`${metric.name}: ${metric.value}ms`, metric.metadata);
      });
      console.groupEnd();
    }

    // Example API call to analytics service
    /*
    await fetch('/api/analytics/performance', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ metrics }),
    });
    */
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary() {
    const summary: Record<string, { count: number; avg: number; max: number; min: number }> = {};

    this.metrics.forEach(metric => {
      if (!summary[metric.name]) {
        summary[metric.name] = { count: 0, avg: 0, max: 0, min: Infinity };
      }

      const s = summary[metric.name];
      s.count++;
      s.max = Math.max(s.max, metric.value);
      s.min = Math.min(s.min, metric.value);
      s.avg = (s.avg * (s.count - 1) + metric.value) / s.count;
    });

    return summary;
  }

  /**
   * Enable/disable monitoring
   */
  setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
  }

  /**
   * Cleanup
   */
  destroy() {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    this.flush(); // Final flush
  }
}

// Create singleton instance
export const performanceMonitor = new PerformanceMonitor();

// React Hook for component performance monitoring
export function usePerformanceMonitor(componentName: string) {
  const startTime = performance.now();

  React.useEffect(() => {
    return () => {
      const renderTime = performance.now() - startTime;
      performanceMonitor.recordComponentRender(componentName, renderTime);
    };
  }, [componentName, startTime]);

  return {
    recordInteraction: (action: string, element: string) => {
      performanceMonitor.recordInteraction(action, element);
    },
    recordApiCall: (endpoint: string, duration: number, status: number) => {
      performanceMonitor.recordApiCall(endpoint, duration, status);
    },
  };
}

// Higher-order component for automatic performance monitoring
export function withPerformanceMonitoring<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName?: string
) {
  const displayName = componentName || WrappedComponent.displayName || WrappedComponent.name || 'Component';

  const WithPerformanceMonitoring = (props: P) => {
    usePerformanceMonitor(displayName);
    return <WrappedComponent {...props} />;
  };

  WithPerformanceMonitoring.displayName = `withPerformanceMonitoring(${displayName})`;
  return WithPerformanceMonitoring;
}

export default performanceMonitor;

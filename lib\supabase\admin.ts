// =====================================================
// ADMIN DASHBOARD SUPABASE CLIENT
// Professional-grade Supabase integration for admin operations
// =====================================================

import { createClient } from '@supabase/supabase-js';
import { createBrowserClient, createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import type { 
  AdminUser, 
  AdminTemplate, 
  AdminService, 
  AdminBlogPost,
  AdminQuoteRequest,
  AdminContactSubmission,
  AdminCustomer,
  AdminAnalyticsEvent,
  AdminBusinessMetrics,
  AdminSystemSetting,
  AdminAuditLog,
  AdminEmailTemplate,
  DashboardStats,
  TopPerformingContent,
  LeadFollowUp,
  PaginatedResponse
} from '@/lib/types/admin';

// =====================================================
// CLIENT CONFIGURATION
// =====================================================

export function createAdminClient() {
  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );
}

export async function createAdminServerClient() {
  const cookieStore = await cookies();

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: any) {
          cookieStore.set({ name, value, ...options });
        },
        remove(name: string, options: any) {
          cookieStore.set({ name, value: '', ...options });
        },
      },
    }
  );
}

// =====================================================
// AUTHENTICATION FUNCTIONS
// =====================================================

export class AdminAuthService {
  private supabase = createAdminClient();

  async signIn(email: string, password: string) {
    try {
      const { data, error } = await this.supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      // Check if user is an admin
      const { data: adminUser, error: adminError } = await this.supabase
        .from('admin_users')
        .select('*')
        .eq('email', email)
        .eq('is_active', true)
        .single();

      if (adminError || !adminUser) {
        await this.supabase.auth.signOut();
        throw new Error('Access denied. Admin privileges required.');
      }

      // Log the login action
      await this.logAdminAction('login', 'admin_user', adminUser.id, adminUser.email);

      return { user: data.user, adminUser };
    } catch (error) {
      console.error('Admin sign in error:', error);
      throw error;
    }
  }

  async signOut() {
    try {
      await this.logAdminAction('logout', 'admin_user');
      const { error } = await this.supabase.auth.signOut();
      if (error) throw error;
    } catch (error) {
      console.error('Admin sign out error:', error);
      throw error;
    }
  }

  async getCurrentAdminUser(): Promise<AdminUser | null> {
    try {
      const { data: { user } } = await this.supabase.auth.getUser();
      if (!user) return null;

      const { data: adminUser, error } = await this.supabase
        .from('admin_users')
        .select('*')
        .eq('supabase_user_id', user.id)
        .eq('is_active', true)
        .single();

      if (error || !adminUser) return null;

      return adminUser;
    } catch (error) {
      console.error('Get current admin user error:', error);
      return null;
    }
  }

  private async logAdminAction(action: string, resourceType: string, resourceId?: string, resourceName?: string) {
    try {
      await this.supabase.rpc('log_admin_action', {
        p_action: action,
        p_resource_type: resourceType,
        p_resource_id: resourceId,
        p_resource_name: resourceName,
      });
    } catch (error) {
      console.error('Failed to log admin action:', error);
    }
  }
}

// =====================================================
// CONTENT MANAGEMENT FUNCTIONS
// =====================================================

export class AdminContentService {
  private supabase = createAdminClient();

  // Templates Management
  async getTemplates(page = 1, limit = 10, filters?: any): Promise<PaginatedResponse<AdminTemplate>> {
    try {
      let query = this.supabase
        .from('admin_templates')
        .select('*', { count: 'exact' });

      if (filters?.status) {
        query = query.eq('status', filters.status);
      }

      if (filters?.category) {
        query = query.eq('category', filters.category);
      }

      if (filters?.search) {
        query = query.textSearch('name,short_description', filters.search);
      }

      const { data, error, count } = await query
        .order('created_at', { ascending: false })
        .range((page - 1) * limit, page * limit - 1);

      if (error) throw error;

      return {
        data: data || [],
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit),
        },
      };
    } catch (error) {
      console.error('Get templates error:', error);
      throw error;
    }
  }

  async createTemplate(template: Partial<AdminTemplate>): Promise<AdminTemplate> {
    try {
      const { data, error } = await this.supabase
        .from('admin_templates')
        .insert([template])
        .select()
        .single();

      if (error) throw error;

      await this.logAction('create', 'template', data.id, data.name);
      return data;
    } catch (error) {
      console.error('Create template error:', error);
      throw error;
    }
  }

  async updateTemplate(id: string, updates: Partial<AdminTemplate>): Promise<AdminTemplate> {
    try {
      const { data, error } = await this.supabase
        .from('admin_templates')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      await this.logAction('update', 'template', data.id, data.name);
      return data;
    } catch (error) {
      console.error('Update template error:', error);
      throw error;
    }
  }

  async deleteTemplate(id: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('admin_templates')
        .delete()
        .eq('id', id);

      if (error) throw error;

      await this.logAction('delete', 'template', id);
    } catch (error) {
      console.error('Delete template error:', error);
      throw error;
    }
  }

  // Services Management
  async getServices(page = 1, limit = 10, filters?: any): Promise<PaginatedResponse<AdminService>> {
    try {
      let query = this.supabase
        .from('admin_services')
        .select('*', { count: 'exact' });

      if (filters?.status) {
        query = query.eq('status', filters.status);
      }

      if (filters?.search) {
        query = query.textSearch('title,description', filters.search);
      }

      const { data, error, count } = await query
        .order('sort_order', { ascending: true })
        .range((page - 1) * limit, page * limit - 1);

      if (error) throw error;

      return {
        data: data || [],
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit),
        },
      };
    } catch (error) {
      console.error('Get services error:', error);
      throw error;
    }
  }

  async createService(service: Partial<AdminService>): Promise<AdminService> {
    try {
      const { data, error } = await this.supabase
        .from('admin_services')
        .insert([service])
        .select()
        .single();

      if (error) throw error;

      await this.logAction('create', 'service', data.id, data.title);
      return data;
    } catch (error) {
      console.error('Create service error:', error);
      throw error;
    }
  }

  async updateService(id: string, updates: Partial<AdminService>): Promise<AdminService> {
    try {
      const { data, error } = await this.supabase
        .from('admin_services')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      await this.logAction('update', 'service', data.id, data.title);
      return data;
    } catch (error) {
      console.error('Update service error:', error);
      throw error;
    }
  }

  // Blog Posts Management
  async getBlogPosts(page = 1, limit = 10, filters?: any): Promise<PaginatedResponse<AdminBlogPost>> {
    try {
      let query = this.supabase
        .from('admin_blog_posts')
        .select('*', { count: 'exact' });

      if (filters?.status) {
        query = query.eq('status', filters.status);
      }

      if (filters?.category) {
        query = query.eq('category', filters.category);
      }

      if (filters?.search) {
        query = query.textSearch('title,excerpt,content', filters.search);
      }

      const { data, error, count } = await query
        .order('created_at', { ascending: false })
        .range((page - 1) * limit, page * limit - 1);

      if (error) throw error;

      return {
        data: data || [],
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit),
        },
      };
    } catch (error) {
      console.error('Get blog posts error:', error);
      throw error;
    }
  }

  async createBlogPost(post: Partial<AdminBlogPost>): Promise<AdminBlogPost> {
    try {
      const { data, error } = await this.supabase
        .from('admin_blog_posts')
        .insert([post])
        .select()
        .single();

      if (error) throw error;

      await this.logAction('create', 'blog_post', data.id, data.title);
      return data;
    } catch (error) {
      console.error('Create blog post error:', error);
      throw error;
    }
  }

  private async logAction(action: string, resourceType: string, resourceId?: string, resourceName?: string) {
    try {
      await this.supabase.rpc('log_admin_action', {
        p_action: action,
        p_resource_type: resourceType,
        p_resource_id: resourceId,
        p_resource_name: resourceName,
      });
    } catch (error) {
      console.error('Failed to log admin action:', error);
    }
  }
}

// =====================================================
// LEAD MANAGEMENT FUNCTIONS
// =====================================================

export class AdminLeadService {
  private supabase = createAdminClient();

  async getQuoteRequests(page = 1, limit = 10, filters?: any): Promise<PaginatedResponse<AdminQuoteRequest>> {
    try {
      let query = this.supabase
        .from('admin_quote_requests')
        .select('*', { count: 'exact' });

      if (filters?.status) {
        query = query.eq('status', filters.status);
      }

      if (filters?.priority) {
        query = query.eq('priority', filters.priority);
      }

      if (filters?.assignedTo) {
        query = query.eq('assigned_to', filters.assignedTo);
      }

      const { data, error, count } = await query
        .order('created_at', { ascending: false })
        .range((page - 1) * limit, page * limit - 1);

      if (error) throw error;

      return {
        data: data || [],
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit),
        },
      };
    } catch (error) {
      console.error('Get quote requests error:', error);
      throw error;
    }
  }

  async updateQuoteRequest(id: string, updates: Partial<AdminQuoteRequest>): Promise<AdminQuoteRequest> {
    try {
      const { data, error } = await this.supabase
        .from('admin_quote_requests')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      await this.logAction('update', 'quote_request', data.id, data.full_name);
      return data;
    } catch (error) {
      console.error('Update quote request error:', error);
      throw error;
    }
  }

  async getLeadsRequiringFollowup(): Promise<LeadFollowUp[]> {
    try {
      const { data, error } = await this.supabase.rpc('get_leads_requiring_followup');

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Get leads requiring followup error:', error);
      throw error;
    }
  }

  private async logAction(action: string, resourceType: string, resourceId?: string, resourceName?: string) {
    try {
      await this.supabase.rpc('log_admin_action', {
        p_action: action,
        p_resource_type: resourceType,
        p_resource_id: resourceId,
        p_resource_name: resourceName,
      });
    } catch (error) {
      console.error('Failed to log admin action:', error);
    }
  }
}

// =====================================================
// ANALYTICS FUNCTIONS
// =====================================================

export class AdminAnalyticsService {
  private supabase = createAdminClient();

  async getDashboardStats(startDate?: string, endDate?: string): Promise<DashboardStats> {
    try {
      const { data, error } = await this.supabase.rpc('get_dashboard_analytics', {
        p_start_date: startDate,
        p_end_date: endDate,
      });

      if (error) throw error;

      return data[0] || {
        totalPageViews: 0,
        uniqueVisitors: 0,
        avgSessionDuration: 0,
        bounceRate: 0,
        newLeads: 0,
        conversionRate: 0,
        totalRevenue: 0,
        avgProjectValue: 0,
      };
    } catch (error) {
      console.error('Get dashboard stats error:', error);
      throw error;
    }
  }

  async getTopPerformingContent(limit = 10, contentType = 'all'): Promise<TopPerformingContent[]> {
    try {
      const { data, error } = await this.supabase.rpc('get_top_performing_content', {
        p_limit: limit,
        p_content_type: contentType,
      });

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Get top performing content error:', error);
      throw error;
    }
  }

  async trackEvent(event: Partial<AdminAnalyticsEvent>): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('admin_analytics_events')
        .insert([event]);

      if (error) throw error;
    } catch (error) {
      console.error('Track event error:', error);
      throw error;
    }
  }
}

// =====================================================
// EXPORT SERVICES
// =====================================================

export const adminAuth = new AdminAuthService();
export const adminContent = new AdminContentService();
export const adminLeads = new AdminLeadService();
export const adminAnalytics = new AdminAnalyticsService();

'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import AdminProtectedRoute from '@/components/admin/auth/AdminProtectedRoute';
import AdminLayout from '@/components/admin/layout/AdminLayout';
import CustomerList from '@/components/admin/customers/CustomerList';
import { ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';
import { AdminCustomer } from '@/lib/types/admin';

export default function AdminCustomersPage() {
  const router = useRouter();

  const handleView = (customer: AdminCustomer) => {
    router.push(`/admin/customers/${customer.id}`);
  };

  const handleEdit = (customer: AdminCustomer) => {
    router.push(`/admin/customers/${customer.id}/edit`);
  };

  return (
    <AdminProtectedRoute requiredPermission={ADMIN_PERMISSIONS.CUSTOMERS_VIEW}>
      <AdminLayout 
        title="Customers" 
        subtitle="Manage your customer relationships and accounts"
      >
        <CustomerList onView={handleView} onEdit={handleEdit} />
      </AdminLayout>
    </AdminProtectedRoute>
  );
}

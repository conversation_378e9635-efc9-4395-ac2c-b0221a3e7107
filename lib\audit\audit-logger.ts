/**
 * Comprehensive Audit Logging System
 * Tracks all user actions, system events, and data changes for compliance
 */

interface AuditLog {
  id: string;
  timestamp: number;
  userId?: string;
  userEmail?: string;
  sessionId?: string;
  ipAddress: string;
  userAgent: string;
  action: AuditAction;
  resource: string;
  resourceId?: string;
  details: Record<string, any>;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  success: boolean;
  errorMessage?: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  category: AuditCategory;
  tags: string[];
}

type AuditAction = 
  // Authentication actions
  | 'login' | 'logout' | 'login_failed' | 'password_changed' | 'password_reset'
  | 'two_factor_enabled' | 'two_factor_disabled' | 'account_locked' | 'account_unlocked'
  
  // User management actions
  | 'user_created' | 'user_updated' | 'user_deleted' | 'user_role_changed'
  | 'user_permissions_changed' | 'user_activated' | 'user_deactivated'
  
  // Content management actions
  | 'template_created' | 'template_updated' | 'template_deleted' | 'template_published'
  | 'service_created' | 'service_updated' | 'service_deleted' | 'service_published'
  | 'blog_post_created' | 'blog_post_updated' | 'blog_post_deleted' | 'blog_post_published'
  | 'media_uploaded' | 'media_deleted' | 'media_updated'
  
  // Lead management actions
  | 'lead_created' | 'lead_updated' | 'lead_deleted' | 'lead_assigned'
  | 'lead_status_changed' | 'lead_contacted' | 'lead_converted'
  | 'customer_created' | 'customer_updated' | 'customer_deleted'
  
  // System actions
  | 'settings_updated' | 'backup_created' | 'backup_restored'
  | 'system_maintenance' | 'cache_cleared' | 'database_migration'
  
  // Security actions
  | 'security_event' | 'access_denied' | 'privilege_escalation_attempt'
  | 'suspicious_activity' | 'data_export' | 'data_import'
  
  // API actions
  | 'api_call' | 'api_error' | 'rate_limit_exceeded';

type AuditCategory = 
  | 'authentication' | 'authorization' | 'user_management' | 'content_management'
  | 'lead_management' | 'system_administration' | 'security' | 'api' | 'data_access';

interface AuditFilter {
  userId?: string;
  action?: AuditAction;
  category?: AuditCategory;
  resource?: string;
  severity?: string;
  success?: boolean;
  startDate?: number;
  endDate?: number;
  ipAddress?: string;
  tags?: string[];
  limit?: number;
  offset?: number;
}

interface AuditStats {
  totalLogs: number;
  logsByAction: Record<string, number>;
  logsByCategory: Record<string, number>;
  logsBySeverity: Record<string, number>;
  successRate: number;
  topUsers: Array<{ userId: string; userEmail: string; count: number }>;
  topResources: Array<{ resource: string; count: number }>;
  recentActivity: AuditLog[];
}

class AuditLogger {
  private logs: AuditLog[] = [];
  private maxLogs: number = 10000;
  private retentionDays: number = 365;
  private batchSize: number = 100;
  private flushInterval: number = 60000; // 1 minute
  private pendingLogs: AuditLog[] = [];
  private flushTimer?: NodeJS.Timeout;

  constructor() {
    this.startAutoFlush();
  }

  /**
   * Log an audit event
   */
  log(
    action: AuditAction,
    resource: string,
    details: {
      userId?: string;
      userEmail?: string;
      sessionId?: string;
      ipAddress: string;
      userAgent: string;
      resourceId?: string;
      oldValues?: Record<string, any>;
      newValues?: Record<string, any>;
      success?: boolean;
      errorMessage?: string;
      metadata?: Record<string, any>;
      tags?: string[];
    }
  ): AuditLog {
    const auditLog: AuditLog = {
      id: this.generateLogId(),
      timestamp: Date.now(),
      userId: details.userId,
      userEmail: details.userEmail,
      sessionId: details.sessionId,
      ipAddress: details.ipAddress,
      userAgent: details.userAgent,
      action,
      resource,
      resourceId: details.resourceId,
      details: details.metadata || {},
      oldValues: details.oldValues,
      newValues: details.newValues,
      success: details.success !== false,
      errorMessage: details.errorMessage,
      severity: this.calculateSeverity(action, details.success !== false),
      category: this.categorizeAction(action),
      tags: details.tags || [],
    };

    this.pendingLogs.push(auditLog);

    // Auto-flush if batch size reached
    if (this.pendingLogs.length >= this.batchSize) {
      this.flush();
    }

    return auditLog;
  }

  /**
   * Log authentication event
   */
  logAuth(
    action: Extract<AuditAction, 'login' | 'logout' | 'login_failed' | 'password_changed' | 'password_reset'>,
    details: {
      userId?: string;
      userEmail?: string;
      sessionId?: string;
      ipAddress: string;
      userAgent: string;
      success?: boolean;
      errorMessage?: string;
      metadata?: Record<string, any>;
    }
  ): AuditLog {
    return this.log(action, 'authentication', {
      ...details,
      tags: ['authentication', 'security'],
    });
  }

  /**
   * Log user management event
   */
  logUserManagement(
    action: Extract<AuditAction, 'user_created' | 'user_updated' | 'user_deleted' | 'user_role_changed'>,
    targetUserId: string,
    details: {
      userId: string;
      userEmail: string;
      ipAddress: string;
      userAgent: string;
      oldValues?: Record<string, any>;
      newValues?: Record<string, any>;
      success?: boolean;
      errorMessage?: string;
    }
  ): AuditLog {
    return this.log(action, 'user', {
      ...details,
      resourceId: targetUserId,
      tags: ['user_management', 'admin'],
    });
  }

  /**
   * Log content management event
   */
  logContentManagement(
    action: Extract<AuditAction, 'template_created' | 'template_updated' | 'service_created' | 'blog_post_published'>,
    contentId: string,
    details: {
      userId: string;
      userEmail: string;
      ipAddress: string;
      userAgent: string;
      oldValues?: Record<string, any>;
      newValues?: Record<string, any>;
      success?: boolean;
      errorMessage?: string;
    }
  ): AuditLog {
    return this.log(action, this.getResourceFromAction(action), {
      ...details,
      resourceId: contentId,
      tags: ['content_management'],
    });
  }

  /**
   * Log security event
   */
  logSecurity(
    action: Extract<AuditAction, 'security_event' | 'access_denied' | 'suspicious_activity'>,
    details: {
      userId?: string;
      userEmail?: string;
      ipAddress: string;
      userAgent: string;
      resource: string;
      resourceId?: string;
      metadata?: Record<string, any>;
      severity?: 'info' | 'warning' | 'error' | 'critical';
    }
  ): AuditLog {
    const log = this.log(action, details.resource, {
      ...details,
      tags: ['security', 'threat_detection'],
    });

    // Override severity if provided
    if (details.severity) {
      log.severity = details.severity;
    }

    return log;
  }

  /**
   * Log API call
   */
  logApiCall(
    endpoint: string,
    method: string,
    details: {
      userId?: string;
      userEmail?: string;
      ipAddress: string;
      userAgent: string;
      statusCode: number;
      responseTime: number;
      requestBody?: any;
      responseBody?: any;
      errorMessage?: string;
    }
  ): AuditLog {
    return this.log('api_call', 'api', {
      ...details,
      resourceId: `${method} ${endpoint}`,
      success: details.statusCode < 400,
      metadata: {
        method,
        endpoint,
        statusCode: details.statusCode,
        responseTime: details.responseTime,
        requestBody: details.requestBody,
        responseBody: details.responseBody,
      },
      tags: ['api', 'integration'],
    });
  }

  /**
   * Get audit logs with filtering
   */
  getLogs(filter: AuditFilter = {}): AuditLog[] {
    let filteredLogs = [...this.logs];

    // Apply filters
    if (filter.userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === filter.userId);
    }

    if (filter.action) {
      filteredLogs = filteredLogs.filter(log => log.action === filter.action);
    }

    if (filter.category) {
      filteredLogs = filteredLogs.filter(log => log.category === filter.category);
    }

    if (filter.resource) {
      filteredLogs = filteredLogs.filter(log => log.resource === filter.resource);
    }

    if (filter.severity) {
      filteredLogs = filteredLogs.filter(log => log.severity === filter.severity);
    }

    if (filter.success !== undefined) {
      filteredLogs = filteredLogs.filter(log => log.success === filter.success);
    }

    if (filter.startDate) {
      filteredLogs = filteredLogs.filter(log => log.timestamp >= filter.startDate!);
    }

    if (filter.endDate) {
      filteredLogs = filteredLogs.filter(log => log.timestamp <= filter.endDate!);
    }

    if (filter.ipAddress) {
      filteredLogs = filteredLogs.filter(log => log.ipAddress === filter.ipAddress);
    }

    if (filter.tags && filter.tags.length > 0) {
      filteredLogs = filteredLogs.filter(log => 
        filter.tags!.some(tag => log.tags.includes(tag))
      );
    }

    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => b.timestamp - a.timestamp);

    // Apply pagination
    const offset = filter.offset || 0;
    const limit = filter.limit || 100;

    return filteredLogs.slice(offset, offset + limit);
  }

  /**
   * Get audit statistics
   */
  getStats(timeRangeHours: number = 24): AuditStats {
    const cutoffTime = Date.now() - (timeRangeHours * 60 * 60 * 1000);
    const recentLogs = this.logs.filter(log => log.timestamp >= cutoffTime);

    const logsByAction: Record<string, number> = {};
    const logsByCategory: Record<string, number> = {};
    const logsBySeverity: Record<string, number> = {};
    const userCounts: Record<string, { email: string; count: number }> = {};
    const resourceCounts: Record<string, number> = {};
    let successCount = 0;

    recentLogs.forEach(log => {
      // Count by action
      logsByAction[log.action] = (logsByAction[log.action] || 0) + 1;

      // Count by category
      logsByCategory[log.category] = (logsByCategory[log.category] || 0) + 1;

      // Count by severity
      logsBySeverity[log.severity] = (logsBySeverity[log.severity] || 0) + 1;

      // Count by user
      if (log.userId && log.userEmail) {
        if (!userCounts[log.userId]) {
          userCounts[log.userId] = { email: log.userEmail, count: 0 };
        }
        userCounts[log.userId].count++;
      }

      // Count by resource
      resourceCounts[log.resource] = (resourceCounts[log.resource] || 0) + 1;

      // Count successes
      if (log.success) {
        successCount++;
      }
    });

    // Top users
    const topUsers = Object.entries(userCounts)
      .map(([userId, data]) => ({ userId, userEmail: data.email, count: data.count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Top resources
    const topResources = Object.entries(resourceCounts)
      .map(([resource, count]) => ({ resource, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Recent activity (last 10 logs)
    const recentActivity = recentLogs.slice(0, 10);

    return {
      totalLogs: recentLogs.length,
      logsByAction,
      logsByCategory,
      logsBySeverity,
      successRate: recentLogs.length > 0 ? (successCount / recentLogs.length) * 100 : 0,
      topUsers,
      topResources,
      recentActivity,
    };
  }

  /**
   * Export audit logs
   */
  exportLogs(filter: AuditFilter = {}, format: 'json' | 'csv' = 'json'): string {
    const logs = this.getLogs(filter);

    if (format === 'csv') {
      return this.exportToCSV(logs);
    }

    return JSON.stringify(logs, null, 2);
  }

  /**
   * Export logs to CSV format
   */
  private exportToCSV(logs: AuditLog[]): string {
    const headers = [
      'ID', 'Timestamp', 'User ID', 'User Email', 'IP Address',
      'Action', 'Resource', 'Resource ID', 'Success', 'Severity',
      'Category', 'Error Message', 'Tags'
    ];

    const rows = logs.map(log => [
      log.id,
      new Date(log.timestamp).toISOString(),
      log.userId || '',
      log.userEmail || '',
      log.ipAddress,
      log.action,
      log.resource,
      log.resourceId || '',
      log.success.toString(),
      log.severity,
      log.category,
      log.errorMessage || '',
      log.tags.join(';')
    ]);

    return [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');
  }

  /**
   * Flush pending logs
   */
  private flush() {
    if (this.pendingLogs.length === 0) return;

    // Add to main logs array
    this.logs.push(...this.pendingLogs);
    this.pendingLogs = [];

    // Enforce max logs limit
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // In a real implementation, this would persist to database
    this.persistLogs();
  }

  /**
   * Persist logs to storage
   */
  private async persistLogs() {
    // In a real implementation, this would save to database
    // For now, we'll just log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`Audit: Persisted ${this.pendingLogs.length} audit logs`);
    }
  }

  /**
   * Start auto-flush timer
   */
  private startAutoFlush() {
    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.flushInterval);
  }

  /**
   * Generate unique log ID
   */
  private generateLogId(): string {
    return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Calculate severity based on action and success
   */
  private calculateSeverity(action: AuditAction, success: boolean): 'info' | 'warning' | 'error' | 'critical' {
    if (!success) {
      const criticalActions = ['login_failed', 'access_denied', 'privilege_escalation_attempt', 'data_export'];
      if (criticalActions.includes(action)) {
        return 'critical';
      }
      return 'error';
    }

    const warningActions = ['password_changed', 'user_role_changed', 'settings_updated'];
    if (warningActions.includes(action)) {
      return 'warning';
    }

    return 'info';
  }

  /**
   * Categorize action
   */
  private categorizeAction(action: AuditAction): AuditCategory {
    const categoryMap: Record<string, AuditCategory> = {
      login: 'authentication',
      logout: 'authentication',
      login_failed: 'authentication',
      password_changed: 'authentication',
      user_created: 'user_management',
      user_updated: 'user_management',
      template_created: 'content_management',
      service_created: 'content_management',
      lead_created: 'lead_management',
      settings_updated: 'system_administration',
      security_event: 'security',
      api_call: 'api',
    };

    return categoryMap[action] || 'system_administration';
  }

  /**
   * Get resource name from action
   */
  private getResourceFromAction(action: AuditAction): string {
    if (action.includes('template')) return 'template';
    if (action.includes('service')) return 'service';
    if (action.includes('blog')) return 'blog_post';
    if (action.includes('media')) return 'media';
    if (action.includes('lead')) return 'lead';
    if (action.includes('customer')) return 'customer';
    if (action.includes('user')) return 'user';
    return 'system';
  }

  /**
   * Cleanup old logs
   */
  cleanup() {
    const cutoffTime = Date.now() - (this.retentionDays * 24 * 60 * 60 * 1000);
    this.logs = this.logs.filter(log => log.timestamp >= cutoffTime);
  }

  /**
   * Destroy audit logger
   */
  destroy() {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    this.flush(); // Final flush
  }
}

// Create singleton instance
export const auditLogger = new AuditLogger();

// React hook for audit logging
export function useAuditLogger() {
  return {
    logAuth: auditLogger.logAuth.bind(auditLogger),
    logUserManagement: auditLogger.logUserManagement.bind(auditLogger),
    logContentManagement: auditLogger.logContentManagement.bind(auditLogger),
    logSecurity: auditLogger.logSecurity.bind(auditLogger),
    logApiCall: auditLogger.logApiCall.bind(auditLogger),
    log: auditLogger.log.bind(auditLogger),
  };
}

export default AuditLogger;

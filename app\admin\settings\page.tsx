'use client';

import React from 'react';
import AdminProtectedRoute from '@/components/admin/auth/AdminProtectedRoute';
import AdminLayout from '@/components/admin/layout/AdminLayout';
import SystemSettings from '@/components/admin/settings/SystemSettings';
import { ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';

export default function AdminSettingsPage() {
  return (
    <AdminProtectedRoute requiredPermission={ADMIN_PERMISSIONS.SYSTEM_SETTINGS}>
      <AdminLayout 
        title="System Settings" 
        subtitle="Configure your admin dashboard and system preferences"
      >
        <SystemSettings />
      </AdminLayout>
    </AdminProtectedRoute>
  );
}

'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Plus, Minus } from 'lucide-react';

interface Feature {
  title: string;
  content: string;
  link: string;
}

const WhyiREMESoftHub = () => {
  const [openItems, setOpenItems] = useState<Record<number, boolean>>({});

  const toggleItem = (index: number) => {
    setOpenItems(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  const features: Feature[] = [
    {
      title: "Cutting-edge development solutions",
      content: "We leverage the latest technologies and frameworks to deliver innovative, scalable, and future-proof applications that drive your business forward.",
      link: "/solutions"
    },
    {
      title: "Expert team of seasoned developers",
      content: "Our skilled developers bring years of experience across multiple technologies, ensuring high-quality code and best practices in every project.",
      link: "/team"
    },
    {
      title: "Security you can trust",
      content: "We implement industry-standard security protocols and conduct thorough testing to protect your applications and user data from potential threats.",
      link: "/security"
    },
    {
      title: "Innovation that accelerates transformation",
      content: "Our creative approach to problem-solving and embrace of emerging technologies helps transform your ideas into powerful digital solutions.",
      link: "/innovation"
    },
    {
      title: "Most proven development expertise",
      content: "With a track record of successful projects across various industries, we deliver reliable solutions that meet deadlines and exceed expectations.",
      link: "/expertise"
    }
  ];

  return (
    <section className="bg-white py-16 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 relative">
          {/* Left Column - Title and Description */}
          <div className="space-y-6 lg:sticky lg:top-20 lg:self-start">
            <h2
              className="text-4xl sm:text-5xl font-bold text-black leading-tight text-left font-jost"
            >
              Why iREME Soft Hub?
            </h2>
            <p
              className="text-lg text-gray-700 leading-relaxed text-left font-jost"
            >
              iREME Soft Hub is the world's most innovative and reliable development partner.
              Hundreds of businesses including fast growing startups, established enterprises,
              and forward-thinking organizations choose iREME Soft Hub to build exceptional digital
              experiences, reduce development costs, and accelerate their growth.
            </p>
          </div>

          {/* Right Column - Accordion Features */}
          <div className="space-y-4">
            {features.map((feature, index) => (
              <div key={index} className="border-b border-gray-200">
                <button
                  onClick={() => toggleItem(index)}
                  className="w-full flex items-center justify-between py-4 text-left hover:bg-gray-50 transition-colors duration-200 rounded-lg px-2"
                >
                  <h3
                    className="text-lg font-semibold text-black pr-4 font-jost"
                  >
                    {feature.title}
                  </h3>
                  <div className="flex-shrink-0">
                    {openItems[index] ? (
                      <Minus size={20} className="text-black" />
                    ) : (
                      <Plus size={20} className="text-black" />
                    )}
                  </div>
                </button>
                {openItems[index] && (
                  <div className="pb-4 px-2 text-left">
                    <p
                      className="text-gray-600 leading-relaxed mb-2 font-jost"
                    >
                      {feature.content}
                    </p>
                    <Link
                      href={feature.link}
                      className="text-blue-600 hover:text-blue-800 font-medium inline-flex items-center hover:decoration-blue-800 transition-all duration-300 relative after:content-[''] after:absolute after:w-0 after:h-[2px] after:bottom-0 after:left-0 after:bg-blue-800 after:transition-all after:duration-300 hover:after:w-full font-jost"
                    >
                      Learn more about {feature.title.toLowerCase()}
                    </Link>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyiREMESoftHub;

'use client';

import React, { useState } from 'react';
import { X, Filter } from 'lucide-react';

interface FilterModalProps {
  isOpen: boolean;
  onClose: () => void;
  onApplyFilters: (filters: any) => void;
  type: 'projects' | 'jobs';
  currentFilters: any;
}

const FilterModal: React.FC<FilterModalProps> = ({
  isOpen,
  onClose,
  onApplyFilters,
  type,
  currentFilters
}) => {
  const [filters, setFilters] = useState(currentFilters);

  const projectFilters = {
    difficulty: ['Beginner', 'Intermediate', 'Advanced'],
    technologies: [
      'React', 'Vue.js', 'Angular', 'JavaScript', 'TypeScript',
      'Node.js', 'Python', 'Java', 'C#', 'PHP',
      'React Native', 'Flutter', 'Swift', 'Kotlin',
      'MongoDB', 'PostgreSQL', 'MySQL', 'Firebase',
      'AWS', 'Azure', 'Google Cloud', 'Docker'
    ],
    duration: ['1-2 months', '2-3 months', '3-4 months', '4-6 months', '6+ months']
  };

  const jobFilters = {
    employmentType: ['Full-time', 'Part-time', 'Contract'],
    experienceLevel: ['Junior', 'Mid', 'Senior'],
    locationType: ['Remote', 'On-site', 'Hybrid'],
    compensationRange: [
      'Below $500/month',
      '$500-800/month',
      '$800-1200/month',
      '$1200-2000/month',
      '$2000-3000/month',
      '$3000+/month'
    ]
  };

  const handleFilterChange = (category: string, value: string) => {
    setFilters((prev: any) => ({
      ...prev,
      [category]: prev[category] === value ? undefined : value
    }));
  };

  const handleApply = () => {
    onApplyFilters(filters);
    onClose();
  };

  const handleClear = () => {
    setFilters({});
    onApplyFilters({});
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <Filter className="w-6 h-6 text-black" />
            <h2 className="text-xl font-bold text-black font-jost">
              Filter {type === 'projects' ? 'Projects' : 'Jobs'}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X size={20} className="text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {type === 'projects' ? (
            <>
              {/* Difficulty Level */}
              <div>
                <h3 className="text-lg font-semibold text-black mb-3 font-jost">Difficulty Level</h3>
                <div className="grid grid-cols-3 gap-3">
                  {projectFilters.difficulty.map((level) => (
                    <button
                      key={level}
                      onClick={() => handleFilterChange('difficulty', level)}
                      className={`p-3 text-sm rounded-lg border transition-all duration-300 font-jost ${
                        filters.difficulty === level
                          ? 'bg-black text-white border-black'
                          : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      {level}
                    </button>
                  ))}
                </div>
              </div>

              {/* Technologies */}
              <div>
                <h3 className="text-lg font-semibold text-black mb-3 font-jost">Technologies</h3>
                <div className="grid grid-cols-3 gap-2 max-h-48 overflow-y-auto">
                  {projectFilters.technologies.map((tech) => (
                    <button
                      key={tech}
                      onClick={() => handleFilterChange('technology', tech)}
                      className={`p-2 text-sm rounded-lg border transition-all duration-300 font-jost ${
                        filters.technology === tech
                          ? 'bg-black text-white border-black'
                          : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      {tech}
                    </button>
                  ))}
                </div>
              </div>

              {/* Duration */}
              <div>
                <h3 className="text-lg font-semibold text-black mb-3 font-jost">Duration</h3>
                <div className="grid grid-cols-2 gap-3">
                  {projectFilters.duration.map((duration) => (
                    <button
                      key={duration}
                      onClick={() => handleFilterChange('duration', duration)}
                      className={`p-3 text-sm rounded-lg border transition-all duration-300 font-jost ${
                        filters.duration === duration
                          ? 'bg-black text-white border-black'
                          : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      {duration}
                    </button>
                  ))}
                </div>
              </div>
            </>
          ) : (
            <>
              {/* Employment Type */}
              <div>
                <h3 className="text-lg font-semibold text-black mb-3 font-jost">Employment Type</h3>
                <div className="grid grid-cols-3 gap-3">
                  {jobFilters.employmentType.map((type) => (
                    <button
                      key={type}
                      onClick={() => handleFilterChange('employmentType', type)}
                      className={`p-3 text-sm rounded-lg border transition-all duration-300 font-jost ${
                        filters.employmentType === type
                          ? 'bg-black text-white border-black'
                          : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      {type}
                    </button>
                  ))}
                </div>
              </div>

              {/* Experience Level */}
              <div>
                <h3 className="text-lg font-semibold text-black mb-3 font-jost">Experience Level</h3>
                <div className="grid grid-cols-3 gap-3">
                  {jobFilters.experienceLevel.map((level) => (
                    <button
                      key={level}
                      onClick={() => handleFilterChange('experienceLevel', level)}
                      className={`p-3 text-sm rounded-lg border transition-all duration-300 font-jost ${
                        filters.experienceLevel === level
                          ? 'bg-black text-white border-black'
                          : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      {level}
                    </button>
                  ))}
                </div>
              </div>

              {/* Location Type */}
              <div>
                <h3 className="text-lg font-semibold text-black mb-3 font-jost">Location Type</h3>
                <div className="grid grid-cols-3 gap-3">
                  {jobFilters.locationType.map((location) => (
                    <button
                      key={location}
                      onClick={() => handleFilterChange('locationType', location)}
                      className={`p-3 text-sm rounded-lg border transition-all duration-300 font-jost ${
                        filters.locationType === location
                          ? 'bg-black text-white border-black'
                          : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      {location}
                    </button>
                  ))}
                </div>
              </div>

              {/* Compensation Range */}
              <div>
                <h3 className="text-lg font-semibold text-black mb-3 font-jost">Compensation Range</h3>
                <div className="grid grid-cols-2 gap-3">
                  {jobFilters.compensationRange.map((range) => (
                    <button
                      key={range}
                      onClick={() => handleFilterChange('compensationRange', range)}
                      className={`p-3 text-sm rounded-lg border transition-all duration-300 font-jost ${
                        filters.compensationRange === range
                          ? 'bg-black text-white border-black'
                          : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      {range}
                    </button>
                  ))}
                </div>
              </div>
            </>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-100">
          <button
            onClick={handleClear}
            className="px-6 py-3 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors font-jost"
          >
            Clear All
          </button>
          <div className="flex gap-3">
            <button
              onClick={onClose}
              className="px-6 py-3 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors font-jost"
            >
              Cancel
            </button>
            <button
              onClick={handleApply}
              className="px-6 py-3 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors font-jost"
            >
              Apply Filters
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FilterModal;

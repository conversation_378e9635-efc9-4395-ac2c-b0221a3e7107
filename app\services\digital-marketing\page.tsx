'use client';

import React, { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import Link from 'next/link';
import { 
  ArrowRight, 
  BarChart3, 
  Search, 
  Target, 
  Mail, 
  Share2, 
  TrendingUp,
  CheckCircle,
  Clock,
  Users,
  ChevronDown,
  ChevronUp,
  Zap,
  Globe,
  Eye
} from 'lucide-react';

interface Feature {
  title: string;
  description: string;
  icon: React.ReactNode;
}

interface Package {
  name: string;
  price: string;
  description: string;
  features: string[];
  timeline: string;
  popular?: boolean;
}

export default function DigitalMarketingPage() {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  const features: Feature[] = [
    {
      title: 'SEO & Content Strategy',
      description: 'Comprehensive SEO optimization and content marketing to improve search rankings and drive organic traffic.',
      icon: <Search className="w-6 h-6" />
    },
    {
      title: 'Google Ads Management',
      description: 'Expert PPC campaign management to maximize ROI and drive qualified leads to your business.',
      icon: <Target className="w-6 h-6" />
    },
    {
      title: 'Social Media Marketing',
      description: 'Strategic social media campaigns across all platforms to build brand awareness and engagement.',
      icon: <Share2 className="w-6 h-6" />
    },
    {
      title: 'Email Marketing Automation',
      description: 'Automated email sequences and campaigns that nurture leads and convert prospects into customers.',
      icon: <Mail className="w-6 h-6" />
    },
    {
      title: 'Analytics & Reporting',
      description: 'Comprehensive tracking and reporting to measure performance and optimize marketing strategies.',
      icon: <BarChart3 className="w-6 h-6" />
    },
    {
      title: 'Conversion Optimization',
      description: 'A/B testing and optimization strategies to improve conversion rates and maximize marketing ROI.',
      icon: <TrendingUp className="w-6 h-6" />
    }
  ];

  const packages: Package[] = [
    {
      name: 'Marketing Audit',
      price: '$1,500',
      description: 'Comprehensive analysis of your current marketing efforts with actionable improvement strategies.',
      features: [
        'Website & SEO audit',
        'Competitor analysis',
        'Social media assessment',
        'Marketing strategy review',
        'Detailed recommendations report',
        '30 days support'
      ],
      timeline: '1-2 weeks'
    },
    {
      name: 'Growth Marketing',
      price: '$5,000/mo',
      description: 'Complete digital marketing solution to drive leads, increase sales, and grow your business.',
      features: [
        'SEO optimization',
        'Google Ads management',
        'Social media marketing',
        'Content creation',
        'Email marketing',
        'Monthly reporting',
        'Strategy optimization',
        'Dedicated account manager'
      ],
      timeline: '2-4 weeks setup',
      popular: true
    },
    {
      name: 'Enterprise Marketing',
      price: '$10,000+/mo',
      description: 'Advanced marketing solutions for large businesses with complex multi-channel requirements.',
      features: [
        'Multi-channel campaigns',
        'Advanced analytics setup',
        'Marketing automation',
        'Lead scoring & nurturing',
        'Custom integrations',
        'Team training',
        'Weekly strategy calls',
        'Priority support'
      ],
      timeline: '4-6 weeks setup'
    }
  ];

  const faqs = [
    {
      question: 'How long does it take to see results from digital marketing?',
      answer: 'Results vary by channel. PPC ads can show immediate results, SEO typically takes 3-6 months, and social media builds momentum over 2-4 months. We provide regular reports to track progress and optimize strategies.'
    },
    {
      question: 'Do you work with businesses in all industries?',
      answer: 'Yes! We have experience across various industries including e-commerce, SaaS, healthcare, finance, and more. We tailor our strategies to your specific industry and target audience.'
    },
    {
      question: 'What\'s included in your monthly reporting?',
      answer: 'Our reports include traffic analytics, conversion tracking, campaign performance, ROI analysis, keyword rankings, and strategic recommendations for the following month.'
    },
    {
      question: 'Can you work with our existing marketing team?',
      answer: 'Absolutely! We can complement your internal team, provide training, or take full ownership of your digital marketing efforts. We\'re flexible to work with your preferred collaboration style.'
    },
    {
      question: 'How do you measure marketing success?',
      answer: 'We focus on metrics that matter to your business: qualified leads, conversion rates, customer acquisition cost, lifetime value, and overall ROI. We set clear KPIs and track progress monthly.'
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden bg-gradient-to-br from-orange-50 to-white">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="inline-flex items-center bg-orange-600 text-white px-4 py-2 rounded-full text-sm mb-6 font-jost">
                <BarChart3 className="w-4 h-4 mr-2" />
                Digital Marketing Services
              </div>
              
              <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-black mb-6 leading-tight font-jost">
                Marketing That Generates 
                <span className="text-orange-600"> Real Results</span>
              </h1>
              
              <p className="text-xl text-gray-600 mb-8 leading-relaxed font-jost">
                We create data-driven marketing strategies that generate qualified leads and drive business growth. 
                From SEO to PPC, social media to email marketing, we deliver measurable ROI for your marketing investment.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <Link
                  href="/get-quote"
                  className="bg-black text-white px-8 py-4 rounded-full hover:bg-gray-800 transition-all duration-300 flex items-center justify-center gap-2 font-medium font-jost group"
                >
                  Get Free Quote
                  <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
                </Link>
                <Link
                  href="/project"
                  className="border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-full hover:border-gray-400 hover:bg-gray-50 transition-all duration-300 font-medium font-jost"
                >
                  View Case Studies
                </Link>
              </div>
              
              <div className="flex items-center gap-8 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  <span className="font-jost">Results in 30-90 days</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  <span className="font-jost">Marketing experts</span>
                </div>
              </div>
            </div>
            
            <div className="relative">
              <div className="bg-white p-8 rounded-2xl shadow-2xl border border-gray-100">
                <div className="space-y-6">
                  {/* Marketing Dashboard */}
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-semibold text-gray-800 font-jost">Marketing Dashboard</h3>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-xs text-gray-500">Live</span>
                    </div>
                  </div>
                  
                  {/* Key Metrics */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="text-blue-600 font-bold text-xl">+250%</div>
                      <div className="text-xs text-gray-600">Traffic Growth</div>
                      <div className="flex items-center mt-1">
                        <TrendingUp className="w-3 h-3 text-blue-500 mr-1" />
                        <span className="text-xs text-blue-600">This Month</span>
                      </div>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="text-green-600 font-bold text-xl">3.2x</div>
                      <div className="text-xs text-gray-600">Lead Generation</div>
                      <div className="flex items-center mt-1">
                        <TrendingUp className="w-3 h-3 text-green-500 mr-1" />
                        <span className="text-xs text-green-600">vs Last Quarter</span>
                      </div>
                    </div>
                  </div>
                  
                  {/* Channel Performance */}
                  <div className="space-y-3">
                    <h4 className="text-sm font-medium text-gray-700 font-jost">Channel Performance</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Search className="w-4 h-4 text-blue-500" />
                          <span className="text-sm font-jost">SEO</span>
                        </div>
                        <div className="flex-1 mx-3 bg-gray-200 h-2 rounded-full">
                          <div className="bg-blue-500 h-2 rounded-full w-4/5"></div>
                        </div>
                        <span className="text-xs text-gray-600">80%</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Target className="w-4 h-4 text-orange-500" />
                          <span className="text-sm font-jost">PPC</span>
                        </div>
                        <div className="flex-1 mx-3 bg-gray-200 h-2 rounded-full">
                          <div className="bg-orange-500 h-2 rounded-full w-3/5"></div>
                        </div>
                        <span className="text-xs text-gray-600">60%</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Share2 className="w-4 h-4 text-purple-500" />
                          <span className="text-sm font-jost">Social</span>
                        </div>
                        <div className="flex-1 mx-3 bg-gray-200 h-2 rounded-full">
                          <div className="bg-purple-500 h-2 rounded-full w-2/3"></div>
                        </div>
                        <span className="text-xs text-gray-600">65%</span>
                      </div>
                    </div>
                  </div>
                  
                  {/* ROI Indicator */}
                  <div className="bg-orange-600 text-white p-3 rounded-lg text-center">
                    <div className="font-bold text-lg">ROI: 420%</div>
                    <div className="text-sm opacity-90">Marketing Investment Return</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              Why Choose Our Digital Marketing?
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto font-jost">
              We don't just run campaigns – we create comprehensive marketing strategies that generate qualified leads,
              increase brand awareness, and deliver measurable ROI for your business.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group"
              >
                <div className="flex items-center justify-center w-12 h-12 bg-orange-600 text-white rounded-xl mb-6 group-hover:scale-110 transition-transform duration-300">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold text-black mb-4 font-jost">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed font-jost">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Packages Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              Digital Marketing Packages
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto font-jost">
              From marketing audits to comprehensive growth strategies, we have the perfect solution
              to accelerate your business growth and maximize your marketing ROI.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {packages.map((pkg, index) => (
              <div
                key={index}
                className={`bg-white p-8 rounded-2xl shadow-lg border-2 transition-all duration-300 hover:shadow-xl hover:-translate-y-1 relative ${
                  pkg.popular ? 'border-orange-600' : 'border-gray-100'
                }`}
              >
                {pkg.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="bg-orange-600 text-white px-4 py-2 rounded-full text-sm font-medium font-jost">
                      Most Popular
                    </div>
                  </div>
                )}

                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-black mb-2 font-jost">
                    {pkg.name}
                  </h3>
                  <div className="text-4xl font-bold text-black mb-2 font-jost">
                    {pkg.price}
                  </div>
                  <p className="text-gray-600 font-jost">
                    {pkg.description}
                  </p>
                </div>

                <ul className="space-y-3 mb-8">
                  {pkg.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center text-gray-600 font-jost">
                      <CheckCircle className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>

                <div className="text-center mb-6">
                  <div className="text-sm text-gray-500 font-jost">Setup: {pkg.timeline}</div>
                </div>

                <Link
                  href="/get-quote"
                  className={`w-full py-3 px-6 rounded-full transition-all duration-300 flex items-center justify-center gap-2 font-medium font-jost ${
                    pkg.popular
                      ? 'bg-orange-600 text-white hover:bg-orange-700'
                      : 'bg-black text-white hover:bg-gray-800'
                  }`}
                >
                  Get Started
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-gray-600 font-jost">
              Get answers to common questions about our digital marketing services and strategies.
            </p>
          </div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div
                key={index}
                className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"
              >
                <button
                  onClick={() => setOpenFAQ(openFAQ === index ? null : index)}
                  className="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
                >
                  <h3 className="text-lg font-semibold text-black font-jost pr-4">
                    {faq.question}
                  </h3>
                  {openFAQ === index ? (
                    <ChevronUp className="w-5 h-5 text-gray-500 flex-shrink-0" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-gray-500 flex-shrink-0" />
                  )}
                </button>

                {openFAQ === index && (
                  <div className="px-8 pb-6">
                    <p className="text-gray-600 leading-relaxed font-jost">
                      {faq.answer}
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-orange-600 to-red-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl sm:text-5xl font-bold text-white mb-6 font-jost">
            Ready to Accelerate Your Growth?
          </h2>
          <p className="text-xl text-orange-100 mb-8 leading-relaxed font-jost">
            Join hundreds of businesses that have transformed their marketing results with our proven strategies.
            Let's create a marketing plan that drives real growth for your business.
          </p>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-8">
            <Link
              href="/get-quote"
              className="bg-white text-orange-600 px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 flex items-center gap-2 font-medium font-jost group"
            >
              Get Free Marketing Audit
              <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
            </Link>
            <Link
              href="/contact-us"
              className="border-2 border-white text-white px-8 py-4 rounded-full hover:bg-white hover:text-orange-600 transition-all duration-300 font-medium font-jost"
            >
              Schedule Strategy Call
            </Link>
          </div>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-orange-100">
            <div className="flex items-center gap-2">
              <Clock className="w-5 h-5" />
              <span className="font-jost">Free audit included</span>
            </div>
            <div className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              <span className="font-jost">Measurable ROI guaranteed</span>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

# 🚀 iREME Soft Hub Admin Dashboard Setup Guide

## 📋 Overview

This guide provides step-by-step instructions for setting up the professional admin dashboard for iREME Soft Hub. The dashboard includes enterprise-grade features like role-based access control, comprehensive content management, lead tracking, and business analytics.

## 🗄️ Database Setup

### Step 1: Run Database Migrations

You need to run the admin dashboard database migrations in your Supabase project:

1. **Go to your Supabase Dashboard:**
   - Visit: https://supabase.com/dashboard/project/[your-project-id]/sql
   - Replace `[your-project-id]` with your actual project ID

2. **Run the Schema Migration:**
   - Copy the contents of `supabase/migrations/006_create_admin_dashboard_schema.sql`
   - Paste it into the SQL editor
   - Click "Run" to execute

3. **Run the Functions Migration:**
   - Copy the contents of `supabase/migrations/007_create_admin_dashboard_functions.sql`
   - Paste it into the SQL editor
   - Click "Run" to execute

### Step 2: Create Your First Admin User

After running the migrations, you need to create your first admin user:

1. **Create a Supabase Auth User:**
   ```sql
   -- In Supabase SQL editor, run:
   INSERT INTO auth.users (
     id,
     email,
     encrypted_password,
     email_confirmed_at,
     created_at,
     updated_at,
     raw_app_meta_data,
     raw_user_meta_data,
     is_super_admin,
     role
   ) VALUES (
     gen_random_uuid(),
     '<EMAIL>',
     crypt('Nike14##', gen_salt('bf')),
     NOW(),
     NOW(),
     NOW(),
     '{"provider": "email", "providers": ["email"]}',
     '{}',
     false,
     'authenticated'
   );
   ```

2. **Link to Admin Users Table:**
   ```sql
   -- Get the user ID from the previous insert and run:
   INSERT INTO admin_users (
     email,
     supabase_user_id,
     first_name,
     last_name,
     role,
     is_active,
     is_verified
   ) VALUES (
     '<EMAIL>',
     (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
     'System',
     'Administrator',
     'super_admin',
     true,
     true
   );
   ```

## 🔐 Environment Variables

Ensure your `.env.local` file has the required Supabase configuration:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## 🚀 Accessing the Admin Dashboard

### Step 1: Start the Development Server

```bash
npm run dev
```

### Step 2: Navigate to Admin Login

Visit: http://localhost:3000/admin/login

### Step 3: Sign In

Use the admin credentials you created:
- Email: `<EMAIL>`
- Password: `Nike14##`

## 🎯 Features Overview

### ✅ Authentication & Authorization
- **Role-based Access Control:** Super Admin, Admin, Editor, Viewer
- **Secure Login:** Enterprise-grade authentication with session management
- **Permission System:** Granular permissions for different resources
- **Account Security:** Login attempt tracking, account lockout protection

### ✅ Content Management System
- **Template Management:** CRUD operations for project templates
- **Service Management:** Dynamic service pages with packages and pricing
- **Blog Management:** Full-featured blog system with scheduling
- **Media Library:** Centralized asset management with optimization

### ✅ Lead & Customer Management
- **Quote Requests:** Advanced lead scoring and tracking system
- **Contact Forms:** Comprehensive contact submission management
- **Customer Database:** Complete customer relationship management
- **Follow-up System:** Automated follow-up reminders and tracking

### ✅ Analytics & Reporting
- **Business Metrics:** Real-time dashboard with key performance indicators
- **Content Analytics:** Track template views, blog engagement, service inquiries
- **Lead Analytics:** Conversion rates, lead sources, and performance metrics
- **Custom Reports:** Flexible reporting with export capabilities

### ✅ System Administration
- **User Management:** Admin user CRUD with role assignment
- **System Settings:** Configurable application settings
- **Audit Logs:** Complete activity tracking for compliance
- **Email Templates:** Automated communication system

## 🔧 Customization

### Adding New Admin Users

1. **Navigate to User Management** (when implemented)
2. **Click "Add New User"**
3. **Fill in user details and assign role**
4. **User will receive invitation email**

### Configuring Permissions

Permissions are defined in `lib/auth/admin-auth.ts`:

```typescript
export const ADMIN_PERMISSIONS = {
  CONTENT_VIEW: 'content.view',
  CONTENT_CREATE: 'content.create',
  // Add custom permissions here
};
```

### Role Customization

Modify role permissions in `ROLE_PERMISSIONS` object:

```typescript
export const ROLE_PERMISSIONS: Record<AdminUserRole, string[]> = {
  super_admin: Object.values(ADMIN_PERMISSIONS),
  admin: [/* specific permissions */],
  // Customize role permissions
};
```

## 🛡️ Security Features

### Row Level Security (RLS)
- All admin tables have RLS policies enabled
- Users can only access data based on their role and permissions
- Automatic audit logging for all admin actions

### Session Management
- Secure session tracking with IP and user agent
- Automatic session expiration
- Login attempt monitoring

### Data Protection
- Encrypted sensitive data storage
- Audit trails for compliance
- Secure password handling

## 📊 Database Schema

### Core Tables Created:
- `admin_users` - Admin user accounts and roles
- `admin_sessions` - Session tracking and security
- `admin_templates` - Template management
- `admin_services` - Service management
- `admin_blog_posts` - Blog content management
- `admin_media_library` - Media asset management
- `admin_quote_requests` - Lead management
- `admin_contact_submissions` - Contact form management
- `admin_customers` - Customer relationship management
- `admin_analytics_events` - Event tracking
- `admin_business_metrics` - Business intelligence
- `admin_system_settings` - Configuration management
- `admin_audit_logs` - Activity tracking
- `admin_email_templates` - Communication templates

## 🔍 Troubleshooting

### Common Issues:

1. **Cannot Access Admin Dashboard**
   - Verify database migrations are run
   - Check admin user is created and active
   - Ensure environment variables are set

2. **Permission Denied Errors**
   - Check user role and permissions
   - Verify RLS policies are correctly applied
   - Check audit logs for access attempts

3. **Database Connection Issues**
   - Verify Supabase URL and keys
   - Check network connectivity
   - Ensure Supabase project is active

### Getting Help:

- Check the audit logs in the admin dashboard
- Review browser console for JavaScript errors
- Verify database logs in Supabase dashboard
- Contact system administrator for access issues

## 🚀 Next Steps

1. **Complete Database Setup** - Run all migrations
2. **Create Admin Users** - Set up your admin team
3. **Configure Settings** - Customize system settings
4. **Import Content** - Migrate existing content
5. **Set Up Analytics** - Configure tracking and reporting
6. **Train Users** - Provide admin training for your team

## 📞 Support

For technical support or questions about the admin dashboard:
- Email: <EMAIL>
- Documentation: Check this file and inline code comments
- Issues: Create GitHub issues for bugs or feature requests

---

**🎉 Congratulations!** Your professional admin dashboard is now ready for use. The system provides enterprise-grade functionality for managing your iREME Soft Hub business operations.

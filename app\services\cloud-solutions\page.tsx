'use client';

import React, { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import Link from 'next/link';
import { 
  ArrowRight, 
  Cloud, 
  Zap, 
  Shield, 
  Users, 
  Server, 
  BarChart3,
  CheckCircle,
  Clock,
  Star,
  Quote,
  ChevronDown,
  ChevronUp,
  Database,
  Lock,
  TrendingUp
} from 'lucide-react';

interface Feature {
  title: string;
  description: string;
  icon: React.ReactNode;
}

interface Package {
  name: string;
  price: string;
  description: string;
  features: string[];
  timeline: string;
  popular?: boolean;
}

interface CaseStudy {
  title: string;
  client: string;
  challenge: string;
  solution: string;
  results: string[];
  image: string;
}

export default function CloudSolutionsPage() {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  const features: Feature[] = [
    {
      title: 'Multi-Cloud Architecture',
      description: 'Deploy across AWS, Google Cloud, and Azure for maximum reliability and performance optimization.',
      icon: <Cloud className="w-6 h-6" />
    },
    {
      title: 'Auto-Scaling Infrastructure',
      description: 'Automatically scale resources up or down based on demand to optimize costs and performance.',
      icon: <TrendingUp className="w-6 h-6" />
    },
    {
      title: 'Enterprise Security',
      description: 'Advanced security measures including encryption, access controls, and compliance management.',
      icon: <Lock className="w-6 h-6" />
    },
    {
      title: 'Database Management',
      description: 'Optimized database solutions with backup, replication, and performance monitoring.',
      icon: <Database className="w-6 h-6" />
    },
    {
      title: 'CI/CD Pipelines',
      description: 'Automated deployment pipelines for faster, more reliable software releases.',
      icon: <Server className="w-6 h-6" />
    },
    {
      title: 'Cost Optimization',
      description: 'Continuous monitoring and optimization to reduce cloud costs by up to 40%.',
      icon: <BarChart3 className="w-6 h-6" />
    }
  ];

  const packages: Package[] = [
    {
      name: 'Cloud Migration',
      price: '$7,500',
      description: 'Perfect for businesses moving from on-premise to cloud infrastructure.',
      features: [
        'Infrastructure assessment',
        'Migration strategy planning',
        'Single cloud platform setup',
        'Basic monitoring setup',
        'Security configuration',
        '30 days support'
      ],
      timeline: '4-6 weeks'
    },
    {
      name: 'Cloud Infrastructure',
      price: '$15,000',
      description: 'Comprehensive cloud solution for growing businesses with advanced features.',
      features: [
        'Multi-cloud architecture',
        'Auto-scaling setup',
        'CI/CD pipeline implementation',
        'Advanced monitoring',
        'Security & compliance',
        'Database optimization',
        'Cost optimization',
        '60 days support'
      ],
      timeline: '8-12 weeks',
      popular: true
    },
    {
      name: 'Enterprise Cloud',
      price: '$30,000+',
      description: 'Advanced cloud solutions for large organizations with complex requirements.',
      features: [
        'Custom cloud architecture',
        'Multi-region deployment',
        'Advanced security audit',
        'Disaster recovery setup',
        'Performance optimization',
        'Custom integrations',
        '24/7 monitoring',
        '90 days support'
      ],
      timeline: '12-20 weeks'
    }
  ];

  const caseStudies: CaseStudy[] = [
    {
      title: 'E-commerce Cloud Transformation',
      client: 'RetailGiant Corp',
      challenge: 'Legacy infrastructure couldn\'t handle traffic spikes during sales events, causing frequent downtime and lost revenue.',
      solution: 'Migrated to AWS with auto-scaling, implemented CDN, and set up monitoring for proactive issue resolution.',
      results: [
        '99.9% uptime during peak sales',
        '60% reduction in infrastructure costs',
        '3x faster page load times',
        'Zero downtime during Black Friday'
      ],
      image: '/placeholder-cloud-case-study.jpg'
    }
  ];

  const faqs = [
    {
      question: 'Which cloud platform is best for my business?',
      answer: 'The best platform depends on your specific needs, existing infrastructure, and budget. AWS offers the most services, Google Cloud excels in AI/ML, and Azure integrates well with Microsoft products. We help you choose the optimal platform based on your requirements.'
    },
    {
      question: 'How much can I save by moving to the cloud?',
      answer: 'Most businesses save 20-40% on infrastructure costs after migrating to the cloud. Savings come from reduced hardware costs, lower maintenance, and optimized resource usage. We provide detailed cost analysis during consultation.'
    },
    {
      question: 'Is my data secure in the cloud?',
      answer: 'Yes, when properly configured. Cloud providers offer enterprise-grade security that often exceeds on-premise capabilities. We implement best practices including encryption, access controls, and compliance measures to ensure your data is secure.'
    },
    {
      question: 'How long does cloud migration take?',
      answer: 'Migration timeline depends on complexity and data volume. Simple migrations take 4-8 weeks, while complex enterprise migrations can take 12-24 weeks. We provide detailed migration plans with minimal downtime.'
    },
    {
      question: 'Do you provide ongoing cloud management?',
      answer: 'Yes! We offer comprehensive managed cloud services including monitoring, optimization, security updates, and 24/7 support. Our goal is to ensure your cloud infrastructure runs smoothly so you can focus on your business.'
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden bg-gradient-to-br from-blue-50 to-white">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="inline-flex items-center bg-blue-600 text-white px-4 py-2 rounded-full text-sm mb-6 font-jost">
                <Cloud className="w-4 h-4 mr-2" />
                Cloud Solutions & DevOps
              </div>
              
              <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-black mb-6 leading-tight font-jost">
                Scalable Cloud Infrastructure 
                <span className="text-blue-600"> That Grows With You</span>
              </h1>
              
              <p className="text-xl text-gray-600 mb-8 leading-relaxed font-jost">
                We design and implement cloud solutions that reduce costs, improve performance, and scale automatically. 
                From migration to optimization, we help you leverage AWS, Google Cloud, and Azure for maximum efficiency.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <Link
                  href="/get-quote"
                  className="bg-black text-white px-8 py-4 rounded-full hover:bg-gray-800 transition-all duration-300 flex items-center justify-center gap-2 font-medium font-jost group"
                >
                  Get Free Quote
                  <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
                </Link>
                <Link
                  href="/project"
                  className="border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-full hover:border-gray-400 hover:bg-gray-50 transition-all duration-300 font-medium font-jost"
                >
                  View Case Studies
                </Link>
              </div>
              
              <div className="flex items-center gap-8 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  <span className="font-jost">4-20 weeks delivery</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  <span className="font-jost">Cloud experts team</span>
                </div>
              </div>
            </div>
            
            <div className="relative">
              <div className="bg-white p-8 rounded-2xl shadow-2xl border border-gray-100">
                <div className="space-y-6">
                  {/* Cloud Icons */}
                  <div className="flex items-center justify-between">
                    <div className="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-xs">AWS</span>
                    </div>
                    <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-xs">GCP</span>
                    </div>
                    <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-xs">Azure</span>
                    </div>
                  </div>
                  
                  {/* Server Visualization */}
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <Server className="w-5 h-5 text-gray-400" />
                      <div className="flex-1 bg-gray-200 h-2 rounded-full">
                        <div className="bg-green-500 h-2 rounded-full w-3/4"></div>
                      </div>
                      <span className="text-xs text-gray-500">75%</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Database className="w-5 h-5 text-gray-400" />
                      <div className="flex-1 bg-gray-200 h-2 rounded-full">
                        <div className="bg-blue-500 h-2 rounded-full w-1/2"></div>
                      </div>
                      <span className="text-xs text-gray-500">50%</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <BarChart3 className="w-5 h-5 text-gray-400" />
                      <div className="flex-1 bg-gray-200 h-2 rounded-full">
                        <div className="bg-purple-500 h-2 rounded-full w-5/6"></div>
                      </div>
                      <span className="text-xs text-gray-500">85%</span>
                    </div>
                  </div>
                  
                  {/* Status Indicators */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <div className="text-green-600 font-semibold">99.9%</div>
                      <div className="text-xs text-gray-600">Uptime</div>
                    </div>
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <div className="text-blue-600 font-semibold">-40%</div>
                      <div className="text-xs text-gray-600">Cost Savings</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              Why Choose Our Cloud Solutions?
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto font-jost">
              We don't just migrate to the cloud – we optimize your entire infrastructure for performance,
              security, and cost-effectiveness while ensuring seamless scalability.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group"
              >
                <div className="flex items-center justify-center w-12 h-12 bg-blue-600 text-white rounded-xl mb-6 group-hover:scale-110 transition-transform duration-300">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold text-black mb-4 font-jost">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed font-jost">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Packages Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              Cloud Solutions Packages
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto font-jost">
              From simple migrations to complex multi-cloud architectures, we have the perfect solution
              for your cloud transformation journey.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {packages.map((pkg, index) => (
              <div
                key={index}
                className={`bg-white p-8 rounded-2xl shadow-lg border-2 transition-all duration-300 hover:shadow-xl hover:-translate-y-1 relative ${
                  pkg.popular ? 'border-blue-600' : 'border-gray-100'
                }`}
              >
                {pkg.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-medium font-jost">
                      Most Popular
                    </div>
                  </div>
                )}

                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-black mb-2 font-jost">
                    {pkg.name}
                  </h3>
                  <div className="text-4xl font-bold text-black mb-2 font-jost">
                    {pkg.price}
                  </div>
                  <p className="text-gray-600 font-jost">
                    {pkg.description}
                  </p>
                </div>

                <ul className="space-y-3 mb-8">
                  {pkg.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center text-gray-600 font-jost">
                      <CheckCircle className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>

                <div className="text-center mb-6">
                  <div className="text-sm text-gray-500 font-jost">Timeline: {pkg.timeline}</div>
                </div>

                <Link
                  href="/get-quote"
                  className={`w-full py-3 px-6 rounded-full transition-all duration-300 flex items-center justify-center gap-2 font-medium font-jost ${
                    pkg.popular
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-black text-white hover:bg-gray-800'
                  }`}
                >
                  Get Started
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-600 mb-4 font-jost">
              Need a custom cloud architecture? We design solutions for unique enterprise requirements.
            </p>
            <Link
              href="/contact-us"
              className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium font-jost group"
            >
              Discuss Custom Cloud Solutions
              <ArrowRight className="w-4 h-4 ml-1 transition-transform duration-300 group-hover:translate-x-1" />
            </Link>
          </div>
        </div>
      </section>

      {/* Case Study Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              Cloud Transformation Success
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto font-jost">
              See how our cloud solutions have helped businesses reduce costs, improve performance,
              and achieve unprecedented scalability.
            </p>
          </div>

          {caseStudies.map((study, index) => (
            <div key={index} className="bg-gray-50 rounded-2xl p-8 lg:p-12">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-3xl font-bold text-black mb-4 font-jost">
                    {study.title}
                  </h3>
                  <div className="text-blue-600 font-semibold mb-6 font-jost">
                    Client: {study.client}
                  </div>

                  <div className="space-y-6">
                    <div>
                      <h4 className="text-lg font-semibold text-black mb-2 font-jost">Challenge</h4>
                      <p className="text-gray-600 font-jost">{study.challenge}</p>
                    </div>

                    <div>
                      <h4 className="text-lg font-semibold text-black mb-2 font-jost">Solution</h4>
                      <p className="text-gray-600 font-jost">{study.solution}</p>
                    </div>

                    <div>
                      <h4 className="text-lg font-semibold text-black mb-3 font-jost">Results</h4>
                      <ul className="space-y-2">
                        {study.results.map((result, idx) => (
                          <li key={idx} className="flex items-center text-gray-600 font-jost">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                            {result}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-2xl shadow-lg">
                  <div className="space-y-6">
                    {/* Cloud Transformation Results */}
                    <div className="text-center mb-4">
                      <h4 className="text-lg font-semibold text-black mb-2 font-jost">Cloud Migration Results</h4>
                      <p className="text-sm text-gray-600 font-jost">Infrastructure Transformation Impact</p>
                    </div>

                    {/* Before vs After Comparison */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-700 font-jost">Before Migration</span>
                        <span className="text-sm font-medium text-gray-700 font-jost">After Migration</span>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        {/* Before Column */}
                        <div className="space-y-3">
                          <div className="bg-red-50 p-3 rounded-lg">
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-xs text-gray-600 font-jost">Uptime</span>
                              <span className="text-sm font-bold text-red-600 font-jost">99.5%</span>
                            </div>
                            <div className="w-full bg-red-200 h-1.5 rounded-full">
                              <div className="bg-red-600 h-1.5 rounded-full w-11/12"></div>
                            </div>
                          </div>

                          <div className="bg-red-50 p-3 rounded-lg">
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-xs text-gray-600 font-jost">Monthly Cost</span>
                              <span className="text-sm font-bold text-red-600 font-jost">$50K</span>
                            </div>
                            <div className="w-full bg-red-200 h-1.5 rounded-full">
                              <div className="bg-red-600 h-1.5 rounded-full w-full"></div>
                            </div>
                          </div>

                          <div className="bg-red-50 p-3 rounded-lg">
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-xs text-gray-600 font-jost">Load Time</span>
                              <span className="text-sm font-bold text-red-600 font-jost">12s</span>
                            </div>
                            <div className="w-full bg-red-200 h-1.5 rounded-full">
                              <div className="bg-red-600 h-1.5 rounded-full w-4/5"></div>
                            </div>
                          </div>
                        </div>

                        {/* After Column */}
                        <div className="space-y-3">
                          <div className="bg-green-50 p-3 rounded-lg">
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-xs text-gray-600 font-jost">Uptime</span>
                              <span className="text-sm font-bold text-green-600 font-jost">99.9%</span>
                            </div>
                            <div className="w-full bg-green-200 h-1.5 rounded-full">
                              <div className="bg-green-600 h-1.5 rounded-full w-full"></div>
                            </div>
                          </div>

                          <div className="bg-green-50 p-3 rounded-lg">
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-xs text-gray-600 font-jost">Monthly Cost</span>
                              <span className="text-sm font-bold text-green-600 font-jost">$30K</span>
                            </div>
                            <div className="w-full bg-green-200 h-1.5 rounded-full">
                              <div className="bg-green-600 h-1.5 rounded-full w-3/5"></div>
                            </div>
                          </div>

                          <div className="bg-green-50 p-3 rounded-lg">
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-xs text-gray-600 font-jost">Load Time</span>
                              <span className="text-sm font-bold text-green-600 font-jost">2.1s</span>
                            </div>
                            <div className="w-full bg-green-200 h-1.5 rounded-full">
                              <div className="bg-green-600 h-1.5 rounded-full w-1/5"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Cloud Architecture Visualization */}
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h5 className="text-sm font-semibold text-blue-800 mb-3 font-jost">New Cloud Architecture</h5>
                      <div className="flex items-center justify-between">
                        <div className="text-center">
                          <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center mb-1">
                            <span className="text-white text-xs font-bold">AWS</span>
                          </div>
                          <span className="text-xs text-gray-600 font-jost">Primary</span>
                        </div>
                        <div className="flex-1 mx-2 border-t-2 border-dashed border-blue-300"></div>
                        <div className="text-center">
                          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mb-1">
                            <Database className="w-4 h-4 text-white" />
                          </div>
                          <span className="text-xs text-gray-600 font-jost">Database</span>
                        </div>
                        <div className="flex-1 mx-2 border-t-2 border-dashed border-blue-300"></div>
                        <div className="text-center">
                          <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center mb-1">
                            <Shield className="w-4 h-4 text-white" />
                          </div>
                          <span className="text-xs text-gray-600 font-jost">Security</span>
                        </div>
                      </div>
                    </div>

                    {/* Key Achievements */}
                    <div className="grid grid-cols-2 gap-3">
                      <div className="text-center p-2 bg-gray-50 rounded-lg">
                        <div className="text-lg font-bold text-blue-600 font-jost">60%</div>
                        <div className="text-xs text-gray-600 font-jost">Cost Reduction</div>
                      </div>
                      <div className="text-center p-2 bg-gray-50 rounded-lg">
                        <div className="text-lg font-bold text-green-600 font-jost">Zero</div>
                        <div className="text-xs text-gray-600 font-jost">Downtime Events</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-black mb-6 font-jost">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-gray-600 font-jost">
              Get answers to common questions about our cloud solutions and migration services.
            </p>
          </div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div
                key={index}
                className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"
              >
                <button
                  onClick={() => setOpenFAQ(openFAQ === index ? null : index)}
                  className="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
                >
                  <h3 className="text-lg font-semibold text-black font-jost pr-4">
                    {faq.question}
                  </h3>
                  {openFAQ === index ? (
                    <ChevronUp className="w-5 h-5 text-gray-500 flex-shrink-0" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-gray-500 flex-shrink-0" />
                  )}
                </button>

                {openFAQ === index && (
                  <div className="px-8 pb-6">
                    <p className="text-gray-600 leading-relaxed font-jost">
                      {faq.answer}
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-600 to-cyan-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl sm:text-5xl font-bold text-white mb-6 font-jost">
            Ready to Transform Your Infrastructure?
          </h2>
          <p className="text-xl text-blue-100 mb-8 leading-relaxed font-jost">
            Join hundreds of businesses that have reduced costs and improved performance with our cloud solutions.
            Let's optimize your infrastructure for the future.
          </p>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-8">
            <Link
              href="/get-quote"
              className="bg-white text-blue-600 px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 flex items-center gap-2 font-medium font-jost group"
            >
              Get Free Cloud Assessment
              <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
            </Link>
            <Link
              href="/contact-us"
              className="border-2 border-white text-white px-8 py-4 rounded-full hover:bg-white hover:text-blue-600 transition-all duration-300 font-medium font-jost"
            >
              Schedule Consultation
            </Link>
          </div>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-blue-100">
            <div className="flex items-center gap-2">
              <Clock className="w-5 h-5" />
              <span className="font-jost">Free assessment included</span>
            </div>
            <div className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              <span className="font-jost">Enterprise-grade security</span>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

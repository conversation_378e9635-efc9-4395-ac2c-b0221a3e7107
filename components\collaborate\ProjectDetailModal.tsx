'use client';

import React from 'react';
import { X, Clock, Users, Star, Code2, Calendar, Target, CheckCircle, ArrowRight } from 'lucide-react';

interface Project {
  id: string;
  title: string;
  description: string;
  technologies: string[];
  duration: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  requiredSkills: string[];
  isActive: boolean;
  applicationDeadline?: string;
  maxCollaborators?: number;
}

interface ProjectDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: Project | null;
}

const ProjectDetailModal: React.FC<ProjectDetailModalProps> = ({ isOpen, onClose, project }) => {
  if (!isOpen || !project) return null;

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Intermediate':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Advanced':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleApplyClick = () => {
    window.location.href = `/collaborate/apply/project?id=${project.id}`;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-gray-100 p-6 rounded-t-2xl">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h2 className="text-2xl font-bold text-black font-jost">{project.title}</h2>
                {project.isActive && (
                  <div className="flex items-center gap-1 text-green-600">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-sm font-jost">Active</span>
                  </div>
                )}
              </div>
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <Clock size={16} />
                  <span className="font-jost">{project.duration}</span>
                </div>
                <div className={`px-3 py-1 rounded-full text-xs font-medium border ${getDifficultyColor(project.difficulty)} font-jost`}>
                  {project.difficulty}
                </div>
                <div className="flex items-center gap-1 text-yellow-500">
                  <Star size={16} fill="currentColor" />
                  <span className="text-sm text-gray-600 font-jost">Featured Project</span>
                </div>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X size={24} className="text-gray-500" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-8">
          {/* Project Overview */}
          <div>
            <h3 className="text-lg font-semibold text-black mb-4 font-jost">Project Overview</h3>
            <p className="text-gray-700 leading-relaxed font-jost">{project.description}</p>
          </div>

          {/* Technologies */}
          <div>
            <h3 className="text-lg font-semibold text-black mb-4 font-jost">Technologies Used</h3>
            <div className="flex flex-wrap gap-3">
              {project.technologies.map((tech, index) => (
                <span
                  key={index}
                  className="inline-flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors font-jost"
                >
                  <Code2 size={16} />
                  {tech}
                </span>
              ))}
            </div>
          </div>

          {/* Required Skills */}
          <div>
            <h3 className="text-lg font-semibold text-black mb-4 font-jost">Required Skills</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {project.requiredSkills.map((skill, index) => (
                <div key={index} className="flex items-center gap-2">
                  <CheckCircle size={16} className="text-green-500" />
                  <span className="text-gray-700 font-jost">{skill}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Project Details */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gray-50 rounded-lg p-6 text-center">
              <Users size={24} className="text-black mx-auto mb-2" />
              <div className="text-2xl font-bold text-black font-jost">
                {project.maxCollaborators || '5'}
              </div>
              <div className="text-sm text-gray-600 font-jost">Max Team Size</div>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center">
              <Calendar size={24} className="text-black mx-auto mb-2" />
              <div className="text-2xl font-bold text-black font-jost">
                {project.applicationDeadline ? 
                  new Date(project.applicationDeadline).toLocaleDateString() : 
                  'Open'
                }
              </div>
              <div className="text-sm text-gray-600 font-jost">Application Deadline</div>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center">
              <Target size={24} className="text-black mx-auto mb-2" />
              <div className="text-2xl font-bold text-black font-jost">3</div>
              <div className="text-sm text-gray-600 font-jost">Current Team Members</div>
            </div>
          </div>

          {/* Project Goals */}
          <div>
            <h3 className="text-lg font-semibold text-black mb-4 font-jost">Project Goals</h3>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <CheckCircle size={20} className="text-green-500 mt-0.5" />
                <div>
                  <h4 className="font-medium text-black font-jost">Build a scalable solution</h4>
                  <p className="text-gray-600 text-sm font-jost">Create a robust and maintainable codebase that can grow with user needs.</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle size={20} className="text-green-500 mt-0.5" />
                <div>
                  <h4 className="font-medium text-black font-jost">Learn modern technologies</h4>
                  <p className="text-gray-600 text-sm font-jost">Gain hands-on experience with cutting-edge tools and frameworks.</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle size={20} className="text-green-500 mt-0.5" />
                <div>
                  <h4 className="font-medium text-black font-jost">Collaborate effectively</h4>
                  <p className="text-gray-600 text-sm font-jost">Work as a team using best practices for version control and communication.</p>
                </div>
              </div>
            </div>
          </div>

          {/* What You'll Gain */}
          <div>
            <h3 className="text-lg font-semibold text-black mb-4 font-jost">What You'll Gain</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3 p-4 bg-blue-50 rounded-lg">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <Code2 size={16} className="text-white" />
                </div>
                <div>
                  <h4 className="font-medium text-black font-jost">Technical Skills</h4>
                  <p className="text-sm text-gray-600 font-jost">Hands-on experience with modern tech stack</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-4 bg-green-50 rounded-lg">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <Users size={16} className="text-white" />
                </div>
                <div>
                  <h4 className="font-medium text-black font-jost">Team Collaboration</h4>
                  <p className="text-sm text-gray-600 font-jost">Work with experienced developers</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-4 bg-purple-50 rounded-lg">
                <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                  <Target size={16} className="text-white" />
                </div>
                <div>
                  <h4 className="font-medium text-black font-jost">Portfolio Project</h4>
                  <p className="text-sm text-gray-600 font-jost">Add a real project to your portfolio</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-4 bg-yellow-50 rounded-lg">
                <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                  <Star size={16} className="text-white" />
                </div>
                <div>
                  <h4 className="font-medium text-black font-jost">Recognition</h4>
                  <p className="text-sm text-gray-600 font-jost">Get recognized for your contributions</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="sticky bottom-0 bg-white border-t border-gray-100 p-6 rounded-b-2xl">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600 font-jost">
              Ready to contribute to this project?
            </div>
            <button
              onClick={handleApplyClick}
              className="flex items-center gap-2 bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-all duration-300 font-medium font-jost hover:shadow-lg transform hover:-translate-y-0.5"
            >
              <span>Apply Now</span>
              <ArrowRight size={18} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectDetailModal;

'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ExternalLink, Eye, AlertCircle } from 'lucide-react';
import { TemplateCardProps } from '@/lib/types';
import ErrorBoundary, { TemplateCardErrorFallback } from './ErrorBoundary';

const TemplateCard: React.FC<TemplateCardProps> = ({ template, className = '' }) => {
  const [imageError, setImageError] = useState(false);

  const handleDemoClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    window.open(template.demoUrl, '_blank', 'noopener,noreferrer');
  };

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <div className={`group relative bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-500 overflow-hidden hover:-translate-y-2 ${className}`}>
      {/* Hero Image */}
      <div className="relative h-64 overflow-hidden">
        {imageError ? (
          <div className="w-full h-full bg-gray-100 flex items-center justify-center">
            <div className="text-center">
              <AlertCircle size={32} className="text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500 font-jost">Image not available</p>
            </div>
          </div>
        ) : (
          <Image
            src={template.heroImage}
            alt={`${template.name} preview`}
            fill
            className="object-cover transition-transform duration-700 group-hover:scale-110"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            priority={false}
            onError={handleImageError}
          />
        )}
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        
        {/* Category Badge */}
        <div className="absolute top-4 left-4">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-black/80 text-white backdrop-blur-sm">
            {template.category}
          </span>
        </div>

        {/* Quick Demo Button - appears on hover */}
        <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
          <button
            onClick={handleDemoClick}
            className="flex items-center gap-2 px-3 py-2 bg-white/90 backdrop-blur-sm rounded-lg text-sm font-medium text-gray-900 hover:bg-white transition-colors duration-200"
            aria-label={`View ${template.name} demo`}
          >
            <ExternalLink size={14} />
            Demo
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Template Name */}
        <h3 className="text-xl font-bold text-gray-900 mb-3 font-jost group-hover:text-gray-700 transition-colors duration-200">
          {template.name}
        </h3>

        {/* Short Description */}
        <p className="text-gray-600 mb-4 font-jost leading-relaxed line-clamp-3">
          {template.shortDescription}
        </p>

        {/* Technologies */}
        <div className="flex flex-wrap gap-2 mb-6">
          {template.technologies.slice(0, 3).map((tech, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-gray-100 text-gray-700 rounded-md text-xs font-medium font-jost"
            >
              {tech}
            </span>
          ))}
          {template.technologies.length > 3 && (
            <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded-md text-xs font-medium font-jost">
              +{template.technologies.length - 3} more
            </span>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
          <Link
            href={`/project/${template.slug}`}
            className="flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-black text-white rounded-lg hover:bg-gray-800 transition-all duration-300 text-sm font-medium font-jost group/btn"
          >
            <Eye size={16} className="transition-transform duration-300 group-hover/btn:scale-110" />
            View Details
          </Link>
          
          <button
            onClick={handleDemoClick}
            className="flex items-center justify-center gap-2 px-4 py-3 border-2 border-gray-300 text-gray-700 rounded-lg hover:border-gray-400 hover:bg-gray-50 transition-all duration-300 text-sm font-medium font-jost group/btn"
          >
            <ExternalLink size={16} className="transition-transform duration-300 group-hover/btn:scale-110" />
            See Demo
          </button>
        </div>
      </div>

      {/* Hover Effect Overlay */}
      <div className="absolute inset-0 border-2 border-transparent group-hover:border-gray-200 rounded-2xl transition-colors duration-300 pointer-events-none" />
    </div>
  );
};

// Wrapped with Error Boundary
const TemplateCardWithErrorBoundary: React.FC<TemplateCardProps> = (props) => (
  <ErrorBoundary fallback={TemplateCardErrorFallback}>
    <TemplateCard {...props} />
  </ErrorBoundary>
);

export default TemplateCardWithErrorBoundary;

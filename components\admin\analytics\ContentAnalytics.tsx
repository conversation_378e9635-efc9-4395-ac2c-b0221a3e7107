'use client';

import React, { useState, useEffect } from 'react';
import { AdminCard, AdminStatCard } from '@/components/admin/ui/AdminCard';
import AdminButton from '@/components/admin/ui/AdminButton';
import { AdminTableSkeleton } from '@/components/admin/ui/AdminLoading';
import { useAdminAuth, ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';
import { AdminPermissionWrapper } from '@/components/admin/auth/AdminProtectedRoute';
import { ContentPerformance } from '@/lib/types/admin';
import {
  Eye,
  Heart,
  Share2,
  MessageSquare,
  TrendingUp,
  TrendingDown,
  Clock,
  Users,
  Filter,
  Search,
  Download,
  FileText,
  Briefcase,
  PenTool,
} from 'lucide-react';

interface ContentAnalyticsProps {
  contentType?: 'all' | 'templates' | 'services' | 'blog';
}

export default function ContentAnalytics({ contentType = 'all' }: ContentAnalyticsProps) {
  const { hasPermission } = useAdminAuth();
  const [content, setContent] = useState<ContentPerformance[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>(contentType);
  const [sortBy, setSortBy] = useState<string>('views');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const itemsPerPage = 10;

  // Fetch content performance data
  const fetchContentPerformance = async () => {
    try {
      setLoading(true);
      
      // Mock data for development
      const mockContent: ContentPerformance[] = [
        {
          id: '1',
          title: 'E-commerce Website Template',
          type: 'template',
          slug: 'ecommerce-website-template',
          publishedAt: '2024-01-15T10:00:00Z',
          views: 1250,
          uniqueViews: 980,
          likes: 45,
          shares: 12,
          comments: 8,
          downloads: 23,
          inquiries: 15,
          conversionRate: 3.6,
          avgTimeOnPage: 245,
          bounceRate: 28.5,
          topReferrers: ['Google', 'Direct', 'Social Media'],
          topKeywords: ['ecommerce template', 'online store', 'shopping website'],
        },
        {
          id: '2',
          title: 'Mobile App Development Service',
          type: 'service',
          slug: 'mobile-app-development',
          publishedAt: '2024-01-10T14:30:00Z',
          views: 890,
          uniqueViews: 720,
          likes: 32,
          shares: 8,
          comments: 5,
          downloads: 0,
          inquiries: 28,
          conversionRate: 4.2,
          avgTimeOnPage: 320,
          bounceRate: 22.1,
          topReferrers: ['Google', 'LinkedIn', 'Direct'],
          topKeywords: ['mobile app development', 'ios app', 'android app'],
        },
        {
          id: '3',
          title: 'How to Build a Modern Web App',
          type: 'blog',
          slug: 'how-to-build-modern-web-app',
          publishedAt: '2024-01-08T09:15:00Z',
          views: 2340,
          uniqueViews: 1890,
          likes: 78,
          shares: 34,
          comments: 23,
          downloads: 0,
          inquiries: 5,
          conversionRate: 0.8,
          avgTimeOnPage: 420,
          bounceRate: 35.2,
          topReferrers: ['Google', 'Twitter', 'LinkedIn'],
          topKeywords: ['web app development', 'modern web app', 'javascript framework'],
        },
      ];
      
      setContent(mockContent);
      setTotalPages(1);
    } catch (error) {
      console.error('Error fetching content performance:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchContentPerformance();
  }, [typeFilter, sortBy, searchQuery, currentPage]);

  const getContentIcon = (type: string) => {
    switch (type) {
      case 'template':
        return <FileText className="h-5 w-5 text-blue-600" />;
      case 'service':
        return <Briefcase className="h-5 w-5 text-green-600" />;
      case 'blog':
        return <PenTool className="h-5 w-5 text-purple-600" />;
      default:
        return <FileText className="h-5 w-5 text-gray-600" />;
    }
  };

  const getContentTypeBadge = (type: string) => {
    const badges = {
      template: 'bg-blue-100 text-blue-800',
      service: 'bg-green-100 text-green-800',
      blog: 'bg-purple-100 text-purple-800',
    };
    return badges[type as keyof typeof badges] || 'bg-gray-100 text-gray-800';
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Calculate summary stats
  const totalViews = content.reduce((sum, item) => sum + item.views, 0);
  const totalUniqueViews = content.reduce((sum, item) => sum + item.uniqueViews, 0);
  const totalEngagement = content.reduce((sum, item) => sum + item.likes + item.shares + item.comments, 0);
  const avgConversionRate = content.length > 0 
    ? content.reduce((sum, item) => sum + item.conversionRate, 0) / content.length 
    : 0;

  if (loading) {
    return <AdminTableSkeleton rows={5} columns={6} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 font-jost">Content Analytics</h1>
          <p className="text-gray-600 font-jost">Track performance of your templates, services, and blog posts</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.ANALYTICS_EXPORT}>
            <AdminButton 
              variant="outline"
              icon={<Download className="h-4 w-4" />}
            >
              Export Report
            </AdminButton>
          </AdminPermissionWrapper>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <AdminStatCard
          title="Total Views"
          value={formatNumber(totalViews)}
          change={{ value: '+12%', type: 'positive' }}
          icon={<Eye className="h-6 w-6 text-gray-600" />}
        />
        <AdminStatCard
          title="Unique Views"
          value={formatNumber(totalUniqueViews)}
          change={{ value: '+8%', type: 'positive' }}
          icon={<Users className="h-6 w-6 text-gray-600" />}
        />
        <AdminStatCard
          title="Total Engagement"
          value={formatNumber(totalEngagement)}
          change={{ value: '+15%', type: 'positive' }}
          icon={<Heart className="h-6 w-6 text-gray-600" />}
        />
        <AdminStatCard
          title="Avg Conversion Rate"
          value={formatPercentage(avgConversionRate)}
          change={{ value: '+0.5%', type: 'positive' }}
          icon={<TrendingUp className="h-6 w-6 text-gray-600" />}
        />
      </div>

      {/* Filters */}
      <AdminCard>
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search content..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
              />
            </div>
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-3">
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
            >
              <option value="all">All Content</option>
              <option value="templates">Templates</option>
              <option value="services">Services</option>
              <option value="blog">Blog Posts</option>
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-jost"
            >
              <option value="views">Most Views</option>
              <option value="engagement">Most Engagement</option>
              <option value="conversion">Best Conversion</option>
              <option value="recent">Most Recent</option>
            </select>
          </div>
        </div>
      </AdminCard>

      {/* Content Performance List */}
      {content.length > 0 ? (
        <div className="space-y-4">
          {content.map((item) => (
            <AdminCard key={item.id} className="hover:shadow-md transition-shadow duration-200">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-start space-x-4">
                      <div className="h-12 w-12 bg-gray-100 rounded-lg flex items-center justify-center">
                        {getContentIcon(item.type)}
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 font-jost">
                          {item.title}
                        </h3>
                        <div className="flex items-center space-x-3 text-sm text-gray-600 font-jost">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getContentTypeBadge(item.type)}`}>
                            {item.type}
                          </span>
                          <span>Published {formatDate(item.publishedAt)}</span>
                        </div>
                      </div>
                    </div>

                    <div className="text-right">
                      <div className="text-lg font-bold text-gray-900 font-jost">
                        {formatPercentage(item.conversionRate)}
                      </div>
                      <div className="text-xs text-gray-500 font-jost">Conversion Rate</div>
                    </div>
                  </div>

                  {/* Metrics Grid */}
                  <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-4">
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <Eye className="h-4 w-4 text-gray-400 mr-1" />
                        <span className="text-sm font-medium text-gray-900 font-jost">
                          {formatNumber(item.views)}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500 font-jost">Views</div>
                    </div>

                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <Users className="h-4 w-4 text-gray-400 mr-1" />
                        <span className="text-sm font-medium text-gray-900 font-jost">
                          {formatNumber(item.uniqueViews)}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500 font-jost">Unique</div>
                    </div>

                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <Heart className="h-4 w-4 text-gray-400 mr-1" />
                        <span className="text-sm font-medium text-gray-900 font-jost">
                          {formatNumber(item.likes)}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500 font-jost">Likes</div>
                    </div>

                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <Share2 className="h-4 w-4 text-gray-400 mr-1" />
                        <span className="text-sm font-medium text-gray-900 font-jost">
                          {formatNumber(item.shares)}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500 font-jost">Shares</div>
                    </div>

                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <Clock className="h-4 w-4 text-gray-400 mr-1" />
                        <span className="text-sm font-medium text-gray-900 font-jost">
                          {formatDuration(item.avgTimeOnPage)}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500 font-jost">Avg Time</div>
                    </div>

                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <MessageSquare className="h-4 w-4 text-gray-400 mr-1" />
                        <span className="text-sm font-medium text-gray-900 font-jost">
                          {formatNumber(item.inquiries)}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500 font-jost">Inquiries</div>
                    </div>
                  </div>

                  {/* Top Keywords */}
                  {item.topKeywords.length > 0 && (
                    <div className="mb-4">
                      <p className="text-sm font-medium text-gray-900 font-jost mb-2">Top Keywords:</p>
                      <div className="flex flex-wrap gap-2">
                        {item.topKeywords.slice(0, 5).map((keyword, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800"
                          >
                            {keyword}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Performance Indicators */}
                  <div className="flex items-center justify-between pt-3 border-t border-gray-200">
                    <div className="flex items-center space-x-4 text-sm text-gray-600 font-jost">
                      <span>Bounce Rate: {formatPercentage(item.bounceRate)}</span>
                      <span>Top Referrer: {item.topReferrers[0]}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      {item.conversionRate > 3 && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <TrendingUp className="h-3 w-3 mr-1" />
                          High Converting
                        </span>
                      )}
                      {item.views > 1000 && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          Popular
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </AdminCard>
          ))}
        </div>
      ) : (
        <AdminCard className="text-center py-12">
          <div className="mx-auto h-16 w-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <Eye className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2 font-jost">No content performance data</h3>
          <p className="text-gray-600 font-jost">
            {searchQuery || typeFilter !== 'all'
              ? 'Try adjusting your filters to see more results.'
              : 'Content performance data will appear here once you have published content.'}
          </p>
        </AdminCard>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-700 font-jost">
            Showing page {currentPage} of {totalPages}
          </p>
          <div className="flex items-center space-x-2">
            <AdminButton
              variant="outline"
              size="sm"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(currentPage - 1)}
            >
              Previous
            </AdminButton>
            <AdminButton
              variant="outline"
              size="sm"
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage(currentPage + 1)}
            >
              Next
            </AdminButton>
          </div>
        </div>
      )}
    </div>
  );
}

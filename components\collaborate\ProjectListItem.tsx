'use client';

import React, { useState } from 'react';
import { Clock, <PERSON>, <PERSON>, <PERSON>R<PERSON>, Code2, Eye } from 'lucide-react';

interface Project {
  id: string;
  title: string;
  description: string;
  previewDescription?: string;
  technologies: string[];
  duration: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  requiredSkills: string[];
  isActive: boolean;
  applicationDeadline?: string;
  maxCollaborators?: number;
}

interface ProjectListItemProps {
  project: Project;
  className?: string;
  onViewDetails?: (project: Project) => void;
}

const ProjectListItem: React.FC<ProjectListItemProps> = ({ 
  project, 
  className = '',
  onViewDetails 
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Intermediate':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Advanced':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleApplyClick = () => {
    window.location.href = `/collaborate/apply/project?id=${project.id}`;
  };

  const handleViewDetails = () => {
    if (onViewDetails) {
      onViewDetails(project);
    }
  };

  return (
    <div 
      className={`group relative bg-white rounded-xl shadow-md border border-gray-100 hover:shadow-lg transition-all duration-300 overflow-hidden hover:-translate-y-1 ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="p-6">
        <div className="flex items-start justify-between gap-6">
          {/* Left Section - Main Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <h3 className="text-lg font-bold text-black mb-2 font-jost group-hover:text-gray-800 transition-colors line-clamp-1">
                  {project.title}
                </h3>
                <div className="flex items-center gap-3 text-sm text-gray-600 mb-3">
                  <div className="flex items-center gap-1">
                    <Clock size={14} />
                    <span className="font-jost">{project.duration}</span>
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium border ${getDifficultyColor(project.difficulty)} font-jost`}>
                    {project.difficulty}
                  </div>
                  {project.isActive && (
                    <div className="flex items-center gap-1 text-green-600">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-xs font-jost">Active</span>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-1 text-yellow-500">
                <Star size={14} fill="currentColor" />
                <span className="text-xs text-gray-600 font-jost">Featured</span>
              </div>
            </div>

            <p className="text-gray-600 text-sm mb-4 line-clamp-2 font-jost">
              {project.previewDescription || project.description}
            </p>

            {/* Technology Tags */}
            <div className="flex flex-wrap gap-2 mb-4">
              {project.technologies.slice(0, 4).map((tech, index) => (
                <span
                  key={index}
                  className="inline-flex items-center gap-1 px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs font-medium hover:bg-gray-200 transition-colors font-jost"
                >
                  <Code2 size={12} />
                  {tech}
                </span>
              ))}
              {project.technologies.length > 4 && (
                <span className="text-xs text-gray-500 font-jost">
                  +{project.technologies.length - 4} more
                </span>
              )}
            </div>
          </div>

          {/* Right Section - Stats and Actions */}
          <div className="flex flex-col items-end gap-4 min-w-0">
            {/* Project Stats */}
            <div className="flex items-center gap-6 text-center">
              <div>
                <div className="text-sm font-bold text-black font-jost">
                  {project.maxCollaborators || '5'}
                </div>
                <div className="text-xs text-gray-600 font-jost">Max Team</div>
              </div>
              <div>
                <div className="text-sm font-bold text-black font-jost">
                  {project.applicationDeadline ? 
                    new Date(project.applicationDeadline).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }) : 
                    'Open'
                  }
                </div>
                <div className="text-xs text-gray-600 font-jost">Deadline</div>
              </div>
              <div>
                <div className="flex items-center justify-center gap-1">
                  <Users size={14} className="text-green-500" />
                  <span className="text-sm font-bold text-black font-jost">3</span>
                </div>
                <div className="text-xs text-gray-600 font-jost">Current</div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-2">
              <button
                onClick={handleViewDetails}
                className="flex items-center gap-1 px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:border-gray-400 hover:bg-gray-50 transition-all duration-300 text-sm font-medium font-jost"
              >
                <Eye size={14} />
                <span>Details</span>
              </button>
              <button
                onClick={handleApplyClick}
                className="flex items-center gap-2 bg-black text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-all duration-300 text-sm font-medium font-jost hover:shadow-md transform hover:-translate-y-0.5"
              >
                <span>Apply</span>
                <ArrowRight 
                  size={14} 
                  className={`transition-all duration-300 ${
                    isHovered ? 'translate-x-0.5 scale-110' : ''
                  }`}
                />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Hover Overlay Effect */}
      <div className={`absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none rounded-xl`} />
    </div>
  );
};

export default ProjectListItem;

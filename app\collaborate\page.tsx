'use client';

import React, { useState, useEffect } from 'react';
import { ArrowRight, Users, Code, Briefcase, Filter, Search, Grid3X3, List, ChevronDown } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ProjectCard from '@/components/collaborate/ProjectCard';
import JobCard from '@/components/collaborate/JobCard';
import ProjectListItem from '@/components/collaborate/ProjectListItem';
import JobListItem from '@/components/collaborate/JobListItem';
import ProjectDetailModal from '@/components/collaborate/ProjectDetailModal';
import JobDetailModal from '@/components/collaborate/JobDetailModal';
import FilterModal from '@/components/collaborate/FilterModal';
import { getActiveProjects, getActiveJobPostings, searchProjects, searchJobPostings, Project, JobPosting } from '@/lib/supabase/collaborate';

const CollaboratePage = () => {
  const [activeTab, setActiveTab] = useState<'projects' | 'jobs'>('projects');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilters, setSelectedFilters] = useState<{
    technology?: string;
    difficulty?: string;
    experienceLevel?: string;
    employmentType?: string;
  }>({});
  const [projects, setProjects] = useState<Project[]>([]);
  const [jobs, setJobs] = useState<JobPosting[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [displayedProjectsCount, setDisplayedProjectsCount] = useState(6);
  const [displayedJobsCount, setDisplayedJobsCount] = useState(6);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [selectedJob, setSelectedJob] = useState<JobPosting | null>(null);
  const [showProjectModal, setShowProjectModal] = useState(false);
  const [showJobModal, setShowJobModal] = useState(false);
  const ITEMS_PER_LOAD = 6;

  // Load data on component mount
  useEffect(() => {
    loadData();
  }, []);

  // Load data when search term or filters change
  useEffect(() => {
    if (searchTerm || Object.keys(selectedFilters).length > 0) {
      handleSearch();
    } else {
      loadData();
    }
  }, [searchTerm, selectedFilters]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [projectsData, jobsData] = await Promise.all([
        getActiveProjects(),
        getActiveJobPostings()
      ]);

      setProjects(projectsData);
      setJobs(jobsData);
    } catch (err) {
      console.error('Error loading data:', err);
      setError('Failed to load data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    try {
      setLoading(true);
      setError(null);

      if (activeTab === 'projects') {
        const filters: { technologies?: string[]; difficulty?: string; duration?: string } = {};
        if (selectedFilters.difficulty) filters.difficulty = selectedFilters.difficulty;
        if (selectedFilters.technology) filters.technologies = [selectedFilters.technology];

        const projectsData = await searchProjects(searchTerm, filters);
        setProjects(projectsData);
      } else {
        const filters: { employment_type?: string; experience_level?: string; location_type?: string } = {};
        if (selectedFilters.employmentType) filters.employment_type = selectedFilters.employmentType;
        if (selectedFilters.experienceLevel) filters.experience_level = selectedFilters.experienceLevel;

        const jobsData = await searchJobPostings(searchTerm, filters);
        setJobs(jobsData);
      }
    } catch (err) {
      console.error('Error searching:', err);
      setError('Search failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const clearSearch = () => {
    setSearchTerm('');
    setSelectedFilters({});
    setDisplayedProjectsCount(6);
    setDisplayedJobsCount(6);
  };

  const handleApplyFilters = (filters: any) => {
    setSelectedFilters(filters);
    setDisplayedProjectsCount(6);
    setDisplayedJobsCount(6);
  };

  const loadMoreProjects = () => {
    setDisplayedProjectsCount(prev => prev + ITEMS_PER_LOAD);
  };

  const loadMoreJobs = () => {
    setDisplayedJobsCount(prev => prev + ITEMS_PER_LOAD);
  };

  const handleProjectDetails = (project: any) => {
    // Convert the component project format back to database format for the modal
    const dbProject = projects.find(p => p.id === project.id);
    if (dbProject) {
      setSelectedProject(dbProject);
      setShowProjectModal(true);
    }
  };

  const handleJobDetails = (job: any) => {
    // Convert the component job format back to database format for the modal
    const dbJob = jobs.find(j => j.id === job.id);
    if (dbJob) {
      setSelectedJob(dbJob);
      setShowJobModal(true);
    }
  };

  const closeProjectModal = () => {
    setShowProjectModal(false);
    setSelectedProject(null);
  };

  const closeJobModal = () => {
    setShowJobModal(false);
    setSelectedJob(null);
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
          <div className="max-w-7xl mx-auto">
            <div className="text-center">
              {/* Badge */}
              <div className="inline-flex items-center bg-black text-white px-4 py-2 rounded-full text-sm mb-8 hover:scale-105 transition-all duration-300 cursor-pointer animate-fade-in font-jost">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
                Join Our Developer Community - Grow Together
              </div>

              {/* Main Heading */}
              <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6 leading-tight font-jost">
                Collaborate with iREME
              </h1>

              {/* Subtext */}
              <p className="text-lg sm:text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed font-jost">
                Join our inclusive developer community and contribute to meaningful projects while advancing your career.
                <br className="hidden sm:block" />
                Discover opportunities that match your skills and aspirations.
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-16">
                <button
                  onClick={() => setActiveTab('projects')}
                  className={`px-8 py-4 rounded-full transition-all duration-500 flex items-center gap-2 text-base font-medium group relative overflow-hidden hover:shadow-lg transform hover:-translate-y-1 font-jost ${
                    activeTab === 'projects'
                      ? 'bg-black text-white'
                      : 'border-2 border-gray-300 text-gray-700 hover:border-gray-400 hover:bg-gray-50'
                  }`}
                >
                  <Code size={20} />
                  <span>Explore Projects</span>
                  <ArrowRight size={20} className="transition-transform duration-300 group-hover:translate-x-1" />
                </button>

                <button
                  onClick={() => setActiveTab('jobs')}
                  className={`px-8 py-4 rounded-full transition-all duration-500 flex items-center gap-2 text-base font-medium group relative overflow-hidden hover:shadow-lg transform hover:-translate-y-1 font-jost ${
                    activeTab === 'jobs'
                      ? 'bg-black text-white'
                      : 'border-2 border-gray-300 text-gray-700 hover:border-gray-400 hover:bg-gray-50'
                  }`}
                >
                  <Briefcase size={20} />
                  <span>View Job Opportunities</span>
                  <ArrowRight size={20} className="transition-transform duration-300 group-hover:translate-x-1" />
                </button>
              </div>

              {/* Floating Elements */}
              <div className="absolute top-20 left-10 w-20 h-20 bg-blue-100 rounded-full opacity-50 animate-float-slow hidden lg:block"></div>
              <div className="absolute top-40 right-20 w-16 h-16 bg-green-100 rounded-full opacity-50 animate-float-medium hidden lg:block"></div>
              <div className="absolute bottom-20 left-20 w-12 h-12 bg-yellow-100 rounded-full opacity-50 animate-float-fast hidden lg:block"></div>
            </div>
          </div>
        </section>

        {/* Main Content */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            {/* Tab Content */}
            {activeTab === 'projects' && (
              <div>
                {/* Section Header */}
                <div className="text-center mb-12">
                  <h2 className="text-3xl font-bold text-black mb-4 font-jost">Public Projects</h2>
                  <p className="text-lg text-gray-600 max-w-2xl mx-auto font-jost">
                    Contribute to impactful projects and gain real-world experience while building your portfolio.
                  </p>
                </div>

                {/* Search, Filters, and View Toggle */}
                <div className="mb-8 space-y-4">
                  <div className="flex flex-col md:flex-row gap-4">
                    <div className="flex-1 relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                      <input
                        type="text"
                        placeholder="Search projects..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost"
                      />
                    </div>
                    <button
                      onClick={() => setShowFilterModal(true)}
                      className="flex items-center gap-2 px-6 py-3 border border-gray-300 rounded-lg hover:border-gray-400 hover:bg-gray-50 transition-all duration-300 font-jost"
                    >
                      <Filter size={20} />
                      Filters
                      {Object.keys(selectedFilters).length > 0 && (
                        <span className="bg-black text-white text-xs px-2 py-1 rounded-full">
                          {Object.keys(selectedFilters).length}
                        </span>
                      )}
                    </button>
                  </div>

                  {/* View Toggle */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600 font-jost">View:</span>
                      <div className="flex items-center bg-gray-100 rounded-lg p-1">
                        <button
                          onClick={() => setViewMode('grid')}
                          className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 font-jost ${
                            viewMode === 'grid'
                              ? 'bg-white text-black shadow-sm'
                              : 'text-gray-600 hover:text-black'
                          }`}
                        >
                          <Grid3X3 size={16} />
                          Grid
                        </button>
                        <button
                          onClick={() => setViewMode('list')}
                          className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 font-jost ${
                            viewMode === 'list'
                              ? 'bg-white text-black shadow-sm'
                              : 'text-gray-600 hover:text-black'
                          }`}
                        >
                          <List size={16} />
                          List
                        </button>
                      </div>
                    </div>
                    <div className="text-sm text-gray-600 font-jost">
                      Showing {Math.min(displayedProjectsCount, projects.length)} of {projects.length} projects
                    </div>
                  </div>
                </div>

                {/* Loading State */}
                {loading && (
                  <div className="flex justify-center items-center py-12">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
                  </div>
                )}

                {/* Error State */}
                {error && (
                  <div className="text-center py-12">
                    <p className="text-red-600 mb-4 font-jost">{error}</p>
                    <button
                      onClick={loadData}
                      className="bg-black text-white px-6 py-2 rounded-lg hover:bg-gray-800 transition-colors font-jost"
                    >
                      Try Again
                    </button>
                  </div>
                )}

                {/* Projects Display */}
                {!loading && !error && (
                  <div>
                    {projects.length > 0 ? (
                      <>
                        <div className={viewMode === 'grid'
                          ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
                          : "space-y-4"
                        }>
                          {projects.slice(0, displayedProjectsCount).map((project) => {
                            const projectData = {
                              id: project.id,
                              title: project.title,
                              description: project.description,
                              technologies: project.technologies,
                              duration: project.duration,
                              difficulty: project.difficulty_level,
                              requiredSkills: project.required_skills,
                              isActive: project.is_active,
                              ...(project.preview_description && { previewDescription: project.preview_description }),
                              ...(project.application_deadline && { applicationDeadline: project.application_deadline }),
                              ...(project.max_collaborators && { maxCollaborators: project.max_collaborators })
                            };

                            return viewMode === 'grid' ? (
                              <ProjectCard
                                key={project.id}
                                project={projectData}
                              />
                            ) : (
                              <ProjectListItem
                                key={project.id}
                                project={projectData}
                                onViewDetails={handleProjectDetails}
                              />
                            );
                          })}
                        </div>

                        {/* Load More Button */}
                        {displayedProjectsCount < projects.length && (
                          <div className="text-center mt-12">
                            <button
                              onClick={loadMoreProjects}
                              className="inline-flex items-center gap-2 bg-black text-white px-8 py-3 rounded-lg hover:bg-gray-800 transition-all duration-300 font-medium font-jost hover:shadow-lg transform hover:-translate-y-0.5"
                            >
                              <span>Load More Projects</span>
                              <ChevronDown size={18} />
                            </button>
                          </div>
                        )}
                      </>
                    ) : (
                      <div className="text-center py-12">
                        <p className="text-gray-600 font-jost">No projects found matching your criteria.</p>
                        {(searchTerm || Object.keys(selectedFilters).length > 0) && (
                          <button
                            onClick={clearSearch}
                            className="mt-4 text-blue-500 hover:text-blue-600 font-jost"
                          >
                            Clear search and filters
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {activeTab === 'jobs' && (
              <div>
                {/* Section Header */}
                <div className="text-center mb-12">
                  <h2 className="text-3xl font-bold text-black mb-4 font-jost">Job Opportunities</h2>
                  <p className="text-lg text-gray-600 max-w-2xl mx-auto font-jost">
                    Join our team with competitive compensation and opportunities for professional growth.
                  </p>
                </div>

                {/* Search, Filters, and View Toggle */}
                <div className="mb-8 space-y-4">
                  <div className="flex flex-col md:flex-row gap-4">
                    <div className="flex-1 relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                      <input
                        type="text"
                        placeholder="Search jobs..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 font-jost"
                      />
                    </div>
                    <button
                      onClick={() => setShowFilterModal(true)}
                      className="flex items-center gap-2 px-6 py-3 border border-gray-300 rounded-lg hover:border-gray-400 hover:bg-gray-50 transition-all duration-300 font-jost"
                    >
                      <Filter size={20} />
                      Filters
                      {Object.keys(selectedFilters).length > 0 && (
                        <span className="bg-black text-white text-xs px-2 py-1 rounded-full">
                          {Object.keys(selectedFilters).length}
                        </span>
                      )}
                    </button>
                  </div>

                  {/* View Toggle */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600 font-jost">View:</span>
                      <div className="flex items-center bg-gray-100 rounded-lg p-1">
                        <button
                          onClick={() => setViewMode('grid')}
                          className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 font-jost ${
                            viewMode === 'grid'
                              ? 'bg-white text-black shadow-sm'
                              : 'text-gray-600 hover:text-black'
                          }`}
                        >
                          <Grid3X3 size={16} />
                          Grid
                        </button>
                        <button
                          onClick={() => setViewMode('list')}
                          className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 font-jost ${
                            viewMode === 'list'
                              ? 'bg-white text-black shadow-sm'
                              : 'text-gray-600 hover:text-black'
                          }`}
                        >
                          <List size={16} />
                          List
                        </button>
                      </div>
                    </div>
                    <div className="text-sm text-gray-600 font-jost">
                      Showing {Math.min(displayedJobsCount, jobs.length)} of {jobs.length} jobs
                    </div>
                  </div>
                </div>

                {/* Loading State */}
                {loading && (
                  <div className="flex justify-center items-center py-12">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
                  </div>
                )}

                {/* Error State */}
                {error && (
                  <div className="text-center py-12">
                    <p className="text-red-600 mb-4 font-jost">{error}</p>
                    <button
                      onClick={loadData}
                      className="bg-black text-white px-6 py-2 rounded-lg hover:bg-gray-800 transition-colors font-jost"
                    >
                      Try Again
                    </button>
                  </div>
                )}

                {/* Jobs Display */}
                {!loading && !error && (
                  <div>
                    {jobs.length > 0 ? (
                      <>
                        <div className={viewMode === 'grid'
                          ? "grid grid-cols-1 md:grid-cols-2 gap-8"
                          : "space-y-4"
                        }>
                          {jobs.slice(0, displayedJobsCount).map((job) => {
                            const jobData = {
                              id: job.id,
                              title: job.title,
                              description: job.description,
                              employmentType: job.employment_type,
                              experienceLevel: job.experience_level,
                              locationType: job.location_type,
                              compensationRange: job.compensation_range,
                              requiredSkills: job.required_skills,
                              isActive: job.is_active,
                              ...(job.preview_description && { previewDescription: job.preview_description }),
                              ...(job.application_deadline && { applicationDeadline: job.application_deadline })
                            };

                            return viewMode === 'grid' ? (
                              <JobCard
                                key={job.id}
                                job={jobData}
                              />
                            ) : (
                              <JobListItem
                                key={job.id}
                                job={jobData}
                                onViewDetails={handleJobDetails}
                              />
                            );
                          })}
                        </div>

                        {/* Load More Button */}
                        {displayedJobsCount < jobs.length && (
                          <div className="text-center mt-12">
                            <button
                              onClick={loadMoreJobs}
                              className="inline-flex items-center gap-2 bg-black text-white px-8 py-3 rounded-lg hover:bg-gray-800 transition-all duration-300 font-medium font-jost hover:shadow-lg transform hover:-translate-y-0.5"
                            >
                              <span>Load More Jobs</span>
                              <ChevronDown size={18} />
                            </button>
                          </div>
                        )}
                      </>
                    ) : (
                      <div className="text-center py-12">
                        <p className="text-gray-600 font-jost">No job opportunities found matching your criteria.</p>
                        {(searchTerm || Object.keys(selectedFilters).length > 0) && (
                          <button
                            onClick={clearSearch}
                            className="mt-4 text-blue-500 hover:text-blue-600 font-jost"
                          >
                            Clear search and filters
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </section>
      </main>
      <Footer />

      {/* Filter Modal */}
      <FilterModal
        isOpen={showFilterModal}
        onClose={() => setShowFilterModal(false)}
        onApplyFilters={handleApplyFilters}
        type={activeTab}
        currentFilters={selectedFilters}
      />

      {/* Project Detail Modal */}
      <ProjectDetailModal
        isOpen={showProjectModal}
        onClose={closeProjectModal}
        project={selectedProject ? {
          id: selectedProject.id,
          title: selectedProject.title,
          description: selectedProject.description,
          technologies: selectedProject.technologies,
          duration: selectedProject.duration,
          difficulty: selectedProject.difficulty_level,
          requiredSkills: selectedProject.required_skills,
          isActive: selectedProject.is_active,
          ...(selectedProject.preview_description && { previewDescription: selectedProject.preview_description }),
          ...(selectedProject.application_deadline && { applicationDeadline: selectedProject.application_deadline }),
          ...(selectedProject.max_collaborators && { maxCollaborators: selectedProject.max_collaborators })
        } : null}
      />

      {/* Job Detail Modal */}
      <JobDetailModal
        isOpen={showJobModal}
        onClose={closeJobModal}
        job={selectedJob ? {
          id: selectedJob.id,
          title: selectedJob.title,
          description: selectedJob.description,
          employmentType: selectedJob.employment_type,
          experienceLevel: selectedJob.experience_level,
          locationType: selectedJob.location_type,
          compensationRange: selectedJob.compensation_range,
          requiredSkills: selectedJob.required_skills,
          isActive: selectedJob.is_active,
          ...(selectedJob.preview_description && { previewDescription: selectedJob.preview_description }),
          ...(selectedJob.application_deadline && { applicationDeadline: selectedJob.application_deadline })
        } : null}
      />
    </div>
  );
};

export default CollaboratePage;

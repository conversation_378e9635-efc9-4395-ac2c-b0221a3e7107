'use client';

import React from 'react';
import AdminProtectedRoute from '@/components/admin/auth/AdminProtectedRoute';
import AdminLayout from '@/components/admin/layout/AdminLayout';
import SecurityDashboard from '@/components/admin/security/SecurityDashboard';
import { ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';

export default function AdminSecurityPage() {
  return (
    <AdminProtectedRoute requiredPermission={ADMIN_PERMISSIONS.SYSTEM_SETTINGS}>
      <AdminLayout 
        title="Security Dashboard" 
        subtitle="Monitor security events, threats, and compliance status"
      >
        <SecurityDashboard />
      </AdminLayout>
    </AdminProtectedRoute>
  );
}

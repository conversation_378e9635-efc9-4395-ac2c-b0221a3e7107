'use client';

import React, { useState, useEffect } from 'react';
import { AdminCard, AdminStatCard } from '@/components/admin/ui/AdminCard';
import AdminButton from '@/components/admin/ui/AdminButton';
import { AdminTableSkeleton } from '@/components/admin/ui/AdminLoading';
import { useAdminAuth, ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';
import { AdminPermissionWrapper } from '@/components/admin/auth/AdminProtectedRoute';
import { securityMonitor } from '@/lib/security/security-monitor';
import { auditLogger } from '@/lib/audit/audit-logger';
import { dataProtectionManager } from '@/lib/compliance/data-protection';
import {
  Shield,
  AlertTriangle,
  Eye,
  Lock,
  Users,
  Activity,
  RefreshCw,
  Download,
  CheckCircle,
  XCircle,
  Clock,
  Globe,
  FileText,
  Settings,
} from 'lucide-react';

interface SecurityMetrics {
  totalEvents: number;
  criticalEvents: number;
  blockedIPs: number;
  failedLogins: number;
  suspiciousActivity: number;
  resolvedIncidents: number;
  activeThreats: number;
  complianceScore: number;
}

export default function SecurityDashboard() {
  const { hasPermission } = useAdminAuth();
  const [metrics, setMetrics] = useState<SecurityMetrics | null>(null);
  const [recentEvents, setRecentEvents] = useState<any[]>([]);
  const [auditStats, setAuditStats] = useState<any>(null);
  const [complianceReport, setComplianceReport] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch security data
  const fetchSecurityData = async () => {
    try {
      setLoading(true);
      
      // Get security statistics
      const securityStats = securityMonitor.getSecurityStats(24);
      
      // Get audit statistics
      const auditStatistics = auditLogger.getStats(24);
      
      // Get compliance report
      const compliance = dataProtectionManager.getComplianceReport();
      
      // Get recent security events
      const events = securityMonitor.getEvents({ limit: 10 });
      
      // Calculate metrics
      const securityMetrics: SecurityMetrics = {
        totalEvents: securityStats.totalEvents,
        criticalEvents: securityStats.eventsBySeverity.critical || 0,
        blockedIPs: securityStats.blockedIPs,
        failedLogins: securityStats.eventsByType.failed_login || 0,
        suspiciousActivity: securityStats.eventsByType.suspicious_activity || 0,
        resolvedIncidents: securityStats.resolvedEvents,
        activeThreats: securityStats.unresolvedEvents,
        complianceScore: 95, // Mock score
      };
      
      setMetrics(securityMetrics);
      setRecentEvents(events);
      setAuditStats(auditStatistics);
      setComplianceReport(compliance);
    } catch (error) {
      console.error('Error fetching security data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchSecurityData();
    
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(fetchSecurityData, 30000);
    return () => clearInterval(interval);
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchSecurityData();
  };

  const handleExportSecurityReport = () => {
    // In a real implementation, this would generate a comprehensive security report
    const report = {
      generatedAt: new Date().toISOString(),
      metrics,
      recentEvents,
      auditStats,
      complianceReport,
    };
    
    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `security-report-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getEventSeverityColor = (severity: string) => {
    const colors = {
      low: 'text-blue-600 bg-blue-100',
      medium: 'text-yellow-600 bg-yellow-100',
      high: 'text-orange-600 bg-orange-100',
      critical: 'text-red-600 bg-red-100',
    };
    return colors[severity as keyof typeof colors] || colors.medium;
  };

  const getEventTypeIcon = (type: string) => {
    const icons: Record<string, React.ReactNode> = {
      failed_login: <Lock className="h-4 w-4" />,
      suspicious_login: <AlertTriangle className="h-4 w-4" />,
      brute_force_attempt: <Shield className="h-4 w-4" />,
      unauthorized_access: <XCircle className="h-4 w-4" />,
      privilege_escalation: <AlertTriangle className="h-4 w-4" />,
      rate_limit_exceeded: <Activity className="h-4 w-4" />,
      malicious_request: <Globe className="h-4 w-4" />,
    };
    return icons[type] || <AlertTriangle className="h-4 w-4" />;
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  if (loading) {
    return <AdminTableSkeleton rows={4} columns={4} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 font-jost">Security Dashboard</h1>
          <p className="text-gray-600 font-jost">Monitor security events, threats, and compliance status</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <AdminButton 
            variant="outline" 
            onClick={handleRefresh}
            loading={refreshing}
            icon={<RefreshCw className="h-4 w-4" />}
          >
            Refresh
          </AdminButton>
          
          <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.SYSTEM_SETTINGS}>
            <AdminButton 
              variant="outline"
              onClick={handleExportSecurityReport}
              icon={<Download className="h-4 w-4" />}
            >
              Export Report
            </AdminButton>
          </AdminPermissionWrapper>
        </div>
      </div>

      {/* Security Metrics */}
      {metrics && (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          <AdminStatCard
            title="Security Events (24h)"
            value={metrics.totalEvents.toString()}
            change={{ 
              value: metrics.criticalEvents > 0 ? `${metrics.criticalEvents} critical` : 'No critical events',
              type: metrics.criticalEvents > 0 ? 'negative' : 'positive'
            }}
            icon={<Shield className="h-6 w-6 text-gray-600" />}
          />
          
          <AdminStatCard
            title="Blocked IPs"
            value={metrics.blockedIPs.toString()}
            change={{ 
              value: metrics.blockedIPs > 0 ? 'Active blocks' : 'No blocks',
              type: metrics.blockedIPs > 0 ? 'neutral' : 'positive'
            }}
            icon={<Globe className="h-6 w-6 text-gray-600" />}
          />
          
          <AdminStatCard
            title="Failed Logins"
            value={metrics.failedLogins.toString()}
            change={{ 
              value: metrics.failedLogins > 10 ? 'High activity' : 'Normal',
              type: metrics.failedLogins > 10 ? 'negative' : 'positive'
            }}
            icon={<Lock className="h-6 w-6 text-gray-600" />}
          />
          
          <AdminStatCard
            title="Compliance Score"
            value={`${metrics.complianceScore}%`}
            change={{ 
              value: metrics.complianceScore >= 90 ? 'Excellent' : 'Needs attention',
              type: metrics.complianceScore >= 90 ? 'positive' : 'negative'
            }}
            icon={<CheckCircle className="h-6 w-6 text-gray-600" />}
          />
        </div>
      )}

      {/* Security Status Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Threat Status */}
        <AdminCard>
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 font-jost">Threat Status</h3>
            <AlertTriangle className="h-5 w-5 text-gray-400" />
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <div>
                  <h4 className="font-medium text-green-800 font-jost">System Secure</h4>
                  <p className="text-sm text-green-700 font-jost">No active threats detected</p>
                </div>
              </div>
              <div className="text-2xl font-bold text-green-600 font-jost">
                {metrics?.activeThreats || 0}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-900 font-jost">{metrics?.resolvedIncidents || 0}</div>
                <div className="text-sm text-gray-600 font-jost">Resolved Incidents</div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-900 font-jost">{metrics?.suspiciousActivity || 0}</div>
                <div className="text-sm text-gray-600 font-jost">Suspicious Activities</div>
              </div>
            </div>
          </div>
        </AdminCard>

        {/* Compliance Status */}
        <AdminCard>
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 font-jost">Compliance Status</h3>
            <FileText className="h-5 w-5 text-gray-400" />
          </div>

          {complianceReport && (
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div>
                  <h4 className="font-medium text-blue-800 font-jost">Data Subjects</h4>
                  <p className="text-sm text-blue-700 font-jost">Total registered users</p>
                </div>
                <div className="text-2xl font-bold text-blue-600 font-jost">
                  {complianceReport.totalDataSubjects}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900 font-jost">{complianceReport.pendingRequests}</div>
                  <div className="text-sm text-gray-600 font-jost">Pending Requests</div>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900 font-jost">{complianceReport.completedRequests}</div>
                  <div className="text-sm text-gray-600 font-jost">Completed Requests</div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm font-jost">
                  <span>Marketing Consent</span>
                  <span>{complianceReport.consentRates?.marketing?.toFixed(1) || 0}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full" 
                    style={{ width: `${complianceReport.consentRates?.marketing || 0}%` }}
                  ></div>
                </div>
              </div>
            </div>
          )}
        </AdminCard>
      </div>

      {/* Recent Security Events */}
      <AdminCard>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-gray-900 font-jost">Recent Security Events</h3>
          <Eye className="h-5 w-5 text-gray-400" />
        </div>

        {recentEvents.length > 0 ? (
          <div className="space-y-4">
            {recentEvents.slice(0, 5).map((event, index) => (
              <div key={index} className="flex items-start justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-start space-x-3">
                  <div className={`p-2 rounded-lg ${getEventSeverityColor(event.severity)}`}>
                    {getEventTypeIcon(event.type)}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 font-jost">
                      {event.type.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                    </h4>
                    <p className="text-sm text-gray-600 font-jost">
                      IP: {event.ipAddress} • {formatTimestamp(event.timestamp)}
                    </p>
                    {event.details && Object.keys(event.details).length > 0 && (
                      <p className="text-sm text-gray-500 font-jost mt-1">
                        {JSON.stringify(event.details)}
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getEventSeverityColor(event.severity)}`}>
                    {event.severity}
                  </span>
                  {event.resolved ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <Clock className="h-4 w-4 text-yellow-600" />
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 font-jost">No recent security events</p>
          </div>
        )}
      </AdminCard>

      {/* Audit Activity Summary */}
      {auditStats && (
        <AdminCard>
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 font-jost">Audit Activity (24h)</h3>
            <Activity className="h-5 w-5 text-gray-400" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900 font-jost">{auditStats.totalLogs}</div>
              <div className="text-sm text-gray-600 font-jost">Total Audit Logs</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 font-jost">{auditStats.successRate.toFixed(1)}%</div>
              <div className="text-sm text-gray-600 font-jost">Success Rate</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 font-jost">{auditStats.topUsers.length}</div>
              <div className="text-sm text-gray-600 font-jost">Active Users</div>
            </div>
          </div>

          {auditStats.topUsers.length > 0 && (
            <div className="mt-6">
              <h4 className="font-medium text-gray-900 font-jost mb-3">Most Active Users</h4>
              <div className="space-y-2">
                {auditStats.topUsers.slice(0, 3).map((user: any, index: number) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm font-jost">{user.userEmail}</span>
                    <span className="text-sm text-gray-600 font-jost">{user.count} actions</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </AdminCard>
      )}
    </div>
  );
}

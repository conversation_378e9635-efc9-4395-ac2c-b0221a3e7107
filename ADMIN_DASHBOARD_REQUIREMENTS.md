# iREME Soft Hub - Admin Dashboard Requirements

## 📋 Overview

This document outlines the comprehensive requirements for building an admin dashboard for iREME Soft Hub, a software development company offering web development, mobile development, UI/UX design, e-commerce solutions, cloud solutions, and digital marketing services.

## 🏗️ Current Infrastructure Analysis

### Tech Stack
- **Frontend**: Next.js 14+ with Type<PERSON>, Tailwind CSS
- **Backend**: Supabase (PostgreSQL database, Authentication, Storage)
- **UI Components**: Headless UI, Lucide React icons
- **Forms**: React Hook Form with Zod validation
- **Animation**: Framer Motion

### Existing Database Tables
- `projects` - Collaboration projects
- `job_postings` - Available job positions
- `project_applications` - Project application submissions
- `job_applications` - Job application submissions

### Current Features
- Public website with service pages
- Collaboration system for developers
- Template showcase
- Quote request system
- Contact forms
- Blog system

## 🎯 Admin Dashboard Core Requirements

### 1. Authentication & Authorization

#### Features Needed:
- **Admin Login/Logout System**
  - Secure authentication with <PERSON>pabase Auth
  - Password reset functionality
  - Two-factor authentication (optional)

- **Role-Based Access Control**
  - Super Admin (full access)
  - Admin (most features)
  - Manager (project management)
  - Developer (limited project access)
  - Support (customer communication)

- **User Management**
  - Add/edit/delete admin users
  - Assign roles and permissions
  - User activity tracking
  - Session management

#### Database Tables Needed:
```sql
- admin_users (id, email, role, created_at, last_login)
- roles (id, name, permissions)
- user_sessions (id, user_id, token, expires_at)
```

### 2. Main Dashboard Overview

#### Key Metrics Widgets:
- **Financial Metrics**
  - Monthly recurring revenue (MRR)
  - Total revenue (current month/year)
  - Outstanding invoices
  - Payment status overview

- **Project Metrics**
  - Active projects count
  - Projects by status (planning, development, testing, completed)
  - Project timeline overview
  - Resource utilization

- **Lead Metrics**
  - New leads this month
  - Lead conversion rate
  - Quote requests pending
  - Follow-up reminders

- **Collaboration Metrics**
  - Active collaboration projects
  - Pending applications
  - Collaborator performance

#### Visual Components:
- Revenue charts (line/bar charts)
- Project status pie charts
- Lead funnel visualization
- Recent activity timeline
- Quick action buttons
- Alert notifications

### 3. Customer & Lead Management

#### Lead Management:
- **Quote Requests Dashboard**
  - View all quote requests from `/get-quote` page
  - Lead scoring and qualification
  - Assign leads to team members
  - Follow-up scheduling and reminders
  - Lead status tracking (new, contacted, qualified, converted, lost)

- **Contact Form Submissions**
  - Manage submissions from contact forms
  - Response tracking
  - Integration with email system

#### Customer Management:
- **Client Database**
  - Complete client profiles
  - Project history
  - Communication logs
  - Contract and billing information
  - Client satisfaction ratings

#### Database Tables Needed:
```sql
- leads (id, name, email, phone, service_interest, status, source, created_at)
- clients (id, company_name, contact_person, email, phone, address, created_at)
- communications (id, client_id, type, subject, content, created_at)
- follow_ups (id, lead_id, scheduled_date, completed, notes)
```

### 4. Project Management System

#### Project Creation & Setup:
- Create new projects from quotes
- Define project scope and requirements
- Set timelines and milestones
- Assign team members
- Set project budget and pricing

#### Project Tracking:
- **Timeline Management**
  - Gantt chart view
  - Milestone tracking
  - Deadline management
  - Progress visualization

- **Task Management**
  - Create and assign tasks
  - Task dependencies
  - Time tracking
  - Task status updates

- **Resource Management**
  - Team member allocation
  - Workload balancing
  - Skill matching
  - Availability tracking

#### Client Communication:
- Project updates portal
- File sharing system
- Feedback collection
- Approval workflows

#### Database Tables Needed:
```sql
- projects_internal (id, client_id, name, description, status, budget, start_date, end_date)
- project_milestones (id, project_id, name, due_date, status, completion_percentage)
- tasks (id, project_id, assignee_id, title, description, status, due_date, time_spent)
- project_files (id, project_id, file_name, file_path, uploaded_by, created_at)
- project_team (id, project_id, user_id, role, assigned_date)
```

### 5. Collaboration System Management

#### Project Management:
- **Collaboration Projects CRUD**
  - Create/edit/delete collaboration projects
  - Set project requirements and skills
  - Manage application deadlines
  - Project status management

- **Application Review System**
  - Review project applications
  - Applicant scoring system
  - Communication with applicants
  - Selection and rejection workflows

#### Job Management:
- **Job Postings Management**
  - Create/edit/delete job postings
  - Set requirements and compensation
  - Application deadline management

- **Job Application Review**
  - Review job applications
  - Resume/portfolio viewing
  - Interview scheduling
  - Hiring workflow management

#### Collaborator Management:
- Collaborator profiles and performance
- Project assignment tracking
- Payment management for collaborators
- Performance reviews and ratings

### 6. Financial Management

#### Invoice Management:
- **Invoice Generation**
  - Automated invoice creation from projects
  - Custom invoice templates
  - Tax calculation
  - Multi-currency support

- **Payment Tracking**
  - Payment status monitoring
  - Payment reminders
  - Overdue invoice management
  - Payment method tracking

#### Financial Reporting:
- Revenue analytics and forecasting
- Profit/loss statements
- Cash flow analysis
- Service profitability analysis
- Tax reporting

#### Database Tables Needed:
```sql
- invoices (id, client_id, project_id, amount, tax, status, due_date, created_at)
- payments (id, invoice_id, amount, payment_method, transaction_id, paid_at)
- expenses (id, category, amount, description, date, project_id)
- financial_reports (id, type, period, data, generated_at)
```

### 7. Content Management System

#### Website Content:
- **Service Pages Management**
  - Edit service descriptions and pricing
  - Update package details
  - Manage FAQs
  - Feature list management

- **Template Showcase**
  - Add/edit/delete templates
  - Upload template images
  - Manage demo links
  - Category management

- **Blog Management**
  - Create/edit/delete blog posts
  - SEO optimization tools
  - Publishing schedule
  - Comment management

#### SEO Management:
- Meta tags management
- Sitemap generation
- Analytics integration
- Performance monitoring

### 8. Analytics & Reporting

#### Website Analytics:
- Traffic analysis
- Lead source tracking
- Conversion rate monitoring
- User behavior analysis

#### Business Analytics:
- Service performance metrics
- Client satisfaction scores
- Team productivity metrics
- Financial KPIs

#### Custom Reports:
- Report builder interface
- Scheduled report generation
- Export functionality (PDF, CSV, Excel)
- Data visualization tools

### 9. Communication Tools

#### Email Management:
- **Email Templates**
  - Create/edit email templates
  - Template categories (quotes, follow-ups, project updates)
  - Variable insertion system

- **Automated Sequences**
  - Lead nurturing sequences
  - Project update automation
  - Payment reminder automation

#### Internal Communication:
- Team messaging system
- Project-specific communication channels
- Notification management
- Announcement system

### 10. System Administration

#### Database Management:
- Data backup and restore
- Database optimization
- Data export/import tools
- Data integrity monitoring

#### Security Management:
- Access log monitoring
- Security audit trails
- Permission management
- API key management

#### System Settings:
- Company information management
- Email configuration
- Payment gateway settings
- Integration settings

## 🎨 UI/UX Components Required

### Core Components:
- **Data Tables**
  - Sortable columns
  - Advanced filtering
  - Pagination
  - Bulk actions
  - Export functionality

- **Charts & Graphs**
  - Line charts (revenue trends)
  - Bar charts (project status)
  - Pie charts (service distribution)
  - Donut charts (lead sources)
  - Area charts (growth metrics)

- **Forms**
  - Dynamic form builder
  - Validation system
  - File upload components
  - Date/time pickers
  - Rich text editors

- **Navigation**
  - Collapsible sidebar
  - Breadcrumb navigation
  - Tab navigation
  - Search functionality
  - Quick actions menu

- **Modals & Overlays**
  - Confirmation dialogs
  - Detail view modals
  - Form modals
  - Image galleries
  - Loading overlays

### Advanced Components:
- **Calendar/Scheduling**
  - Project timeline view
  - Meeting scheduler
  - Deadline calendar
  - Resource availability

- **File Management**
  - File upload/download
  - Image preview
  - Document viewer
  - Version control

- **Notification System**
  - Real-time notifications
  - Email notifications
  - Push notifications
  - Notification preferences

## 🔧 Technical Implementation

### Frontend Architecture:
- **Pages Structure**
  ```
  /admin
    /dashboard          # Main overview
    /leads             # Lead management
    /clients           # Client management
    /projects          # Project management
    /collaboration     # Collaboration system
    /finances          # Financial management
    /content           # Content management
    /analytics         # Reports and analytics
    /communications    # Email and messaging
    /settings          # System settings
    /users             # User management
  ```

- **Component Library**
  - Extend existing UI components
  - Create admin-specific components
  - Implement data visualization components
  - Build form components with validation

### Backend Requirements:
- **API Routes**
  - RESTful API for all CRUD operations
  - Authentication middleware
  - Rate limiting
  - Error handling
  - Data validation

- **Database Schema**
  - Extend existing Supabase schema
  - Implement proper relationships
  - Add indexes for performance
  - Set up Row Level Security (RLS)

### Integration Requirements:
- **Email Service** (SendGrid/Mailgun)
- **Payment Gateway** (Stripe/PayPal)
- **File Storage** (Supabase Storage)
- **Analytics** (Google Analytics)
- **Calendar** (Google Calendar API)
- **Communication** (Slack/Discord webhooks)

## 📊 Database Schema Extensions

### New Tables Required:
```sql
-- Admin Users and Roles
CREATE TABLE admin_users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  role VARCHAR(50) NOT NULL,
  first_name VARCHAR(100),
  last_name VARCHAR(100),
  avatar_url TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  last_login TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Leads from quote requests and contact forms
CREATE TABLE leads (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  email VARCHAR(255) NOT NULL,
  phone VARCHAR(20),
  company VARCHAR(255),
  service_interest VARCHAR(100),
  budget_range VARCHAR(50),
  project_description TEXT,
  status VARCHAR(50) DEFAULT 'new',
  source VARCHAR(50),
  assigned_to UUID REFERENCES admin_users(id),
  lead_score INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Clients (converted leads)
CREATE TABLE clients (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  lead_id UUID REFERENCES leads(id),
  company_name VARCHAR(255) NOT NULL,
  contact_person VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  phone VARCHAR(20),
  address TEXT,
  website VARCHAR(255),
  industry VARCHAR(100),
  company_size VARCHAR(50),
  notes TEXT,
  status VARCHAR(50) DEFAULT 'active',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Internal projects (different from collaboration projects)
CREATE TABLE projects_internal (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  client_id UUID REFERENCES clients(id),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  service_type VARCHAR(100) NOT NULL,
  status VARCHAR(50) DEFAULT 'planning',
  budget DECIMAL(10,2),
  estimated_hours INTEGER,
  actual_hours INTEGER,
  start_date DATE,
  end_date DATE,
  completion_percentage INTEGER DEFAULT 0,
  project_manager_id UUID REFERENCES admin_users(id),
  created_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Project milestones
CREATE TABLE project_milestones (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID REFERENCES projects_internal(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  due_date DATE,
  status VARCHAR(50) DEFAULT 'pending',
  completion_percentage INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tasks within projects
CREATE TABLE tasks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID REFERENCES projects_internal(id) ON DELETE CASCADE,
  milestone_id UUID REFERENCES project_milestones(id),
  assignee_id UUID REFERENCES admin_users(id),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  status VARCHAR(50) DEFAULT 'todo',
  priority VARCHAR(20) DEFAULT 'medium',
  estimated_hours INTEGER,
  actual_hours INTEGER,
  due_date DATE,
  completed_at TIMESTAMP WITH TIME ZONE,
  created_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Communications with clients
CREATE TABLE communications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  client_id UUID REFERENCES clients(id),
  project_id UUID REFERENCES projects_internal(id),
  type VARCHAR(50) NOT NULL, -- email, call, meeting, note
  subject VARCHAR(255),
  content TEXT,
  direction VARCHAR(20), -- inbound, outbound
  created_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Invoices
CREATE TABLE invoices (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  invoice_number VARCHAR(50) UNIQUE NOT NULL,
  client_id UUID REFERENCES clients(id),
  project_id UUID REFERENCES projects_internal(id),
  amount DECIMAL(10,2) NOT NULL,
  tax_amount DECIMAL(10,2) DEFAULT 0,
  total_amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'USD',
  status VARCHAR(50) DEFAULT 'draft',
  due_date DATE,
  paid_date DATE,
  notes TEXT,
  created_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Payments
CREATE TABLE payments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  invoice_id UUID REFERENCES invoices(id),
  amount DECIMAL(10,2) NOT NULL,
  payment_method VARCHAR(50),
  transaction_id VARCHAR(255),
  payment_date DATE NOT NULL,
  notes TEXT,
  created_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email templates
CREATE TABLE email_templates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  subject VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  category VARCHAR(100),
  variables JSONB, -- Available variables for the template
  is_active BOOLEAN DEFAULT TRUE,
  created_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- System settings
CREATE TABLE system_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  key VARCHAR(255) UNIQUE NOT NULL,
  value TEXT,
  description TEXT,
  category VARCHAR(100),
  updated_by UUID REFERENCES admin_users(id),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Audit logs
CREATE TABLE audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES admin_users(id),
  action VARCHAR(100) NOT NULL,
  table_name VARCHAR(100),
  record_id UUID,
  old_values JSONB,
  new_values JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🚀 Implementation Phases

### Phase 1: Foundation (Weeks 1-2)
- Set up admin authentication system
- Create basic dashboard layout
- Implement user management
- Set up database schema

### Phase 2: Core Features (Weeks 3-6)
- Lead and customer management
- Basic project management
- Invoice and payment system
- Email template system

### Phase 3: Advanced Features (Weeks 7-10)
- Advanced project management with tasks
- Collaboration system management
- Analytics and reporting
- Content management system

### Phase 4: Integration & Polish (Weeks 11-12)
- Third-party integrations
- Advanced analytics
- Performance optimization
- Testing and bug fixes

## 📈 Success Metrics

### Key Performance Indicators:
- **Operational Efficiency**
  - Reduce lead response time by 50%
  - Increase project completion rate by 25%
  - Improve invoice collection time by 30%

- **Business Growth**
  - Track lead conversion rates
  - Monitor customer satisfaction scores
  - Measure revenue growth
  - Analyze service profitability

- **Team Productivity**
  - Project delivery timelines
  - Resource utilization rates
  - Task completion rates
  - Communication efficiency

## 🔒 Security Considerations

### Data Protection:
- Implement proper authentication and authorization
- Use Row Level Security (RLS) in Supabase
- Encrypt sensitive data
- Regular security audits
- GDPR compliance for client data

### Access Control:
- Role-based permissions
- API rate limiting
- Session management
- Audit logging
- Secure file uploads

## 📞 Support & Maintenance

### Ongoing Requirements:
- Regular database backups
- Performance monitoring
- Security updates
- Feature enhancements
- User training and documentation
- Technical support

---

This comprehensive admin dashboard will transform iREME Soft Hub's operations by providing centralized management of all business processes, from lead generation to project delivery and financial management. The system will scale with the business and provide valuable insights for strategic decision-making.

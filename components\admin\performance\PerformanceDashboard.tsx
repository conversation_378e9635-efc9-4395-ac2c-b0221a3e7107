'use client';

import React, { useState, useEffect } from 'react';
import { AdminCard, AdminStatCard } from '@/components/admin/ui/AdminCard';
import AdminButton from '@/components/admin/ui/AdminButton';
import { AdminTableSkeleton } from '@/components/admin/ui/AdminLoading';
import { useAdminAuth, ADMIN_PERMISSIONS } from '@/lib/auth/admin-auth';
import { AdminPermissionWrapper } from '@/components/admin/auth/AdminProtectedRoute';
import { performanceMonitor } from '@/lib/performance/monitoring';
import { adminCache, dataCache, queryCache } from '@/lib/cache/cache-manager';
import {
  Activity,
  Clock,
  Zap,
  Database,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Server,
  Globe,
  Users,
  Eye,
} from 'lucide-react';

interface PerformanceMetrics {
  pageLoadTime: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  cumulativeLayoutShift: number;
  firstInputDelay: number;
  timeToInteractive: number;
  memoryUsage: number;
  cacheHitRate: number;
  apiResponseTime: number;
  errorRate: number;
}

interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  uptime: number;
  responseTime: number;
  errorCount: number;
  activeUsers: number;
  memoryUsage: number;
  cacheSize: number;
}

export default function PerformanceDashboard() {
  const { hasPermission } = useAdminAuth();
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch performance data
  const fetchPerformanceData = async () => {
    try {
      setLoading(true);
      
      // Get performance summary from monitor
      const performanceSummary = performanceMonitor.getPerformanceSummary();
      
      // Get cache statistics
      const adminCacheStats = adminCache.getStats();
      const dataCacheStats = dataCache.getStats();
      const queryCacheStats = queryCache.getStats();
      
      // Calculate overall cache hit rate
      const totalHits = adminCacheStats.hits + dataCacheStats.hits + queryCacheStats.hits;
      const totalRequests = totalHits + adminCacheStats.misses + dataCacheStats.misses + queryCacheStats.misses;
      const overallHitRate = totalRequests > 0 ? (totalHits / totalRequests) * 100 : 0;
      
      // Mock performance metrics (in a real app, these would come from your monitoring service)
      const mockMetrics: PerformanceMetrics = {
        pageLoadTime: performanceSummary.page_loadTime?.avg || 1250,
        firstContentfulPaint: performanceSummary.first_contentful_paint?.avg || 800,
        largestContentfulPaint: performanceSummary.largest_contentful_paint?.avg || 1200,
        cumulativeLayoutShift: performanceSummary.cumulative_layout_shift?.avg || 0.05,
        firstInputDelay: performanceSummary.first_input_delay?.avg || 45,
        timeToInteractive: performanceSummary.page_timeToInteractive?.avg || 1800,
        memoryUsage: (performance as any).memory?.usedJSHeapSize / 1024 / 1024 || 25.6,
        cacheHitRate: overallHitRate,
        apiResponseTime: performanceSummary.api_call?.avg || 320,
        errorRate: 0.2,
      };
      
      const mockSystemHealth: SystemHealth = {
        status: 'healthy',
        uptime: 99.9,
        responseTime: mockMetrics.apiResponseTime,
        errorCount: 3,
        activeUsers: 127,
        memoryUsage: mockMetrics.memoryUsage,
        cacheSize: adminCacheStats.size + dataCacheStats.size + queryCacheStats.size,
      };
      
      setMetrics(mockMetrics);
      setSystemHealth(mockSystemHealth);
    } catch (error) {
      console.error('Error fetching performance data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchPerformanceData();
    
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(fetchPerformanceData, 30000);
    return () => clearInterval(interval);
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchPerformanceData();
  };

  const handleClearCache = () => {
    adminCache.clear();
    dataCache.clear();
    queryCache.clear();
    fetchPerformanceData();
  };

  const getHealthStatus = (status: string) => {
    const statusConfig = {
      healthy: { color: 'text-green-600', bg: 'bg-green-100', icon: <CheckCircle className="h-5 w-5" /> },
      warning: { color: 'text-yellow-600', bg: 'bg-yellow-100', icon: <AlertTriangle className="h-5 w-5" /> },
      critical: { color: 'text-red-600', bg: 'bg-red-100', icon: <AlertTriangle className="h-5 w-5" /> },
    };
    return statusConfig[status as keyof typeof statusConfig] || statusConfig.healthy;
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const getPerformanceGrade = (metric: string, value: number) => {
    const thresholds: Record<string, { good: number; needs_improvement: number }> = {
      pageLoadTime: { good: 1000, needs_improvement: 2500 },
      firstContentfulPaint: { good: 1000, needs_improvement: 1800 },
      largestContentfulPaint: { good: 2500, needs_improvement: 4000 },
      firstInputDelay: { good: 100, needs_improvement: 300 },
      timeToInteractive: { good: 3800, needs_improvement: 7300 },
      apiResponseTime: { good: 200, needs_improvement: 500 },
    };

    const threshold = thresholds[metric];
    if (!threshold) return 'good';

    if (value <= threshold.good) return 'good';
    if (value <= threshold.needs_improvement) return 'needs_improvement';
    return 'poor';
  };

  const getGradeColor = (grade: string) => {
    const colors = {
      good: 'text-green-600',
      needs_improvement: 'text-yellow-600',
      poor: 'text-red-600',
    };
    return colors[grade as keyof typeof colors] || colors.good;
  };

  if (loading) {
    return <AdminTableSkeleton rows={4} columns={4} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 font-jost">Performance Dashboard</h1>
          <p className="text-gray-600 font-jost">Monitor system performance and optimization metrics</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <AdminButton 
            variant="outline" 
            onClick={handleRefresh}
            loading={refreshing}
            icon={<RefreshCw className="h-4 w-4" />}
          >
            Refresh
          </AdminButton>
          
          <AdminPermissionWrapper permission={ADMIN_PERMISSIONS.SYSTEM_SETTINGS}>
            <AdminButton 
              variant="outline"
              onClick={handleClearCache}
              icon={<Database className="h-4 w-4" />}
            >
              Clear Cache
            </AdminButton>
          </AdminPermissionWrapper>
        </div>
      </div>

      {/* System Health Overview */}
      {systemHealth && (
        <AdminCard>
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 font-jost">System Health</h3>
            <div className="flex items-center space-x-2">
              {getHealthStatus(systemHealth.status).icon}
              <span className={`font-medium ${getHealthStatus(systemHealth.status).color}`}>
                {systemHealth.status.charAt(0).toUpperCase() + systemHealth.status.slice(1)}
              </span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Server className="h-8 w-8 text-gray-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 font-jost">{systemHealth.uptime}%</div>
              <div className="text-sm text-gray-600 font-jost">Uptime</div>
            </div>
            
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Clock className="h-8 w-8 text-gray-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 font-jost">{formatDuration(systemHealth.responseTime)}</div>
              <div className="text-sm text-gray-600 font-jost">Response Time</div>
            </div>
            
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Users className="h-8 w-8 text-gray-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 font-jost">{systemHealth.activeUsers}</div>
              <div className="text-sm text-gray-600 font-jost">Active Users</div>
            </div>
            
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Activity className="h-8 w-8 text-gray-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 font-jost">{systemHealth.errorCount}</div>
              <div className="text-sm text-gray-600 font-jost">Errors (24h)</div>
            </div>
          </div>
        </AdminCard>
      )}

      {/* Core Web Vitals */}
      {metrics && (
        <AdminCard>
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 font-jost">Core Web Vitals</h3>
            <Globe className="h-5 w-5 text-gray-400" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="mb-2">
                <div className={`text-3xl font-bold ${getGradeColor(getPerformanceGrade('largestContentfulPaint', metrics.largestContentfulPaint))}`}>
                  {formatDuration(metrics.largestContentfulPaint)}
                </div>
                <div className="text-sm text-gray-600 font-jost">Largest Contentful Paint</div>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full ${
                    getPerformanceGrade('largestContentfulPaint', metrics.largestContentfulPaint) === 'good' ? 'bg-green-500' :
                    getPerformanceGrade('largestContentfulPaint', metrics.largestContentfulPaint) === 'needs_improvement' ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${Math.min((2500 / metrics.largestContentfulPaint) * 100, 100)}%` }}
                ></div>
              </div>
            </div>

            <div className="text-center">
              <div className="mb-2">
                <div className={`text-3xl font-bold ${getGradeColor(getPerformanceGrade('firstInputDelay', metrics.firstInputDelay))}`}>
                  {formatDuration(metrics.firstInputDelay)}
                </div>
                <div className="text-sm text-gray-600 font-jost">First Input Delay</div>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full ${
                    getPerformanceGrade('firstInputDelay', metrics.firstInputDelay) === 'good' ? 'bg-green-500' :
                    getPerformanceGrade('firstInputDelay', metrics.firstInputDelay) === 'needs_improvement' ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${Math.min((100 / metrics.firstInputDelay) * 100, 100)}%` }}
                ></div>
              </div>
            </div>

            <div className="text-center">
              <div className="mb-2">
                <div className={`text-3xl font-bold ${metrics.cumulativeLayoutShift < 0.1 ? 'text-green-600' : metrics.cumulativeLayoutShift < 0.25 ? 'text-yellow-600' : 'text-red-600'}`}>
                  {metrics.cumulativeLayoutShift.toFixed(3)}
                </div>
                <div className="text-sm text-gray-600 font-jost">Cumulative Layout Shift</div>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full ${
                    metrics.cumulativeLayoutShift < 0.1 ? 'bg-green-500' :
                    metrics.cumulativeLayoutShift < 0.25 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${Math.min((0.1 / metrics.cumulativeLayoutShift) * 100, 100)}%` }}
                ></div>
              </div>
            </div>
          </div>
        </AdminCard>
      )}

      {/* Performance Metrics */}
      {metrics && (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          <AdminStatCard
            title="Page Load Time"
            value={formatDuration(metrics.pageLoadTime)}
            change={{ 
              value: getPerformanceGrade('pageLoadTime', metrics.pageLoadTime) === 'good' ? 'Good' : 'Needs Work',
              type: getPerformanceGrade('pageLoadTime', metrics.pageLoadTime) === 'good' ? 'positive' : 'negative'
            }}
            icon={<Clock className="h-6 w-6 text-gray-600" />}
          />
          
          <AdminStatCard
            title="Cache Hit Rate"
            value={`${metrics.cacheHitRate.toFixed(1)}%`}
            change={{ 
              value: metrics.cacheHitRate > 80 ? 'Excellent' : 'Good',
              type: metrics.cacheHitRate > 80 ? 'positive' : 'neutral'
            }}
            icon={<Database className="h-6 w-6 text-gray-600" />}
          />
          
          <AdminStatCard
            title="API Response Time"
            value={formatDuration(metrics.apiResponseTime)}
            change={{ 
              value: getPerformanceGrade('apiResponseTime', metrics.apiResponseTime) === 'good' ? 'Fast' : 'Slow',
              type: getPerformanceGrade('apiResponseTime', metrics.apiResponseTime) === 'good' ? 'positive' : 'negative'
            }}
            icon={<Zap className="h-6 w-6 text-gray-600" />}
          />
          
          <AdminStatCard
            title="Memory Usage"
            value={`${metrics.memoryUsage.toFixed(1)} MB`}
            change={{ 
              value: metrics.memoryUsage < 50 ? 'Normal' : 'High',
              type: metrics.memoryUsage < 50 ? 'positive' : 'negative'
            }}
            icon={<Activity className="h-6 w-6 text-gray-600" />}
          />
        </div>
      )}

      {/* Performance Recommendations */}
      <AdminCard>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-gray-900 font-jost">Performance Recommendations</h3>
          <TrendingUp className="h-5 w-5 text-gray-400" />
        </div>

        <div className="space-y-4">
          {metrics && metrics.pageLoadTime > 2000 && (
            <div className="flex items-start space-x-3 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-yellow-800 font-jost">Slow Page Load Time</h4>
                <p className="text-sm text-yellow-700 font-jost">
                  Page load time is {formatDuration(metrics.pageLoadTime)}. Consider optimizing images, enabling compression, and reducing JavaScript bundle size.
                </p>
              </div>
            </div>
          )}

          {metrics && metrics.cacheHitRate < 70 && (
            <div className="flex items-start space-x-3 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <Database className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-blue-800 font-jost">Low Cache Hit Rate</h4>
                <p className="text-sm text-blue-700 font-jost">
                  Cache hit rate is {metrics.cacheHitRate.toFixed(1)}%. Review caching strategies and increase cache TTL for frequently accessed data.
                </p>
              </div>
            </div>
          )}

          {metrics && metrics.memoryUsage > 50 && (
            <div className="flex items-start space-x-3 p-4 bg-red-50 border border-red-200 rounded-lg">
              <Activity className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-red-800 font-jost">High Memory Usage</h4>
                <p className="text-sm text-red-700 font-jost">
                  Memory usage is {metrics.memoryUsage.toFixed(1)} MB. Consider implementing memory optimization techniques and clearing unused objects.
                </p>
              </div>
            </div>
          )}

          {(!metrics || (metrics.pageLoadTime <= 2000 && metrics.cacheHitRate >= 70 && metrics.memoryUsage <= 50)) && (
            <div className="flex items-start space-x-3 p-4 bg-green-50 border border-green-200 rounded-lg">
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-green-800 font-jost">Performance is Good</h4>
                <p className="text-sm text-green-700 font-jost">
                  All performance metrics are within acceptable ranges. Continue monitoring for any degradation.
                </p>
              </div>
            </div>
          )}
        </div>
      </AdminCard>
    </div>
  );
}

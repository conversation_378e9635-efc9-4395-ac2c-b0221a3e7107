'use client';

import React, { useState } from 'react';
import { Clock, Users, Star, ArrowRight, Code2 } from 'lucide-react';

interface Project {
  id: string;
  title: string;
  description: string;
  previewDescription?: string;
  technologies: string[];
  duration: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  requiredSkills: string[];
  isActive: boolean;
  applicationDeadline?: string;
  maxCollaborators?: number;
}

interface ProjectCardProps {
  project: Project;
  className?: string;
}

const ProjectCard: React.FC<ProjectCardProps> = ({ project, className = '' }) => {
  const [isHovered, setIsHovered] = useState(false);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Intermediate':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Advanced':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleApplyClick = () => {
    // Navigate to application form with project ID
    window.location.href = `/collaborate/apply/project?id=${project.id}`;
  };

  return (
    <div 
      className={`group relative bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-500 overflow-hidden hover:-translate-y-2 ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Header Section */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="text-lg font-bold text-black mb-2 font-jost group-hover:text-gray-800 transition-colors line-clamp-1">
              {project.title}
            </h3>
            <div className="flex items-center gap-3 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <Clock size={14} />
                <span className="font-jost text-xs">{project.duration}</span>
              </div>
              <div className={`px-2 py-1 rounded-full text-xs font-medium border ${getDifficultyColor(project.difficulty)} font-jost`}>
                {project.difficulty}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-1 text-yellow-500">
            <Star size={14} fill="currentColor" />
          </div>
        </div>

        {/* Technology Tags - Show only first 3 */}
        <div className="flex flex-wrap gap-1">
          {project.technologies.slice(0, 3).map((tech, index) => (
            <span
              key={index}
              className="inline-flex items-center gap-1 px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs font-medium hover:bg-gray-200 transition-colors font-jost"
            >
              <Code2 size={12} />
              {tech}
            </span>
          ))}
          {project.technologies.length > 3 && (
            <span className="px-2 py-1 text-gray-500 text-xs font-jost">
              +{project.technologies.length - 3}
            </span>
          )}
        </div>
      </div>

      {/* Content Section */}
      <div className="p-4">
        {/* Description */}
        <p className="text-gray-600 mb-4 leading-relaxed font-jost text-sm line-clamp-2">
          {project.previewDescription || project.description}
        </p>

        {/* Required Skills - Show only first 3 */}
        <div className="mb-4">
          <div className="flex flex-wrap gap-1">
            {project.requiredSkills.slice(0, 3).map((skill, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-blue-50 text-blue-700 rounded text-xs font-medium border border-blue-200 font-jost"
              >
                {skill}
              </span>
            ))}
            {project.requiredSkills.length > 3 && (
              <span className="px-2 py-1 text-gray-500 text-xs font-jost">
                +{project.requiredSkills.length - 3} more
              </span>
            )}
          </div>
        </div>

        {/* Project Stats - Compact */}
        <div className="flex items-center justify-between mb-4 text-xs text-gray-600">
          <div className="flex items-center gap-1">
            <Users size={14} className="text-green-500" />
            <span className="font-jost">{project.maxCollaborators || '5'} max</span>
          </div>
          <div className="font-jost">
            {project.applicationDeadline ?
              new Date(project.applicationDeadline).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }) :
              'Open'
            }
          </div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-green-400 rounded-full"></div>
            <span className="font-jost">3 joined</span>
          </div>
        </div>

        {/* Apply Button */}
        <button
          onClick={handleApplyClick}
          className="w-full bg-black text-white py-2.5 px-4 rounded-lg hover:bg-gray-800 transition-all duration-300 flex items-center justify-center gap-2 font-medium group/btn hover:shadow-lg transform hover:-translate-y-0.5 font-jost text-sm"
        >
          <span className="transition-transform duration-300 group-hover/btn:translate-x-[-4px]">
            Apply to Join
          </span>
          <ArrowRight
            size={16}
            className={`transition-all duration-300 ${
              isHovered ? 'translate-x-1 scale-110' : ''
            }`}
          />
        </button>
      </div>

      {/* Hover Overlay Effect */}
      <div className={`absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none rounded-2xl`} />
      
      {/* Status Indicator */}
      {project.isActive && (
        <div className="absolute top-4 right-4">
          <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
        </div>
      )}
    </div>
  );
};

export default ProjectCard;
